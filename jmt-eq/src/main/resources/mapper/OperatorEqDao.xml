<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace名字必须与Mapper接口名一致 -->

<mapper namespace="com.jmt.dao.OperatorEqDao">
    <update id="transferOperatorStock">
        UPDATE eq_operator_stock AS target
            INNER JOIN (
            SELECT
            operatorNo,
            eqStockTotal,
            eqANum,
            eqBNum,
            eqCNum,
            onlineNum,
            offlineNum
            FROM eq_operator_stock
            WHERE operatorNo = #{sourceOperatorNo}
            AND isDelete = 0
            ) AS source ON 1=1
            SET
                target.eqStockTotal = target.eqStockTotal + source.eqStockTotal,
                target.eqANum = target.eqANum + source.eqANum,
                target.eqBNum = target.eqBNum + source.eqBNum,
                target.eqCNum = target.eqCNum + source.eqCNum,
                target.onlineNum = target.onlineNum + source.onlineNum,
                target.offlineNum = target.offlineNum + source.offlineNum
        WHERE
            target.operatorNo = #{targetOperatorNo}
          AND target.isDelete = 0;
    </update>

    <update id="update">
        UPDATE eq_operator_stock
        <set>
            <if test="eqStockTotal != null">
                eqStockTotal = #{eqStockTotal},
            </if>
            <if test="eqANum != null">
                eqANum = #{eqANum},
            </if>
            <if test="eqBNum != null">
                eqBNum = #{eqBNum},
            </if>
            <if test="eqCNum != null">
                eqCNum = #{eqCNum},
            </if>
            <if test="onlineNum != null">
                onlineNum = #{onlineNum},
            </if>
            <if test="offlineNum != null">
                offlineNum = #{offlineNum},
            </if>
            <if test="updateTime != null">
                updateTime = #{updateTime}
            </if>
            WHERE operatorNo = #{operatorNo}
            AND isDelete = 0
        </set>
    </update>
    <select id="getPage" resultType="com.jmt.model.eq.vo.EqOperatorStockVo">
        SELECT id,operatorNo,eqStockTotal,eqANum,eqBNum,eqCNum,onlineNum,offlineNum,createTime,updateTime,isDelete
        FROM eq_operator_stock
        <where>
            <if test="queryData != null and operatorNo != null">
                operatorNo = #{operatorNo}
            </if>
            <if test="queryData != null and eqStockTotal != null">
                and eqStockTotal = #{eqStockTotal}
            </if>
            <if test="queryData != null and eqANum != null">
                and eqANum = #{eqANum}
            </if>
            <if test="queryData != null and eqBNum != null">
                and eqBNum = #{eqBNum}
            </if>
            <if test="queryData != null and eqCNum != null">
                and eqCNum = #{eqCNum}
            </if>
        </where>
    </select>
    <select id="getInfo" resultType="com.jmt.model.eq.vo.EqOperatorStockVo">
        SELECT id,operatorNo,eqStockTotal,eqANum,eqBNum,eqCNum,onlineNum,offlineNum,createTime,updateTime,isDelete
        FROM eq_operator_stock
        WHERE id = #{id}
    </select>
    <select id="getInfoByOperatorNo" resultType="com.jmt.model.eq.dto.EqOperatorStock">
        SELECT id,operatorNo,eqStockTotal,eqANum,eqBNum,eqCNum,onlineNum,offlineNum,createTime,updateTime,isDelete
        FROM eq_operator_stock
        WHERE operatorNo = #{operatorNo}
          AND isDelete = 0
    </select>
    <select id="getOperatorEqCount" resultType="java.lang.Integer">
        SELECT eqStockTotal
        FROM eq_operator_stock
        WHERE operatorNo = #{operatorNo}
          AND isDelete = 0
    </select>
</mapper>
