<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace名字必须与Mapper接口名一致 -->

<mapper namespace="com.jmt.dao.EqInfoDao">
	<sql id="Base_Column_List">
		`id`,
		`eqNo`,
		`eqType`,
		`eqState`,
		`busNo`,
		`shopNo`,
		`operatorNo`,
		`investorNo`,
		`deployNo`,
		`country`,
		`province`,
		`city`,
		`area`,
		`address`,
		`longitude`,
		`latitude`,
		`createTime`,
		`updateTime`,
		`isDelete`
	</sql>
	<insert id="batchInsert">
		INSERT INTO eq_info(eqNo,eqState,createTime,isDelete)
		VALUES
		<foreach collection="newEqList" item="item" separator=",">
			(#{item.eqNo},#{item.eqState},NOW(),NOW())
		</foreach>
	</insert>

	<update id="transferOperator">
		UPDATE eq_info
		SET operatorNo = #{targetOperatorNo}
		WHERE operatorNo = #{sourceOperatorNo}
		AND isDelete = 0
	</update>
	<update id="assign">
		UPDATE eq_info
		SET investorNo = #{investorNo}
		WHERE eqNo in
		<foreach collection="existsEqNos" item="eqNo" open="(" separator="," close=")">
			#{eqNo}
		</foreach>
		AND isDelete = 0
	</update>
	<update id="transferSalesman">
		UPDATE eq_info
		SET deployNo = #{targetSalesmanNo}
		WHERE deployNo = #{sourceSalesmanNo}
		AND isDelete = 0
	</update>

    <update id="update" parameterType="com.jmt.model.eq.dto.EqInfo">
		UPDATE eq_info
		<set>
			<if test="eqNo !=null">
				eqNo = #{eqNo},
			</if>
			<if test="eqType !=null">
				eqType = #{eqType},
			</if>
			<if test="eqState !=null">
				eqState = #{eqState},
			</if>
			<if test="busNo !=null">
				busNo = #{busNo},
			</if>
			<if test="shopNo !=null">
				shopNo = #{shopNo},
			</if>
			<if test="operatorNo !=null">
				operatorNo = #{operatorNo},
			</if>
			<if test="investorNo !=null">
				investorNo = #{investorNo},
			</if>
			<if test="deployNo !=null">
				deployNo = #{deployNo},
			</if>
			<if test="updateTime !=null">
				updateTime = #{updateTime}
			</if>
		</set>
		WHERE busNo = #{busNo}
		AND eqType = #{eqType}
		AND isDelete = 0
	</update>
    <select id="getPage" parameterType="com.jmt.model.eq.dto.EqInfoPageQueryDto" resultType="com.jmt.model.eq.vo.EqInfoVo">
		SELECT
			<include refid="Base_Column_List"/>
		FROM
			eq_info
		WHERE
			isDelete = 0
			<if test="eqNo != null">
				AND eqNo = #{eqNo}
			</if>
			<if test="eqType != null">
				AND eqType = #{eqType}
			</if>
			<if test="eqState != null">
				AND eqState = #{eqState}
			</if>
			<if test="busNo != null">
				AND busNo = #{busNo}
			</if>
			<if test="shopNo != null">
				AND shopNo = #{shopNo}
			</if>
			<if test="operatorNo != null">
				AND operatorNo = #{operatorNo}
			</if>
			<if test="investorNo != null">
				AND investorNo = #{investorNo}
			</if>
			<if test="deployNo != null">
				AND deployNo = #{deployNo}
			</if>
			<if test="address != null">
				AND address LIKE CONCAT('%',#{address},'%')
			</if>
	</select>
	<select id="getInfo" resultType="com.jmt.model.eq.vo.EqInfoVo">
		SELECT
			<include refid="Base_Column_List"/>
		FROM
			eq_info
		WHERE
			id = #{id}
	</select>
	<select id="selectExistingEqNos" resultType="java.lang.String">
		SELECT eqNo FROM eq_info
		WHERE eqNo in
		<foreach collection="eqNoList" item="eqNo" open="(" separator="," close=")">
			#{eqNo}
		</foreach>
		AND isDelete = 0
	</select>
	<select id="checkExistsDevices" resultType="java.lang.String">
		SELECT eqNo
		FROM eq_info
		WHERE eqNo in
		<foreach collection="eqNos" item="eqNo" open="(" separator="," close=")">
			#{eqNo}
		</foreach>
		AND isDelete = 0
		AND investorNo IS NULL
	</select>
	<select id="getInfoByBusNo" resultType="com.jmt.model.eq.vo.EqInfoVo">
		SELECT
			<include refid="Base_Column_List"/>
		FROM
			eq_info
		WHERE
			busNo = #{busNo} and eqState = 1 AND isDelete = 0
	</select>
	<select id="getInfoByShopNo" resultType="com.jmt.model.eq.vo.EqListVo">
		SELECT id,eqNo,eqType,eqState
		FROM
		eq_info
		WHERE
			shopNo = #{shopNo}  AND isDelete = 0
	</select>

	<select id="getInvestorEqPage" resultType="com.jmt.model.eq.vo.EqInfoVo">
		SELECT
			<include refid="Base_Column_List"/>
		FROM
			eq_info
		WHERE
			investorNo = #{investorNo} AND isDelete = 0
	</select>

	<select id="optionalPage" parameterType="com.jmt.model.eq.dto.InvestorEqPageQueryDto" resultType="com.jmt.model.eq.vo.EqInfoVo">
		SELECT
			<include refid="Base_Column_List"/>
		FROM
			eq_info
		WHERE
			isDelete = 0 AND (investorNo = #{investorNo} OR investorNo IS NULL)
			<if test="eqType != null">
				AND eqType = #{eqType}
			</if>
			<if test="shopNo != null">
				AND shopNo = #{shopNo}
			</if>
			<if test="address != null">
				AND address LIKE CONCAT('%',#{address},'%')
			</if>
	</select>

	<select id="getMyPage" resultType="com.jmt.model.eq.vo.EqInfoVo">
		SELECT
			<include refid="Base_Column_List"/>
		FROM
			eq_info
		WHERE
			isDelete = 0 AND (busNo = #{busNo} OR shopNo = #{shopNo} OR operatorNo = #{operatorNo} OR investorNo = #{investorNo} OR deployNo = #{deployNo})
			<if test="eqType != null">
				AND eqType = #{eqType}
			</if>
			<if test="eqState != null">
				AND eqState = #{eqState}
			</if>
	</select>
</mapper>
