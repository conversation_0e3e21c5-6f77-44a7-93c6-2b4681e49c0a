package com.jmt.dao;

import com.jmt.model.eq.dto.EqOperatorStockRecord;
import com.jmt.model.eq.dto.EqOperatorStockRecordDto;
import com.jmt.model.eq.vo.EqOperatorStockRecordVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OperatorEqRecordDao {

    /**
     * 获取分页数据
     * @param queryData 查询条件
     * @return
     */
    List<EqOperatorStockRecordVo> getPage(EqOperatorStockRecordDto queryData);
    /**
     * 按id获取详情
     * @param id
     * @return
     */
    EqOperatorStockRecordVo getInfo(Integer id);
    /**
     * 添加记录
     * @param
     */
    void add(EqOperatorStockRecord operatorStockRecord);

    Integer recordDeploy(@Param("operatorNo") String operatorNo, @Param("recordType") Integer recordType);
}
