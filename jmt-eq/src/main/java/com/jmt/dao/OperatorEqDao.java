package com.jmt.dao;

import com.jmt.model.eq.dto.EqOperatorStock;
import com.jmt.model.eq.dto.EqOperatorStockDto;
import com.jmt.model.eq.vo.EqBusinessSetupVo;
import com.jmt.model.eq.vo.EqOperatorStockVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OperatorEqDao {

    List<EqOperatorStockVo> getPage(EqOperatorStockDto queryData);

    EqOperatorStockVo getInfo(Integer id);

    void transferOperatorStock(@Param("sourceOperatorNo") String sourceOperatorNo, @Param("targetOperatorNo") String targetOperatorNo);

    EqOperatorStock getInfoByOperatorNo(String sourceOperatorNo);

    void update(EqOperatorStock eqOperatorStock);

    Integer getOperatorEqCount(String operatorNo);
}
