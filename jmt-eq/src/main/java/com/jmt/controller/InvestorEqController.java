package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.eq.dto.InvestorEqPageQueryDto;
import com.jmt.model.eq.vo.EqInfoVo;
import com.jmt.model.jhh.dto.AssignInvestorEqDto;
import com.jmt.model.page.PageQuery;
import com.jmt.service.InvestorEqService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/eq/investor/v1")
public class InvestorEqController extends BaseController {

    @Resource
    private InvestorEqService investorEqService;

    /**
     * 设备收益人分配设备
     * @param
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/assign")
    public String assign(@RequestBody AssignInvestorEqDto assignInvestorEqDto) {
        investorEqService.assign(assignInvestorEqDto);
        return super.responseSuccess("分配成功");
    }

    /**
     * 设备收益人已分配设备
     */
    @PostMapping(value = "/page")
    public String page(@RequestBody PageQuery<String> pageQuery) {
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
        }
        List<EqInfoVo> dataList = investorEqService.page(pageQuery);
        return super.responseSuccess(dataList,"查询成功");
    }
    /**
     * 设备收益人可选择设备
     */
    @PostMapping(value = "/optionalPage")
    public String optionalPage(@RequestBody PageQuery<InvestorEqPageQueryDto> pageQuery){
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
        }

        List<EqInfoVo> dataList = investorEqService.optionalPage(pageQuery);
        return super.responseSuccess(dataList,"查询成功");
    }
}
