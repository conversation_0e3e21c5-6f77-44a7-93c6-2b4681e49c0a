package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.eq.dto.EqOperatorStockDto;
import com.jmt.model.eq.vo.EqBusinessSetupVo;
import com.jmt.model.eq.vo.EqOperatorStockVo;
import com.jmt.model.jhh.dto.TransferOperatorEqDto;
import com.jmt.model.jhh.dto.TransferSalesmanEqDto;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.service.EqInfoService;
import com.jmt.service.OperatorEqService;
import com.jmt.service.SalesmanEqService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/eq/salesman/v1")
public class SalesmanEqController extends BaseController {

    @Resource
    private SalesmanEqService salesmanEqService;

    /**
     * 业务员设备迁移
     * @param
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/transfer")
    public String transferSalesman(@RequestBody TransferSalesmanEqDto transferSalesmanEqDto) {
        salesmanEqService.transferSalesman(transferSalesmanEqDto);
        return super.responseSuccess("迁移成功");
    }
}
