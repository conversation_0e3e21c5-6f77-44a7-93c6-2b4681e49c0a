package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.eq.dto.*;
import com.jmt.model.eq.vo.EqInfoVo;
import com.jmt.model.eq.vo.EqListVo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.service.EqInfoService;
import com.jmt.util.LoginUserUtil;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/eq/v1")
@Service
public class EqInfoController extends BaseController {

    @Resource
    private EqInfoService eqInfoService;


    /**
     * 我的设备分页查询
     * @param pageQuery 列表查询条件
     * @return String
     */

    @ResponseBody
    @PostMapping(value = "/my/getPage")
    public String getMyPage(@RequestBody PageQuery<EqInfoPageQueryDto> pageQuery) {
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
        }
        LoginUaaUser loginUaaUser = LoginUserUtil.get();
        assert loginUaaUser != null;
        PageResult<EqInfoVo> page = eqInfoService.getMyPage(pageQuery,loginUaaUser);
        return super.responseSuccess(page,"查询成功");
    }
    /**
     * 分页查询
     * @param pageQuery 列表查询条件
     * @return String
     */

    @ResponseBody
    @PostMapping(value = "/getPage")
    public String getPage(@RequestBody PageQuery<EqInfoPageQueryDto> pageQuery) {
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
        }
        PageResult<EqInfoVo> page = eqInfoService.getPage(pageQuery);
        return super.responseSuccess(page,"查询成功");
    }
    /**
     * 查看
     * @param id 主键ID
     * @return String
     */

    @ResponseBody
    @GetMapping(value = "/getInfo")
    public String getInfo(@RequestParam Long id) {
        EqInfoVo eqInfoVo = eqInfoService.getInfo(id);
        return super.responseSuccess(eqInfoVo,"查询成功");
    }

    /**
     * 设备码导入
     */
    @ResponseBody
    @PostMapping(value = "/import")
    public String importEq(@RequestParam("file") MultipartFile file) throws IOException {
        eqInfoService.importEq(file);
        return super.responseSuccess("导入成功");
    }

    /**
     * 根据busNo查询设备信息
     */
    @ResponseBody
    @PostMapping(value = "/getInfoByBusNo")
    public String getInfoByBusNo(@RequestParam String busNo) {
        List<EqInfoVo> eqInfoVo = eqInfoService.getInfoByBusNo(busNo);
        return super.responseSuccess(eqInfoVo,"查询成功");
    }

    @PostMapping(value = "/getInfoByShopNo")
    String getInfoByShopNo(@RequestParam("shopNo") String shopNo){
        List<EqListVo> eqInfoVo = eqInfoService.getInfoByShopNo(shopNo);
        return super.responseSuccess(eqInfoVo,"查询成功");
    }
    /**
     * 设备配网
     */
    @PostMapping(value = "/startNetworkConfig")
    public String startNetworkConfig(@RequestBody EqNetWorkRequest eqNetWorkRequest) {
        eqInfoService.startNetworkConfig(eqNetWorkRequest);
        return super.responseSuccess("配网成功");
    }

}
