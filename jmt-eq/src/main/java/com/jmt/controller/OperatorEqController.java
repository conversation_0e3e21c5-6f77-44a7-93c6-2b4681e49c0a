package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.eq.dto.EqOperatorStockDto;
import com.jmt.model.eq.vo.EqOperatorStockVo;
import com.jmt.model.jhh.dto.TransferOperatorEqDto;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.service.OperatorEqService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/eq/operator/v1")
public class OperatorEqController extends BaseController {

    @Resource
    private OperatorEqService operatorEqService;

    /**
     * 分页查询
     * @param pageQuery 列表查询条件
     * @return String
     */

    @ResponseBody
    @GetMapping(value = "/getPage")
    public String getPage(@RequestBody PageQuery<EqOperatorStockDto> pageQuery) {
        PageResult<EqOperatorStockVo> page = operatorEqService.getPage(pageQuery);
        return super.responseSuccess(page,"查询成功");
    }

    /**
     * 查看
     * @param id 主键ID
     * @return String
     */

    @ResponseBody
    @GetMapping(value = "/getInfo")
    public String getInfo(@RequestParam Integer id) {
        EqOperatorStockVo eqOperatorStockVo = operatorEqService.getInfo(id);
        return super.responseSuccess(eqOperatorStockVo,"查询成功");
    }

    /**
     * 设备迁移
     * @param
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/transfer")
    public String transfer(@RequestBody TransferOperatorEqDto transferOperatorEqDto) {
        operatorEqService.transferOperator(transferOperatorEqDto);
        return super.responseSuccess("迁移成功");
    }

    /**
     * 按运营商编号查询运营商设备库存数量
     *
     */
    @ResponseBody
    @GetMapping(value = "/getOperatorEqCount")
    public String getOperatorEqCount(@RequestParam String operatorNo) {
        Integer number = operatorEqService.getOperatorEqCount(operatorNo);
        return super.responseSuccess(number, "查询成功");
    }
}
