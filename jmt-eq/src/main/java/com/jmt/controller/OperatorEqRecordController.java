package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.eq.dto.EqOperatorStockRecord;
import com.jmt.model.eq.dto.EqOperatorStockRecordDto;
import com.jmt.model.eq.vo.EqOperatorStockRecordVo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.service.OperatorEqRecordService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/eq/stock/operator/v1")
public class OperatorEqRecordController extends BaseController {

    @Resource
    private OperatorEqRecordService operatorEqRecordService;

    /**
     * 分页查询
     * @param pageQuery 列表查询条件
     * @return String
     */

    @ResponseBody
    @GetMapping(value = "/getPage")
    public String getPage(@RequestBody PageQuery<EqOperatorStockRecordDto> pageQuery) {
        PageResult<EqOperatorStockRecordVo> page = operatorEqRecordService.getPage(pageQuery);
        return super.responseSuccess(page,"查询成功");
    }

    /**
     * 查看
     * @param id 主键ID
     * @return String
     */

    @ResponseBody
    @GetMapping(value = "/getInfo")
    public String getInfo(@RequestParam Integer id) {
        EqOperatorStockRecordVo eqOperatorStockVo = operatorEqRecordService.getInfo(id);
        return super.responseSuccess(eqOperatorStockVo,"查询成功");
    }
   /**
     * 添加
     * @param operatorStockRecord 添加参数
     * @return String
     */
   @ResponseBody
   @PostMapping(value = "/record")
    public String record(@RequestBody EqOperatorStockRecord operatorStockRecord) {
       operatorEqRecordService.record(operatorStockRecord);
        return super.responseSuccess("添加成功");
    }
    /**
     * 记录设备发货数量
     * @param
     * @return String
     */
    @ResponseBody
    @GetMapping(value = "/recordDeploy")
    public String recordDeploy(@RequestParam String operatorNo,@RequestParam Integer recordType) {
        Integer num = operatorEqRecordService.recordDeploy(operatorNo,recordType);
        return super.responseSuccess(num,"获取成功");
    }
}
