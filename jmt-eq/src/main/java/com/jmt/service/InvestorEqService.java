package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.jmt.dao.EqInfoDao;
import com.jmt.exception.BusinessException;
import com.jmt.model.eq.dto.InvestorEqPageQueryDto;
import com.jmt.model.eq.vo.EqInfoVo;
import com.jmt.model.jhh.dto.AssignInvestorEqDto;
import com.jmt.model.page.PageQuery;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class InvestorEqService {

    @Resource
    private EqInfoDao eqInfoDao;


    /**
     * 收益人分配设备
     */
    public void assign(AssignInvestorEqDto assignInvestorEqDto) {
        List<String> eqNos = assignInvestorEqDto.getEqNos();
        if (eqNos.isEmpty()){
            throw new BusinessException("设备编号不能为空");
        }
        String investorNo = assignInvestorEqDto.getInvestorNo();
        List<String> existsEqNos = eqInfoDao.checkExistsDevices(eqNos);
        if (existsEqNos.isEmpty()){
            throw new BusinessException("设备编号不存在");
        }
        eqInfoDao.assign(existsEqNos, investorNo);
    }


    public List<EqInfoVo> page(PageQuery<String> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        return eqInfoDao.getInvestorEqPage(pageQuery.getQueryData());
    }

    public List<EqInfoVo> optionalPage(PageQuery<InvestorEqPageQueryDto> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        return eqInfoDao.optionalPage(pageQuery.getQueryData());
    }
}
