package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.jmt.dao.EqInfoDao;
import com.jmt.dao.OperatorEqDao;
import com.jmt.enums.RecordTypeEnum;
import com.jmt.model.eq.dto.EqOperatorStock;
import com.jmt.model.eq.dto.EqOperatorStockDto;
import com.jmt.model.eq.dto.EqOperatorStockRecord;
import com.jmt.model.eq.vo.EqBusinessSetupVo;
import com.jmt.model.eq.vo.EqOperatorStockVo;
import com.jmt.model.jhh.dto.TransferOperatorEqDto;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Service
public class OperatorEqService {

    @Resource
    private EqInfoDao eqInfoDao;

    @Resource
    private OperatorEqDao operatorEqDao;

    public PageResult<EqOperatorStockVo> getPage(PageQuery<EqOperatorStockDto> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<EqOperatorStockVo> page = operatorEqDao.getPage(pageQuery.getQueryData());
        if (page != null){
            return new PageResult<>(page);
        }
        return null;
    }
    /**
     * 运营商设备迁移
     * @param transferOperatorEqDto
     */
    @Transactional
    public void transferOperator(TransferOperatorEqDto transferOperatorEqDto) {
        eqInfoDao.transferOperator(transferOperatorEqDto.getSourceOperatorNo(), transferOperatorEqDto.getTargetOperatorNo());
        operatorEqDao.transferOperatorStock(transferOperatorEqDto.getSourceOperatorNo(), transferOperatorEqDto.getTargetOperatorNo());
    }
    public EqOperatorStockVo getInfo(Integer id) {
        return operatorEqDao.getInfo(id);
    }

    public Integer getOperatorEqCount(String operatorNo) {
        return operatorEqDao.getOperatorEqCount(operatorNo);
    }
}
