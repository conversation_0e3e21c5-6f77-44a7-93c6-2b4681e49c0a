package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.jmt.dao.OperatorEqRecordDao;
import com.jmt.model.eq.dto.EqOperatorStockRecord;
import com.jmt.model.eq.dto.EqOperatorStockRecordDto;
import com.jmt.model.eq.vo.EqOperatorStockRecordVo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class OperatorEqRecordService {

    @Resource
    private OperatorEqRecordDao operatorEqRecordDao;
    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    public PageResult<EqOperatorStockRecordVo> getPage(PageQuery<EqOperatorStockRecordDto> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<EqOperatorStockRecordVo> page = operatorEqRecordDao.getPage(pageQuery.getQueryData());
        if (page != null){
            return new PageResult<>(page);
        }
        return null;
    }
    /**
     * 按id查询
     * @param id
     * @return
     */
    public EqOperatorStockRecordVo getInfo(Integer id) {
        return operatorEqRecordDao.getInfo(id);
    }

    public void record(EqOperatorStockRecord operatorStockRecord) {
        operatorEqRecordDao.add(operatorStockRecord);
    }

    public Integer recordDeploy(String operatorNo, Integer recordType) {
        return operatorEqRecordDao.recordDeploy(operatorNo,recordType);
    }
}
