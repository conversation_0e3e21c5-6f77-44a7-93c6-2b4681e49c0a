package com.jmt.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.alibaba.nacos.shaded.com.google.gson.JsonElement;
import com.alibaba.nacos.shaded.com.google.gson.JsonObject;
import com.alibaba.nacos.shaded.com.google.gson.JsonParser;
import com.github.pagehelper.PageHelper;
import com.jmt.client.JhhSalesmanFeignClient;
import com.jmt.dao.OperatorEqDao;
import com.jmt.dao.EqInfoDao;
import com.jmt.listener.EqInfoImportListener;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.eq.dto.*;
import com.jmt.model.eq.vo.EqInfoVo;
import com.jmt.model.eq.vo.EqListVo;
import com.jmt.model.jhh.dto.SalesmanInfo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;
import java.io.IOException;
import java.util.Date;
import java.util.List;

@Service
public class EqInfoService {

    @Resource
    private EqInfoDao eqInfoDao;

    @Resource
    private EqInfoImportListener eqInfoImportListener;

    @Resource
    private OperatorEqDao operatorEqDao;

    @Resource
    private JhhSalesmanFeignClient jhhSalesmanFeignClient;

    /**
     * 分页查询设备信息列表
     * @param pageQuery
     * @return
     */
    public PageResult<EqInfoVo> getPage(PageQuery<EqInfoPageQueryDto> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<EqInfoVo> page = eqInfoDao.getPage(pageQuery.getQueryData());
        if (page != null){
            return new PageResult<>(page);
        }
        return null;
    }
    /**
     * 查询设备信息详情
     *
     * @param id
     * @return
     */
    public EqInfoVo getInfo(Long id) {
        return eqInfoDao.getInfo(id);
    }

    /**
     * 设备导入
     *
     * @param file
     */
    public void importEq(MultipartFile file) throws IOException {
        EasyExcel.read(file.getInputStream(), EqInfoImport.class, eqInfoImportListener).sheet().headRowNumber(1).doRead();
    }

    public List<EqInfoVo> getInfoByBusNo(String busNo) {
        return eqInfoDao.getInfoByBusNo(busNo);
    }

    public List<EqListVo> getInfoByShopNo(String shopNo) {
        return eqInfoDao.getInfoByShopNo(shopNo);
    }

    /**
     * 开始网络配置
     * @param eqNetWorkRequest
     */
    public void startNetworkConfig(EqNetWorkRequest eqNetWorkRequest) {
        String infoByDeployerNo = jhhSalesmanFeignClient.getSalesmanInfoById(eqNetWorkRequest.getDevelopId());
        Gson gson = new Gson();
        JsonObject jsonObject = JsonParser.parseString(infoByDeployerNo).getAsJsonObject();
        JsonElement dataElement = jsonObject.get("data");
        SalesmanInfo salesmanInfo = gson.fromJson(dataElement, SalesmanInfo.class);
        EqInfo eqInfo = new EqInfo();
        BeanUtils.copyProperties(eqNetWorkRequest, eqInfo);
        eqInfo.setEqType(eqNetWorkRequest.getEqType());
        eqInfo.setEqState(1);//配网成功激活
        eqInfo.setBusNo(eqNetWorkRequest.getBusNo());
        eqInfo.setOperatorNo(salesmanInfo.getOperatorNo());
        eqInfo.setDeployNo(salesmanInfo.getSalesmanNo());
        eqInfo.setUpdateTime(new Date());
        eqInfo.setIsDelete(0);
        eqInfoDao.update(eqInfo);
        EqOperatorStock infoByOperatorNo = operatorEqDao.getInfoByOperatorNo(eqInfo.getOperatorNo());
        EqOperatorStock eqOperatorStock = new EqOperatorStock();
        BeanUtils.copyProperties(infoByOperatorNo,eqOperatorStock);
        eqOperatorStock.setOnlineNum(eqOperatorStock.getOnlineNum()+1);
        eqOperatorStock.setOfflineNum(eqOperatorStock.getOfflineNum()-1);
        eqOperatorStock.setUpdateTime(new Date());
        operatorEqDao.update(eqOperatorStock);
    }

    public PageResult<EqInfoVo> getMyPage(PageQuery<EqInfoPageQueryDto> pageQuery, LoginUaaUser loginUaaUser) {
        EqInfoPageQueryDto dto = pageQuery.getQueryData();
        dto.setDeployNo(loginUaaUser.getSalesmanNo());
        dto.setInvestorNo(loginUaaUser.getInvestorNo());
        dto.setOperatorNo(loginUaaUser.getOperatorNo());
        dto.setShopNo(loginUaaUser.getShopNo());
        dto.setBusNo(loginUaaUser.getBusNo());
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<EqInfoVo> page = eqInfoDao.getMyPage(pageQuery.getQueryData());
        if (page != null){
            return new PageResult<>(page);
        }
        return null;
    }
}
