package com.jmt.config;

import com.jmt.util.DySmsUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class DySmsConfig {

    @Value("${jmt.sms.accessKeyId}")
    private String accessKeyId;

    @Value("${jmt.sms.accessKeySecret}")
    private String accessKeySecret;

    @Bean
    public void initStatic() {
        DySmsUtil.setAccessKeyId(accessKeyId);
        DySmsUtil.setAccessKeySecret(accessKeySecret);
    }

}
