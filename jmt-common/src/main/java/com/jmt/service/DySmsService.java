package com.jmt.service;

import com.alibaba.fastjson.JSONObject;
import com.jmt.base.BaseService;
import com.jmt.constant.RedisKeyConstant;
import com.jmt.enums.DySmsEnum;
import com.jmt.model.auth.SmsCodeDto;
import com.jmt.util.DySmsUtil;
import com.jmt.util.RedisUtil;
import org.apache.commons.lang.RandomStringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service
public class DySmsService extends BaseService {

	@Resource
	private RedisUtil redisUtil;

	public Boolean sendSms(SmsCodeDto smsCodeDto) {
		String telPhone = smsCodeDto.getTelPhone();
		try {
			JSONObject jsonObject = new JSONObject();
			String smsCode = RandomStringUtils.randomNumeric(6);
			jsonObject.put("code", smsCode);
			boolean sendSucceed = DySmsUtil.sendSms(telPhone, jsonObject,DySmsEnum.AUTHENTICATION_CODE);
			if (sendSucceed) {
				redisUtil.set(RedisKeyConstant.SMS_CACHE_KEY + telPhone, smsCode, RedisKeyConstant.SMS_CACHE_EXPIRE);
				redisUtil.set(RedisKeyConstant.SMS_SEND_STINT + telPhone, telPhone, RedisKeyConstant.SMS_SEND_STINT_EXPIRE);
			}
			return sendSucceed;
		} catch (Exception e) {
			logger.error("短信发送失败:",e);
		}
		return Boolean.FALSE;
	}
}
