package com.jmt.service;

import com.aliyun.ocr_api20210707.Client;
import com.aliyun.ocr_api20210707.models.RecognizeBusinessLicenseRequest;
import com.aliyun.ocr_api20210707.models.RecognizeBusinessLicenseResponse;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jmt.model.BusinessLicenseInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.InputStream;
@Slf4j
@Service
public class OcrService {

    @Value("${jmt.ocr.accessKey}")
    private String accessKeyId;

    @Value("${jmt.ocr.secretKey}")
    private String accessKeySecret;

    @Value("${jmt.ocr.endpoint}")
    private String endpoint;

    public Client createClient() throws Exception {
        Config config = new Config()
                .setAccessKeyId(accessKeyId)
                .setAccessKeySecret(accessKeySecret)
                .setType("access_key")
                .setEndpoint(endpoint);

        return new Client(config);
    }

    /**
     * 提取营业执照关键信息
     */
    public BusinessLicenseInfo extractBusinessLicenseInfo(String filePath) {
        BusinessLicenseInfo result = new BusinessLicenseInfo();

        try {
            Client client = createClient();

            InputStream bodyStream = com.aliyun.darabonba.stream.Client.readFromFilePath(filePath);

            RecognizeBusinessLicenseRequest request = new RecognizeBusinessLicenseRequest()
                    .setBody(bodyStream);

            RuntimeOptions runtime = new RuntimeOptions();

            RecognizeBusinessLicenseResponse response = client.recognizeBusinessLicenseWithOptions(request, runtime);

            if (response != null && response.getBody() != null) {
                String dataJson = response.getBody().getData();
                System.out.println("dataJson: " + dataJson);

                if (dataJson != null) {
                    JSONObject firstLevelData = JSON.parseObject(dataJson);

                    JSONObject licenseData = firstLevelData.getJSONObject("data");
                    if (licenseData != null) {
                        // 提取信息
                        result.setCreditCode(licenseData.getString("creditCode"));
                        result.setCompanyName(licenseData.getString("companyName"));
                        result.setLegalPerson(licenseData.getString("legalPerson"));
                        result.setCompanyType(licenseData.getString("companyType"));
                        result.setRegisteredCapital(licenseData.getString("registeredCapital"));
                        result.setValidPeriod(licenseData.getString("validPeriod"));
                        result.setBusinessAddress(licenseData.getString("businessAddress"));
                        result.setBusinessScope(licenseData.getString("businessScope"));
                        result.setRegistrationDate(licenseData.getString("RegistrationDate"));
                        result.setIssueDate(licenseData.getString("issueDate"));
                    }
                }
            }
        } catch (Exception error) {
            log.error("OCR识别失败 ", error);
            return result;
        }
        return result;
    }

    /**
     * 简化版本 - 只提取关键信息
     */
    public BusinessLicenseInfo extractSimpleBusinessLicenseInfo(String filePath) {
        BusinessLicenseInfo result = new BusinessLicenseInfo();

        try {
            com.aliyun.ocr_api20210707.Client client = createClient();

            InputStream bodyStream = com.aliyun.darabonba.stream.Client.readFromFilePath(filePath);

            RecognizeBusinessLicenseRequest request = new RecognizeBusinessLicenseRequest()
                    .setBody(bodyStream);

            RuntimeOptions runtime = new RuntimeOptions();

            RecognizeBusinessLicenseResponse response = client.recognizeBusinessLicenseWithOptions(request, runtime);

            if (response != null && response.getBody() != null) {
                String dataJson = response.getBody().getData();

                if (dataJson != null) {
                    JSONObject firstLevelData = JSON.parseObject(dataJson);
                    JSONObject licenseData = firstLevelData.getJSONObject("data");
                    if (licenseData != null) {
                        result.setCreditCode(licenseData.getString("creditCode"));
                        result.setCompanyName(licenseData.getString("companyName"));
                        result.setLegalPerson(licenseData.getString("legalPerson"));
                        return result;
                    }
                }
            }

        } catch (Exception error) {
            log.error("OCR识别失败 ", error);
            return result;
        }
        return result;
    }

}
