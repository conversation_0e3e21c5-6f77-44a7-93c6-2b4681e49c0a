package com.jmt.service;

import cn.hutool.core.util.ObjectUtil;
import com.jmt.base.BaseService;
import com.jmt.model.OSSFile;
import com.jmt.util.OssBootUtil;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;


@Service
public class OSSFileService extends BaseService {

	public OSSFile upload(MultipartFile multipartFile) {
		String fileName = multipartFile.getOriginalFilename();
		String url = OssBootUtil.upload(multipartFile);
		if (ObjectUtil.isNotNull(url)) {
			OSSFile ossFile = new OSSFile();
			ossFile.setFileName(fileName);
			ossFile.setFileUrl(url);
			return ossFile;
		} else {
			return null;
		}
	}
}
