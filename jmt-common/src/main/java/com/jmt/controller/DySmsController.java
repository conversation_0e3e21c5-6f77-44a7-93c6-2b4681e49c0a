package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.constant.RedisKeyConstant;
import com.jmt.model.auth.SmsCodeDto;
import com.jmt.service.DySmsService;
import com.jmt.util.RedisUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Controller
@RequestMapping("/common/sms/")
public class DySmsController extends BaseController {

	@Resource
	private DySmsService dySmsService;

	@Resource
	private RedisUtil redisUtil;


	@ResponseBody
	@PostMapping("/send")
	public  String sendSms(@RequestBody SmsCodeDto smsCodeDto) {
		String telPhone = smsCodeDto.getTelPhone();
		String regexp = "^((13[0-9])|(14[0-9])|(15([0-3]|[5-9]))|(16([0-3]|[5-9]))|(17[013678])|(18[0-9]))\\d{8}$";
		Pattern pattern = Pattern.compile(regexp);
		Matcher matcher = pattern.matcher(telPhone);
		if (!matcher.matches()) {
			return super.responseFail("手机号格式错误");
		}
		boolean doesItExist = redisUtil.hasKey(RedisKeyConstant.SMS_SEND_STINT + telPhone);
		if (doesItExist) {
			return super.responseFail("请稍后在发送短信");
		}
		Boolean sendSucceed = dySmsService.sendSms(smsCodeDto);
		if (sendSucceed) {
			return super.responseSuccess("短信发送成功");
		} else {
			return super.responseFail("短信发送失败");
		}

	}
}
