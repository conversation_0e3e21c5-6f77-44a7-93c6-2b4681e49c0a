package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.BusinessLicenseInfo;
import com.jmt.service.OcrService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;

@RestController
@RequestMapping("/common/ocr")
public class OcrController extends BaseController {

    @Autowired
    private OcrService ocrService;

    /**
     * 识别营业执照并返回完整信息实体类
     */
    @PostMapping("/busLicense")
    public String recognizeBusinessLicense(@RequestParam("file") MultipartFile file) {
        try {
            // 创建临时文件
            File tempFile = File.createTempFile("license", ".jpg");
            file.transferTo(tempFile);

            BusinessLicenseInfo result = ocrService.extractBusinessLicenseInfo(tempFile.getAbsolutePath());
            if(result == null){
                tempFile.delete();
                return super.responseFail("解析失败");
            }
            tempFile.delete();
            return super.responseSuccess(result,"上传成功");
        } catch (IOException e) {
            return super.responseFail("解析失败");
        }
    }

}
