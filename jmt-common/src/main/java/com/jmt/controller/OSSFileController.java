package com.jmt.controller;

import cn.hutool.core.util.ObjectUtil;
import com.jmt.base.BaseController;
import com.jmt.model.OSSFile;
import com.jmt.service.OSSFileService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;


@Controller
@RequestMapping("/common")
public class OSSFileController extends BaseController {

	@Resource
	private OSSFileService ossFileService;


	@ResponseBody
	@PostMapping("/oss/upload")
	public  String upload(@RequestParam("file") MultipartFile multipartFile) {
		OSSFile ossFile = ossFileService.upload(multipartFile);
		if (ObjectUtil.isNotNull(ossFile)) {
			return super.responseSuccess(ossFile,"上传成功");
		} else {
			return super.responseFail("上传失败");
		}
	}
}
