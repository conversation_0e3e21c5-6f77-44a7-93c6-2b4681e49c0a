<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<artifactId>jmt-common</artifactId>
	<version>1.0.0</version>
	<packaging>jar</packaging>

	<parent>
		<groupId>com.jmt</groupId>
		<artifactId>jmt-cloud</artifactId>
		<version>1.0.0</version>
	</parent>
	<properties>
		<aliyun-java-sdk-core.version>3.2.3</aliyun-java-sdk-core.version>
		<aliyun-java-sdk-dysmsapi.version>1.0.0</aliyun-java-sdk-dysmsapi.version>
	</properties>
	<dependencies>
		<!-- 平台基础模块 -->
		<dependency>
			<groupId>com.jmt</groupId>
			<artifactId>jmt-base</artifactId>
			<version>1.0.0</version>
		</dependency>
		<!-- 平台认证授权模块 -->
		<dependency>
			<groupId>com.jmt</groupId>
			<artifactId>jmt-auth</artifactId>
			<version>1.0.0</version>
		</dependency>
		<!-- 引入web支持 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>com.lmax</groupId>
			<artifactId>disruptor</artifactId>
			<version>3.3.6</version>
		</dependency>

		<dependency>
			<groupId>com.aliyun.oss</groupId>
			<artifactId>aliyun-sdk-oss</artifactId>
			<version>3.1.0</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>mts20140618</artifactId>
			<version>3.3.56</version>
		</dependency>
		<!-- 阿里云短信 -->
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-dysmsapi</artifactId>
			<version>${aliyun-java-sdk-dysmsapi.version}</version>
		</dependency>
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-core</artifactId>
			<version>${aliyun-java-sdk-core.version}</version>
		</dependency>
		<dependency>
			<groupId>com.drewnoakes</groupId>
			<artifactId>metadata-extractor</artifactId>
			<version>2.14.0</version>
		</dependency>
		<dependency>
			<groupId>ws.schild</groupId>
			<artifactId>jave-all-deps</artifactId>
			<version>2.6.0</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>2.0.43</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>ocr_api20210707</artifactId>
			<version>3.1.3</version>
		</dependency>
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>darabonba-stream</artifactId>
			<version>0.0.1</version>
		</dependency>
	</dependencies>
	<build>
		<finalName>jmt-common</finalName>
		<plugins>
			<!--打包jar -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<configuration>
					<excludes>
						<exclude>*.properties</exclude>
						<exclude>*.yml</exclude>
						<exclude>*.xml</exclude>
						<exclude>mapper/**</exclude>
						<exclude>config/**</exclude>
					</excludes>
					<outputDirectory>${project.build.directory}</outputDirectory>
				</configuration>
			</plugin>
			<!--拷贝依赖 copy-dependencies -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-dependencies</id>
						<phase>package</phase>
						<goals>
							<goal>copy-dependencies</goal>
						</goals>
						<configuration>
							<outputDirectory>${project.build.directory}/lib</outputDirectory>
							<overWriteReleases>false</overWriteReleases>
							<overWriteSnapshots>false</overWriteSnapshots>
							<overWriteIfNewer>true</overWriteIfNewer>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<!--拷贝资源文件 copy-resources -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-resources</id>
						<phase>package</phase>
						<goals>
							<goal>copy-resources</goal>
						</goals>
						<configuration>
							<encoding>UTF-8</encoding>
							<resources>
								<resource>
									<directory>src/main/resources</directory>
								</resource>
							</resources>
							<outputDirectory>${project.build.directory}/resources</outputDirectory>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>${spring.boot.version}</version>
				<configuration>
					<!-- 包含本地 jar 依赖 -->
					<includeSystemScope>true</includeSystemScope>
					<!-- 重写包含依赖，包含不存在的依赖，jar里没有pom里的依赖 -->
					<includes>
						<include>
							<groupId>null</groupId>
							<artifactId>null</artifactId>
						</include>
					</includes>
					<layout>ZIP</layout>
					<!-- 使用外部配置文件，jar包里没有资源文件 -->
					<addResources>true</addResources>
					<outputDirectory>${project.build.directory}</outputDirectory>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>