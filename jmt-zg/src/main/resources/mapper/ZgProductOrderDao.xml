<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ZgProductOrderDao">


    <sql id="Base_Column_List">
        id, orderNo, orderName, buyUserId, payType, payAmount, orderStatus,
        driverNo,addressId, createTime, updateTime, isDelete
    </sql>

    <insert id="insert" parameterType="com.jmt.model.zg.entity.ZgProductOrder" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO zg_product_order (
            orderNo, orderName, buyUserId, payType, payAmount, orderStatus,
            driverNo,addressId, createTime, updateTime, isDelete
        ) VALUES (
                     #{orderNo}, #{orderName}, #{buyUserId}, #{payType}, #{payAmount}, #{orderStatus},
                     #{driverNo},#{addressId}, NOW(), NOW(), 0
                 )
    </insert>

    <select id="selectByOrderNo" resultType="com.jmt.model.zg.entity.ZgProductOrder">
        SELECT <include refid="Base_Column_List" />
        FROM zg_product_order
        WHERE orderNo = #{orderNo} AND isDelete = 0
    </select>


    <select id="selectPcOrderDetails" resultType="com.jmt.model.zg.vo.ZgProductOrderDetailVO">
        SELECT
            id,
            orderNo,
            orderName,
            buyUserId,
            payType,
            driverNo,
            orderStatus,
            addressId,
            payAmount
        FROM zg_product_order
        WHERE orderNo = #{orderNo}
          AND isDelete = 0
    </select>


    <select id="selectByUserId" resultType="com.jmt.model.zg.entity.ZgProductOrder">
        SELECT <include refid="Base_Column_List" />
        FROM zg_product_order
        WHERE buyUserId = #{buyUserId} AND isDelete = 0
        ORDER BY createTime DESC
    </select>

    <update id="updateById" parameterType="com.jmt.model.zg.entity.ZgProductOrder">
        UPDATE zg_product_order
        <set>
            <if test="orderNo != null">orderNo = #{orderNo},</if>
            <if test="orderName != null">orderName = #{orderName},</if>
            <if test="payType != null">payType = #{payType},</if>
            <if test="payAmount != null">payAmount = #{payAmount},</if>
            <if test="orderStatus != null">orderStatus = #{orderStatus},</if>
            <if test="driverNo != null">driverNo = #{driverNo},</if>
            <if test="addressId != null">addressId = #{addressId},</if>


            updateTime = NOW()
        </set>
        WHERE id = #{id}
    </update>


    <delete id="delete" parameterType="java.lang.Long">
        UPDATE zg_product_order
        SET isDelete = 1, updateTime = NOW()
        WHERE id = #{id} AND isDelete = 0
    </delete>

    <update id="updateOrderStatus">
        UPDATE zg_product_order
        SET orderStatus = #{orderStatus}, updateTime = #{updateTime} ,payType= #{payType}
        WHERE orderNo = #{orderNo}
    </update>

    <sql id="ZgProductOrderIndexList">
        s.id, s.orderNo, s.orderName, s.buyUserId, s.payAmount, s.orderStatus,s.addressId,
        s.driverNo, s.createTime

    </sql>

    <select id="selectByCondition" parameterType="com.jmt.model.zg.dto.ZgProductOrderDTO" resultType="com.jmt.model.zg.vo.ZgProductOrderIndexVO">
        SELECT
        <include refid="ZgProductOrderIndexList" />
        FROM zg_product_order s
        WHERE s.isDelete = 0
        <if test="orderNo != null">AND s.orderNo = #{orderNo}</if>
        <if test="driverNo != null">AND s.driverNo = #{driverNo}</if>
        <if test="orderStatus != null">AND s.orderStatus = #{orderStatus}</if>
        <if test="startTime != null">AND s.createTime >= #{startTime}</if>
        <if test="endTime != null">AND s.createTime &lt;= #{endTime}</if>

        ORDER BY s.createTime DESC
    </select>

    <update id="assignDriver">
        UPDATE zg_product_order
        SET driverNo = #{driverNo},
            updateTime = NOW()
        WHERE orderNo = #{orderNo}
          AND isDelete = 0
          AND orderStatus = 1
    </update>

    <select id="selectOrderBaseByUserId" parameterType="com.jmt.model.zg.entity.ZgProductOrder" resultType="com.jmt.model.zg.vo.ZgProductOrderAppVO">
        SELECT
            id,
            orderNo,
            payAmount,
            orderStatus
        FROM zg_product_order
        WHERE buyUserId = #{buyUserId}
        <if test="orderStatus != null">AND orderStatus = #{orderStatus}</if>
          AND isDelete = 0
        ORDER BY createTime DESC
    </select>

    <select id="selectOrderDetailsByUser" resultType="com.jmt.model.zg.vo.ZgProductOrderDetailAppVO">
        SELECT
            d.id,
            d.orderNo,
            d.supplierNo,
            d.productNo,
            d.productName,
            d.originalPrice,
            d.currentPrice,
            d.quantity,
            p.categoryId,
            pc.categoryName,
            p.productMainPicture,
            p.productBrand,
            p.productSize,
            p.productBarCode,
            p.productNature
        FROM zg_product_order_detail d
                 LEFT JOIN zg_product_info p ON d.productNo = p.productNo AND p.isDelete = 0
                 LEFT JOIN zg_product_category pc ON p.categoryId = pc.id AND pc.isDelete = 0
        WHERE d.orderNo IN (
            SELECT orderNo FROM zg_product_order
            WHERE buyUserId = #{buyUserId} AND isDelete = 0
        )
          AND d.isDelete = 0
    </select>

</mapper>