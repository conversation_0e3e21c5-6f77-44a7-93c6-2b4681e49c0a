<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ZgSupplierInfoDao">

    <sql id="Base_Column_List">
        id, uuaId, supplierNo, supplierName, legalPerson, bankName, bankCardNo,
        linkman, creditCode, nature, telPhone, country, province, city, area, address,
        longitude, latitude, supplierPermit, supplierLicense, categoryId, supplierStar,
        deliveryModel, minDeliveryAmount, remark, auditStatus, reason, createTime,
        updateTime, isDelete
    </sql>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.zg.entity.ZgSupplierInfo" useGeneratedKeys="true">
        insert into zg_supplier_info
        (id,uuaId, supplierNo, supplierName, legalPerson, bankName, bankCardNo,
         linkman, creditCode, nature, telPhone, country, province, city,area, address,
         longitude, latitude, supplierPermit, supplierLicense, categoryId, supplierStar,
         deliveryModel, minDeliveryAmount, remark, auditStatus, reason, createTime,
         updateTime, isDelete)
        values (#{id},#{uuaId}, #{supplierNo}, #{supplierName}, #{legalPerson}, #{bankName}, #{bankCardNo},
                #{linkman}, #{creditCode}, #{nature}, #{telPhone}, #{country}, #{province}, #{city},#{area}, #{address},
                #{longitude}, #{latitude}, #{supplierPermit}, #{supplierLicense}, #{categoryId}, #{supplierStar},
                #{deliveryModel}, #{minDeliveryAmount}, #{remark}, #{auditStatus}, #{reason}, #{createTime},
                #{updateTime}, #{isDelete})
    </insert>

    <delete id="delete" parameterType="java.lang.Long">
        UPDATE zg_supplier_info SET isDelete = 1, updateTime = NOW() WHERE id = #{id}
    </delete>


    <select id="selectById" parameterType="java.lang.Long" resultType="com.jmt.model.zg.entity.ZgSupplierInfo">
        select
        <include refid="Base_Column_List" />
        from zg_supplier_info
        where id = #{id} AND isDelete = 0
    </select>

    <select id="selectByUaaId" parameterType="java.lang.Long" resultType="com.jmt.model.zg.entity.ZgSupplierInfo">
        select
        <include refid="Base_Column_List" />
        from zg_supplier_info
        where uuaId = #{uuaId} AND isDelete = 0
    </select>

    <select id="getBaseSupplierInfoByUaaId" parameterType="java.lang.Long" resultType="com.jmt.model.zg.vo.ZgBaseSupplierInfoVO">
        select s.supplierNo,s.supplierName,s.categoryId,c.categoryName
        from zg_supplier_info s
        LEFT JOIN zg_product_category c ON s.categoryId = c.id AND c.isDelete = 0
        where s.uuaId = #{uuaId} AND s.isDelete = 0
    </select>




    <select id="countByUaaId" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select count(*)
        from zg_supplier_info
        where uuaId = #{uuaId} AND isDelete = 0
    </select>


    <select id="selectBySupplierNo" parameterType="java.lang.String" resultType="com.jmt.model.zg.entity.ZgSupplierInfo">
        select
        <include refid="Base_Column_List" />
        from zg_supplier_info
        where supplierNo = #{supplierNo} AND isDelete = 0
    </select>

    <update id="updateById" parameterType="com.jmt.model.zg.ZgSupplierInfo">
        update zg_supplier_info
        <set>
            <if test="uuaId != null">uuaId = #{uuaId},</if>
            <if test="supplierNo != null">supplierNo = #{supplierNo},</if>
            <if test="supplierName != null">supplierName = #{supplierName},</if>
            <if test="legalPerson != null">legalPerson = #{legalPerson},</if>
            <if test="bankName != null">bankName = #{bankName},</if>
            <if test="bankCardNo != null">bankCardNo = #{bankCardNo},</if>
            <if test="linkman != null">linkman = #{linkman},</if>
            <if test="creditCode != null">creditCode = #{creditCode},</if>
            <if test="nature != null">nature = #{nature},</if>
            <if test="telPhone != null">telPhone = #{telPhone},</if>
            <if test="country != null">country = #{country},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="area != null">area = #{area},</if>
            <if test="address != null">address = #{address},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="supplierPermit != null">supplierPermit = #{supplierPermit},</if>
            <if test="supplierLicense != null">supplierLicense = #{supplierLicense},</if>
            <if test="categoryId != null">categoryId = #{categoryId},</if>
            <if test="supplierStar != null">supplierStar = #{supplierStar},</if>
            <if test="deliveryModel != null">deliveryModel = #{deliveryModel},</if>
            <if test="minDeliveryAmount != null">minDeliveryAmount = #{minDeliveryAmount},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="auditStatus != null">auditStatus = #{auditStatus},</if>
            <if test="reason != null">reason = #{reason},</if>
            updateTime = NOW()
        </set>
        where id = #{id}
    </update>

    <sql id="SupplierInfoVO_Column_List">
        s.id, s.uuaId, s.supplierNo, s.supplierName, s.legalPerson, s.bankName, s.bankCardNo,
        s.linkman, s.creditCode, s.nature, s.telPhone, s.country, s.province, s.city, s.area, s.address,
        s.longitude, s.latitude, s.supplierPermit, s.supplierLicense, s.categoryId, s.supplierStar,
        s.deliveryModel, s.minDeliveryAmount, s.remark, s.auditStatus, s.reason, s.createTime,
        s.updateTime, s.isDelete,
        c.categoryName

    </sql>

    <select id="selectByCondition" parameterType="com.jmt.model.zg.entity.ZgSupplierInfo" resultType="com.jmt.model.zg.vo.ZgSupplierInfoVO">
        SELECT
        <include refid="SupplierInfoVO_Column_List" />
        FROM zg_supplier_info s
        LEFT JOIN zg_product_category c ON s.categoryId = c.id AND c.isDelete = 0
        <where>
            s.isDelete = 0
            <if test="supplierNo != null and supplierNo != ''">AND s.supplierNo LIKE CONCAT('%', #{supplierNo}, '%')</if>
            <if test="supplierName != null and supplierName != ''">AND s.supplierName LIKE CONCAT('%', #{supplierName}, '%')</if>
            <if test="categoryId != null">AND s.categoryId = #{categoryId}</if>
            <if test="nature != null">AND s.nature = #{nature}</if>
            <if test="deliveryModel != null">AND s.deliveryModel = #{deliveryModel}</if>
            <if test="auditStatus != null">AND s.auditStatus = #{auditStatus}</if>
        </where>
        ORDER BY createTime desc
    </select>




</mapper>