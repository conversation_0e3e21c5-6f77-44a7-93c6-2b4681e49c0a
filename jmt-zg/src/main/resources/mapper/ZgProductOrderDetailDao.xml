<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ZgProductOrderDetailDao">


    <sql id="Base_Column_List">
        id, orderNo, supplierNo, productNo, productName, buyUserId,
        originalPrice, currentPrice, quantity, createTime, updateTime, isDelete
    </sql>

    <insert id="insert" parameterType="com.jmt.model.zg.entity.ZgProductOrderDetail" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO zg_product_order_detail (
            id,orderNo, supplierNo, productNo, productName, buyUserId,
            originalPrice, currentPrice, quantity, createTime, updateTime, isDelete
        ) VALUES (
                     #{id}, #{orderNo}, #{supplierNo}, #{productNo}, #{productName}, #{buyUserId},
                     #{originalPrice}, #{currentPrice}, #{quantity},  NOW(), NOW(), 0
                 )
    </insert>

    <select id="selectByOrderNo" resultType="com.jmt.model.zg.entity.ZgProductOrderDetail">
        SELECT <include refid="Base_Column_List" />
        FROM zg_product_order_detail
        WHERE orderNo = #{orderNo} AND isDelete = 0
        ORDER BY createTime DESC
    </select>

    <select id="selectByUserId" resultType="com.jmt.model.zg.entity.ZgProductOrderDetail">
        SELECT <include refid="Base_Column_List" />
        FROM zg_product_order_detail
        WHERE buyUserId = #{buyUserId} AND isDelete = 0
        ORDER BY createTime DESC
    </select>

    <delete id="delete" parameterType="java.lang.Long">
        UPDATE zg_product_order_detail
        SET isDelete = 1, updateTime = NOW()
        WHERE id = #{id} AND isDelete = 0
    </delete>

    <select id="selectOrderDetailList" resultType="com.jmt.model.zg.vo.ProductOrderDetailVO">
        SELECT
            orderNo,
            supplierNo,
            productNo,
            productName,
            quantity,
            originalPrice,
            currentPrice
        FROM zg_product_order_detail
        WHERE orderNo = #{orderNo}
          AND isDelete = 0
    </select>
</mapper>