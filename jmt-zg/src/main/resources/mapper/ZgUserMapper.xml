<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ZgUserMapper">

    <resultMap id="BaseResultMap" type="com.jmt.model.zg.ZgUser">
        <id property="id" column="id" />
        <result property="uaaId" column="uaaId" />
        <result property="userName" column="userName" />
        <result property="nickName" column="nickName" />
        <result property="headImg" column="headImg" />
        <result property="sex" column="sex" />
        <result property="telPhone" column="telPhone" />
        <result property="email" column="email" />
        <result property="country" column="country" />
        <result property="province" column="province" />
        <result property="city" column="city" />
        <result property="address" column="address" />
        <result property="roleType" column="roleType" />
        <result property="isFirstLogin" column="isFirstLogin" />
        <result property="createTime" column="createTime" />
        <result property="updateTime" column="updateTime" />
        <result property="isDelete" column="isDelete" />
    </resultMap>

    <sql id="Base_Column_List">
        id, uaaId, userName, nickName, headImg, sex,
        telPhone, email, country, province, city,
        address, roleType, isFirstLogin, createTime, updateTime,
        isDelete
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from zg_user
        where id = #{id}
    </select>

    <select id="selectByuaaId" resultType="com.jmt.model.zg.ZgUser" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List" />
        from zg_user
        where uaaId = #{id}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from zg_user
        where id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.zg.ZgUser" useGeneratedKeys="true">
        insert into zg_user
        (id, uaaId, userName, nickName, headImg, sex,
         telPhone, email, country, province, city,
         address, roleType, isFirstLogin, createTime, updateTime,
         isDelete)
        values (#{id}, #{uaaId}, #{userName}, #{nickName}, #{headImg}, #{sex},
                #{telPhone}, #{email}, #{country}, #{province}, #{city},
                #{address}, #{roleType}, #{isFirstLogin}, #{createTime}, #{updateTime},
                #{isDelete})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.zg.ZgUser" useGeneratedKeys="true">
        insert into zg_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="uaaId != null">uaaId,</if>
            <if test="userName != null">userName,</if>
            <if test="nickName != null">nickName,</if>
            <if test="headImg != null">headImg,</if>
            <if test="sex != null">sex,</if>
            <if test="telPhone != null">telPhone,</if>
            <if test="email != null">email,</if>
            <if test="country != null">country,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="address != null">address,</if>
            <if test="roleType != null">roleType,</if>
            <if test="isFirstLogin != null">isFirstLogin,</if>
            <if test="createTime != null">createTime,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="isDelete != null">isDelete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="uaaId != null">#{uaaId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="headImg != null">#{headImg},</if>
            <if test="sex != null">#{sex},</if>
            <if test="telPhone != null">#{telPhone},</if>
            <if test="email != null">#{email},</if>
            <if test="country != null">#{country},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="address != null">#{address},</if>
            <if test="roleType != null">#{roleType},</if>
            <if test="isFirstLogin != null">#{isFirstLogin},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.zg.ZgUser">
        update zg_user
        <set>
            <if test="uaaId != null">
                uaaId = #{uaaId},
            </if>
            <if test="userName != null">
                userName = #{userName},
            </if>
            <if test="nickName != null">
                nickName = #{nickName},
            </if>
            <if test="headImg != null">
                headImg = #{headImg},
            </if>
            <if test="sex != null">
                sex = #{sex},
            </if>
            <if test="telPhone != null">
                telPhone = #{telPhone},
            </if>
            <if test="email != null">
                email = #{email},
            </if>
            <if test="country != null">
                country = #{country},
            </if>
            <if test="province != null">
                province = #{province},
            </if>
            <if test="city != null">
                city = #{city},
            </if>
            <if test="address != null">
                address = #{address},
            </if>
            <if test="roleType != null">
                roleType = #{roleType},
            </if>
            <if test="isFirstLogin != null">
                isFirstLogin = #{isFirstLogin},
            </if>
            <if test="createTime != null">
                createTime = #{createTime},
            </if>
            <if test="updateTime != null">
                updateTime = #{updateTime},
            </if>
            <if test="isDelete != null">
                isDelete = #{isDelete},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.jmt.model.zg.ZgUser">
        update zg_user
        set
            uaaId = #{uaaId},
            userName = #{userName},
            nickName = #{nickName},
            headImg = #{headImg},
            sex = #{sex},
            telPhone = #{telPhone},
            email = #{email},
            country = #{country},
            province = #{province},
            city = #{city},
            address = #{address},
            roleType = #{roleType},
            isFirstLogin = #{isFirstLogin},
            createTime = #{createTime},
            updateTime = #{updateTime},
            isDelete = #{isDelete}
        where id = #{id}
    </update>
</mapper>