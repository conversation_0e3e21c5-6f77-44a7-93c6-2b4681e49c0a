<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ZgProductMapper">

    <resultMap id="BaseResultMap" type="com.jmt.model.zg.ZgProduct">
            <id property="id" column="id" />
            <result property="merchantId" column="merchant_id" />
            <result property="merchantLicenseImg" column="merchant_license_img" />
            <result property="merchantQualityImg" column="merchant_quality_img" />
            <result property="productName" column="product_name" />
            <result property="productBarcode" column="product_barcode" />
            <result property="productBrand" column="product_brand" />
            <result property="productCode" column="product_code" />
            <result property="productWeight" column="product_weight" />
            <result property="packageType" column="package_type" />
            <result property="productLevel" column="product_level" />
            <result property="taxRate" column="tax_rate" />
            <result property="promotionType" column="promotion_type" />
            <result property="marketPrice" column="market_price" />
            <result property="salePrice" column="sale_price" />
            <result property="attachmentImg" column="attachment_img" />
            <result property="detailImg" column="detail_img" />
            <result property="status" column="status" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,merchant_id,merchant_license_img,merchant_quality_img,product_name,product_barcode,
        product_brand,product_code,product_weight,package_type,product_level,
        tax_rate,promotion_type,market_price,sale_price,attachment_img,
        detail_img,status,create_time,update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from zg_product
        where  id = #{id} 
    </select>
    <select id="listProduct" parameterType="com.jmt.model.zg.ZgProduct" resultType="com.jmt.model.zg.ZgProduct" >
        select
        <include refid="Base_Column_List" />
        from zg_product
        where  id = #{id}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from zg_product
        where  id = #{id} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.zg.ZgProduct" useGeneratedKeys="true">
        insert into zg_product
        ( id,merchant_id,merchant_license_img,merchant_quality_img,product_name,product_barcode,
        product_brand,product_code,product_weight,package_type,product_level,
        tax_rate,promotion_type,market_price,sale_price,attachment_img,
        detail_img,status,create_time,update_time)
        values (#{id},#{merchantId},#{merchantLicenseImg},#{merchantQualityImg},#{productName},#{productBarcode},
        #{productBrand},#{productCode},#{productWeight},#{packageType},#{productLevel},
        #{taxRate},#{promotionType},#{marketPrice},#{salePrice},#{attachmentImg},
        #{detailImg},#{status},#{createTime},#{updateTime})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.zg.ZgProduct" useGeneratedKeys="true">
        insert into zg_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="merchantId != null">merchant_id,</if>
                <if test="merchantLicenseImg != null">merchant_license_img,</if>
                <if test="merchantQualityImg != null">merchant_quality_img,</if>
                <if test="productName != null">product_name,</if>
                <if test="productBarcode != null">product_barcode,</if>
                <if test="productBrand != null">product_brand,</if>
                <if test="productCode != null">product_code,</if>
                <if test="productWeight != null">product_weight,</if>
                <if test="packageType != null">package_type,</if>
                <if test="productLevel != null">product_level,</if>
                <if test="taxRate != null">tax_rate,</if>
                <if test="promotionType != null">promotion_type,</if>
                <if test="marketPrice != null">market_price,</if>
                <if test="salePrice != null">sale_price,</if>
                <if test="attachmentImg != null">attachment_img,</if>
                <if test="detailImg != null">detail_img,</if>
                <if test="status != null">status,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id},</if>
                <if test="merchantId != null">#{merchantId},</if>
                <if test="merchantLicenseImg != null">#{merchantLicenseImg},</if>
                <if test="merchantQualityImg != null">#{merchantQualityImg},</if>
                <if test="productName != null">#{productName},</if>
                <if test="productBarcode != null">#{productBarcode},</if>
                <if test="productBrand != null">#{productBrand},</if>
                <if test="productCode != null">#{productCode},</if>
                <if test="productWeight != null">#{productWeight},</if>
                <if test="packageType != null">#{packageType},</if>
                <if test="productLevel != null">#{productLevel},</if>
                <if test="taxRate != null">#{taxRate},</if>
                <if test="promotionType != null">#{promotionType},</if>
                <if test="marketPrice != null">#{marketPrice},</if>
                <if test="salePrice != null">#{salePrice},</if>
                <if test="attachmentImg != null">#{attachmentImg},</if>
                <if test="detailImg != null">#{detailImg},</if>
                <if test="status != null">#{status},</if>
                <if test="createTime != null">#{createTime},</if>
                <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.zg.ZgProduct">
        update zg_product
        <set>
                <if test="merchantId != null">
                    merchant_id = #{merchantId},
                </if>
                <if test="merchantLicenseImg != null">
                    merchant_license_img = #{merchantLicenseImg},
                </if>
                <if test="merchantQualityImg != null">
                    merchant_quality_img = #{merchantQualityImg},
                </if>
                <if test="productName != null">
                    product_name = #{productName},
                </if>
                <if test="productBarcode != null">
                    product_barcode = #{productBarcode},
                </if>
                <if test="productBrand != null">
                    product_brand = #{productBrand},
                </if>
                <if test="productCode != null">
                    product_code = #{productCode},
                </if>
                <if test="productWeight != null">
                    product_weight = #{productWeight},
                </if>
                <if test="packageType != null">
                    package_type = #{packageType},
                </if>
                <if test="productLevel != null">
                    product_level = #{productLevel},
                </if>
                <if test="taxRate != null">
                    tax_rate = #{taxRate},
                </if>
                <if test="promotionType != null">
                    promotion_type = #{promotionType},
                </if>
                <if test="marketPrice != null">
                    market_price = #{marketPrice},
                </if>
                <if test="salePrice != null">
                    sale_price = #{salePrice},
                </if>
                <if test="attachmentImg != null">
                    attachment_img = #{attachmentImg},
                </if>
                <if test="detailImg != null">
                    detail_img = #{detailImg},
                </if>
                <if test="status != null">
                    status = #{status},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime},
                </if>
        </set>
        where   id = #{id} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.jmt.model.zg.ZgProduct">
        update zg_product
        set 
            merchant_id =  #{merchantId},
            merchant_license_img =  #{merchantLicenseImg},
            merchant_quality_img =  #{merchantQualityImg},
            product_name =  #{productName},
            product_barcode =  #{productBarcode},
            product_brand =  #{productBrand},
            product_code =  #{productCode},
            product_weight =  #{productWeight},
            package_type =  #{packageType},
            product_level =  #{productLevel},
            tax_rate =  #{taxRate},
            promotion_type =  #{promotionType},
            market_price =  #{marketPrice},
            sale_price =  #{salePrice},
            attachment_img =  #{attachmentImg},
            detail_img =  #{detailImg},
            status =  #{status},
            create_time =  #{createTime},
            update_time =  #{updateTime}
        where   id = #{id} 
    </update>
</mapper>
