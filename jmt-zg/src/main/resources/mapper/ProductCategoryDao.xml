<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ProductCategoryDao">

    <select id="selectCategory" resultType="com.jmt.model.zg.ZgProductCategoryVo">
        SELECT t.id,t.categoryNo,categoryName,t.categoryPicture,t.parentId FROM zg_product_category t WHERE t.isDelete=0 ORDER BY t.id
    </select>

    <update id="updateById" parameterType="com.jmt.model.zg.ZgProductCategoryVo">
        UPDATE zg_product_category set categoryPicture = #{categoryPicture},updateTime = NOW()
        WHERE id = #{id} AND isDelete = 0
    </update>

    <!-- 查询一级品类 -->
    <select id="selectLevel1Categories" resultType="com.jmt.model.zg.ZgProductCategoryVo">
        SELECT id, categoryNo, categoryName, categoryPicture, parentId
        FROM zg_product_category
        WHERE parentId =0 AND isDelete = 0
        ORDER BY id
    </select>

    <!-- 根据父ID查询子品类 -->
    <select id="selectSubCategoriesByParentId" resultType="com.jmt.model.zg.vo.ZgProductCategoryChildVo">
        SELECT id, categoryNo, categoryName
        FROM zg_product_category
        WHERE parentId = #{parentId} AND isDelete = 0
        ORDER BY id
    </select>

    <insert id="insert" parameterType="com.jmt.model.zg.entity.ZgProductCategory" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO zg_product_category (
            id,categoryNo, categoryName, categoryPicture, parentId,
            createTime, updateTime,  isDelete
        ) VALUES (
                     #{id},#{categoryNo}, #{categoryName}, #{categoryPicture}, #{parentId},
                     NOW(), NOW(), 0
                 )
    </insert>
</mapper>
