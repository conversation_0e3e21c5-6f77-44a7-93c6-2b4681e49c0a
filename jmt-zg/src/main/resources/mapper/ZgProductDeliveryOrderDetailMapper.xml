<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ZgProductDeliveryOrderDetailMapper">

    <resultMap id="BaseResultMap" type="com.jmt.model.zg.ZgProductDeliveryOrderDetail">
        <id property="id" column="id" />
        <result property="productDeliveryOrderNo" column="productDeliveryOrderNo" />
        <result property="productOrderNo" column="productOrderNo" />
        <result property="createTime" column="createTime" />
        <result property="updateTime" column="updateTime" />
        <result property="isDelete" column="isDelete" />
    </resultMap>

    <sql id="Base_Column_List">
        id,productDeliveryOrderNo,productOrderNo,createTime,updateTime,isDelete
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from zg_product_delivery_order_detail
        where  id = #{id} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from zg_product_delivery_order_detail
        where  id = #{id} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.zg.ZgProductDeliveryOrderDetail" useGeneratedKeys="true">
        insert into zg_product_delivery_order_detail
        ( id,productDeliveryOrderNo,productOrderNo,createTime,updateTime,isDelete)
        values (#{id},#{productdeliveryorderno},#{productorderno},#{createtime},#{updatetime},#{isdelete})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.zg.ZgProductDeliveryOrderDetail" useGeneratedKeys="true">
        insert into zg_product_delivery_order_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="productdeliveryorderno != null">productDeliveryOrderNo,</if>
                <if test="productorderno != null">productOrderNo,</if>
                <if test="createtime != null">createTime,</if>
                <if test="updatetime != null">updateTime,</if>
                <if test="isdelete != null">isDelete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id},</if>
                <if test="productdeliveryorderno != null">#{productdeliveryorderno},</if>
                <if test="productorderno != null">#{productorderno},</if>
                <if test="createtime != null">#{createtime},</if>
                <if test="updatetime != null">#{updatetime},</if>
                <if test="isdelete != null">#{isdelete},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.zg.ZgProductDeliveryOrderDetail">
        update zg_product_delivery_order_detail
        <set>
                <if test="productdeliveryorderno != null">
                    productDeliveryOrderNo = #{productdeliveryorderno},
                </if>
                <if test="productorderno != null">
                    productOrderNo = #{productorderno},
                </if>
                <if test="createtime != null">
                    createTime = #{createtime},
                </if>
                <if test="updatetime != null">
                    updateTime = #{updatetime},
                </if>
                <if test="isdelete != null">
                    isDelete = #{isdelete},
                </if>
        </set>
        where   id = #{id} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.jmt.model.zg.ZgProductDeliveryOrderDetail">
        update zg_product_delivery_order_detail
        set 
            productDeliveryOrderNo =  #{productdeliveryorderno},
            productOrderNo =  #{productorderno},
            createTime =  #{createtime},
            updateTime =  #{updatetime},
            isDelete =  #{isdelete}
        where   id = #{id} 
    </update>
</mapper>
