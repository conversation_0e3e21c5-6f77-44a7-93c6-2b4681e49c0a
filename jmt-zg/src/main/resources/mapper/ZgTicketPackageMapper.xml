<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ZgTicketPackageMapper">

    <resultMap id="BaseResultMap" type="com.jmt.model.zg.ZgTicketPackage">
        <id property="id" column="id" />
        <result property="buyUserId" column="buyUserId" />
        <result property="ticketSeqNo" column="ticketSeqNo" />
        <result property="supplierNo" column="supplierNo" />
        <result property="supplierName" column="supplierName" />
        <result property="ticketNo" column="ticketNo" />
        <result property="ticketName" column="ticketName" />
        <result property="originalPrice" column="originalPrice" />
        <result property="currentPrice" column="currentPrice" />
        <result property="prepayAmount" column="prepayAmount" />
        <result property="postpayAmount" column="postpayAmount" />
        <result property="effectiveStartTime" column="effectiveStartTime" />
        <result property="effectiveEndTime" column="effectiveEndTime" />
        <result property="productPicture" column="productPicture" />
        <result property="productName" column="productName" />
        <result property="productSize" column="productSize" />
        <result property="productNum" column="productNum" />
        <result property="tStatus" column="tStatus" /> <!-- 注意：column与property映射需与数据库字段一致 -->
        <result property="createTime" column="createTime" />
        <result property="updateTime" column="updateTime" />
        <result property="isDelete" column="isDelete" />
    </resultMap>

    <sql id="Base_Column_List">
        id, buyUserId, ticketSeqNo, supplierNo, supplierName, ticketNo,
        ticketName, originalPrice, currentPrice, prepayAmount, postpayAmount,
        effectiveStartTime, effectiveEndTime, productPicture, productName, productSize,
        productNum, tStatus, createTime, updateTime, isDelete
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from zg_ticket_pakage
        where id = #{id}
    </select>

    <select id="selectByTicketNoAndBuyUserId" resultType="com.jmt.model.zg.ZgTicketPackage">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        zg_ticket_pakage
        WHERE
        ticketSeqNo = #{ticketSeqNo}
        AND buyUserId = #{buyUserId}
    </select>
    <select id="selectByUserIdAndTicketNo" resultType="com.jmt.model.zg.ZgTicketPackage">
        SELECT
            id,
            buyUserId,
            ticketSeqNo, -- 目标字段：囤货券实例序列号
            supplierNo,
            supplierName,
            ticketNo,
            tStatus,
            isDelete
        FROM
            zg_ticket_pakage
        WHERE
            buyUserId = #{buyUserId} -- 用户ID
          AND ticketNo = #{ticketNo} -- 囤货券基础编号
          AND tStatus = 0 -- 状态为未使用
          AND isDelete = 0 -- 未删除
        LIMIT 1 -- 只取一条（若用户持有多个，可根据业务调整，如按创建时间取最早的）
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from zg_ticket_pakage  <!-- 修正表名拼写 -->
        where id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.zg.ZgTicketPackage" useGeneratedKeys="true">
        insert into zg_ticket_pakage
        (id, buyUserId, ticketSeqNo, supplierNo, supplierName, ticketNo,
        ticketName, originalPrice, currentPrice, prepayAmount, postpayAmount,
        effectiveStartTime, effectiveEndTime, productPicture, productName, productSize,
        productNum, tStatus, createTime, updateTime, isDelete)
        values (#{id}, #{buyUserId}, #{ticketSeqNo}, #{supplierNo}, #{supplierName}, #{ticketNo},
        #{ticketName}, #{originalPrice}, #{currentPrice}, #{prepayAmount}, #{postpayAmount},
        #{effectiveStartTime}, #{effectiveEndTime}, #{productPicture}, #{productName}, #{productSize},
        #{productNum}, #{tStatus}, #{createTime}, #{updateTime}, #{isDelete})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.zg.ZgTicketPackage" useGeneratedKeys="true">
        insert into zg_ticket_pakage  <!-- 修正表名拼写 -->
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="buyUserId != null">buyUserId,</if>
            <if test="ticketSeqNo != null">ticketSeqNo,</if>
            <if test="supplierNo != null">supplierNo,</if>
            <if test="supplierName != null">supplierName,</if>
            <if test="ticketNo != null">ticketNo,</if>
            <if test="ticketName != null">ticketName,</if>
            <if test="originalPrice != null">originalPrice,</if>
            <if test="currentPrice != null">currentPrice,</if>
            <if test="prepayAmount != null">prepayAmount,</if>
            <if test="postpayAmount != null">postpayAmount,</if>
            <if test="effectiveStartTime != null">effectiveStartTime,</if>
            <if test="effectiveEndTime != null">effectiveEndTime,</if>
            <if test="productPicture != null">productPicture,</if>
            <if test="productName != null">productName,</if>
            <if test="productSize != null">productSize,</if>
            <if test="productNum != null">productNum,</if>
            <if test="tStatus != null">tStatus,</if>  <!-- 对应数据库字段tStatus -->
            <if test="createTime != null">createTime,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="isDelete != null">isDelete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="buyUserId != null">#{buyUserId},</if>
            <if test="ticketSeqNo != null">#{ticketSeqNo},</if>
            <if test="supplierNo != null">#{supplierNo},</if>
            <if test="supplierName != null">#{supplierName},</if>
            <if test="ticketNo != null">#{ticketNo},</if>
            <if test="ticketName != null">#{ticketName},</if>
            <if test="originalPrice != null">#{originalPrice},</if>
            <if test="currentPrice != null">#{currentPrice},</if>
            <if test="prepayAmount != null">#{prepayAmount},</if>
            <if test="postpayAmount != null">#{postpayAmount},</if>
            <if test="effectiveStartTime != null">#{effectiveStartTime},</if>
            <if test="effectiveEndTime != null">#{effectiveEndTime},</if>
            <if test="productPicture != null">#{productPicture},</if>
            <if test="productName != null">#{productName},</if>
            <if test="productSize != null">#{productSize},</if>
            <if test="productNum != null">#{productNum},</if>
            <if test="tStatus != null">#{tStatus},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.zg.ZgTicketPackage">
        update zg_ticket_pakage  <!-- 修正表名拼写 -->
        <set>
            <if test="buyUserId != null">
                buyUserId = #{buyUserId},
            </if>
            <if test="ticketSeqNo != null">
                ticketSeqNo = #{ticketSeqNo},
            </if>
            <if test="supplierNo != null">
                supplierNo = #{supplierNo},
            </if>
            <if test="supplierName != null">
                supplierName = #{supplierName},
            </if>
            <if test="ticketNo != null">
                ticketNo = #{ticketNo},
            </if>
            <if test="ticketName != null">
                ticketName = #{ticketName},
            </if>
            <if test="originalPrice != null">
                originalPrice = #{originalPrice},
            </if>
            <if test="currentPrice != null">
                currentPrice = #{currentPrice},
            </if>
            <if test="prepayAmount != null">
                prepayAmount = #{prepayAmount},
            </if>
            <if test="postpayAmount != null">
                postpayAmount = #{postpayAmount},
            </if>
            <if test="effectiveStartTime != null">
                effectiveStartTime = #{effectiveStartTime},
            </if>
            <if test="effectiveEndTime != null">
                effectiveEndTime = #{effectiveEndTime},
            </if>
            <if test="productPicture != null">
                productPicture = #{productPicture},
            </if>
            <if test="productName != null">
                productName = #{productName},
            </if>
            <if test="productSize != null">
                productSize = #{productSize},
            </if>
            <if test="productNum != null">
                productNum = #{productNum},
            </if>
            <if test="tStatus != null">
                tStatus = #{tStatus},  <!-- 对应数据库字段tStatus -->
            </if>
            <if test="createTime != null">
                createTime = #{createTime},
            </if>
            <if test="updateTime != null">
                updateTime = #{updateTime},
            </if>
            <if test="isDelete != null">
                isDelete = #{isDelete},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.jmt.model.zg.ZgTicketPackage">
        update zg_ticket_pakage  <!-- 修正表名拼写 -->
        set
        buyUserId = #{buyUserId},
        ticketSeqNo = #{ticketSeqNo},
        supplierNo = #{supplierNo},
        supplierName = #{supplierName},
        ticketNo = #{ticketNo},
        ticketName = #{ticketName},
        originalPrice = #{originalPrice},
        currentPrice = #{currentPrice},
        prepayAmount = #{prepayAmount},
        postpayAmount = #{postpayAmount},
        effectiveStartTime = #{effectiveStartTime},
        effectiveEndTime = #{effectiveEndTime},
        productPicture = #{productPicture},
        productName = #{productName},
        productSize = #{productSize},
        productNum = #{productNum},
        tStatus = #{tStatus},  <!-- 对应数据库字段tStatus -->
        createTime = #{createTime},
        updateTime = #{updateTime},
        isDelete = #{isDelete}
        where id = #{id}
    </update>
</mapper>