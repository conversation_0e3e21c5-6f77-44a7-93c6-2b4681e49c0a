<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ZgProductDeliveryOrderMapper">

    <resultMap id="BaseResultMap" type="com.jmt.model.zg.ZgProductDeliveryOrder">
        <id property="id" column="id" />
        <result property="orderNo" column="orderNo" />
        <result property="orderName" column="orderName" />
        <result property="driverNo" column="driverNo" />
        <result property="productTotal" column="productTotal" />
        <result property="orderCount" column="orderCount" />
        <result property="sellerCount" column="sellerCount" />
        <result property="buyerCount" column="buyerCount" />
        <result property="orderStatus" column="orderStatus" />
        <result property="reason" column="reason" />
        <result property="takeQrCode" column="takeQrCode" />
        <result property="createTime" column="createTime" />
        <result property="updateTime" column="updateTime" />
        <result property="isDelete" column="isDelete" />
    </resultMap>

    <sql id="Base_Column_List">
        id, orderNo, orderName, driverNo, productTotal, orderCount,
        sellerCount, buyerCount, orderStatus, reason, takeQrCode,
        createTime, updateTime, isDelete
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from zg_product_delivery_order
        where id = #{id}
    </select>

    <select id="getDeliveryOrderPage" resultType="com.jmt.model.zg.ZgProductDeliveryOrder">
        SELECT
        <include refid="Base_Column_List" />  <!-- 使用统一列片段，避免字段冗余或遗漏 -->
        FROM zg_product_delivery_order
    </select>
    <select id="selectByOrderNo" resultType="com.jmt.model.zg.ZgProductDeliveryOrder">
        select
        <include refid="Base_Column_List" />
        from zg_product_delivery_order
        where orderNo = #{orderNo}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from zg_product_delivery_order
        where id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.zg.ZgProductDeliveryOrder" useGeneratedKeys="true">
        insert into zg_product_delivery_order
        (id, orderNo, orderName, driverNo, productTotal, orderCount,
         sellerCount, buyerCount, orderStatus, reason, takeQrCode,
         createTime, updateTime, isDelete)
        values (#{id}, #{orderNo}, #{orderName}, #{driverNo}, #{productTotal}, #{orderCount},
                #{sellerCount}, #{buyerCount}, #{orderStatus}, #{reason}, #{takeQrCode},
                #{createTime}, #{updateTime}, #{isDelete})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.zg.ZgProductDeliveryOrder" useGeneratedKeys="true">
        insert into zg_product_delivery_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="orderNo != null">orderNo,</if>
            <if test="orderName != null">orderName,</if>
            <if test="driverNo != null">driverNo,</if>
            <if test="productTotal != null">productTotal,</if>
            <if test="orderCount != null">orderCount,</if>
            <if test="sellerCount != null">sellerCount,</if>
            <if test="buyerCount != null">buyerCount,</if>
            <if test="orderStatus != null">orderStatus,</if>
            <if test="reason != null">reason,</if>
            <if test="takeQrCode != null">takeQrCode,</if>
            <if test="createTime != null">createTime,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="isDelete != null">isDelete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="orderName != null">#{orderName},</if>
            <if test="driverNo != null">#{driverNo},</if>
            <if test="productTotal != null">#{productTotal},</if>
            <if test="orderCount != null">#{orderCount},</if>
            <if test="sellerCount != null">#{sellerCount},</if>
            <if test="buyerCount != null">#{buyerCount},</if>
            <if test="orderStatus != null">#{orderStatus},</if>
            <if test="reason != null">#{reason},</if>
            <if test="takeQrCode != null">#{takeQrCode},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.zg.ZgProductDeliveryOrder">
        update zg_product_delivery_order
        <set>
            <if test="orderNo != null">
                orderNo = #{orderNo},
            </if>
            <if test="orderName != null">
                orderName = #{orderName},
            </if>
            <if test="driverNo != null">
                driverNo = #{driverNo},
            </if>
            <if test="productTotal != null">
                productTotal = #{productTotal},
            </if>
            <if test="orderCount != null">
                orderCount = #{orderCount},
            </if>
            <if test="sellerCount != null">
                sellerCount = #{sellerCount},
            </if>
            <if test="buyerCount != null">
                buyerCount = #{buyerCount},
            </if>
            <if test="orderStatus != null">
                orderStatus = #{orderStatus},
            </if>
            <if test="reason != null">
                reason = #{reason},
            </if>
            <if test="takeQrCode != null">
                takeQrCode = #{takeQrCode},
            </if>
            <if test="createTime != null">
                createTime = #{createTime},
            </if>
            <if test="updateTime != null">
                updateTime = #{updateTime},
            </if>
            <if test="isDelete != null">
                isDelete = #{isDelete},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.jmt.model.zg.ZgProductDeliveryOrder">
        update zg_product_delivery_order
        set
            orderNo = #{orderNo},
            orderName = #{orderName},
            driverNo = #{driverNo},
            productTotal = #{productTotal},
            orderCount = #{orderCount},
            sellerCount = #{sellerCount},
            buyerCount = #{buyerCount},
            orderStatus = #{orderStatus},
            reason = #{reason},
            takeQrCode = #{takeQrCode},
            createTime = #{createTime},
            updateTime = #{updateTime},
            isDelete = #{isDelete}
        where id = #{id}
    </update>
</mapper>