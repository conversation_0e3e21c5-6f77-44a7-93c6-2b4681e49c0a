<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ZgTicketOrderDetailMapper">

    <resultMap id="BaseResultMap" type="com.jmt.model.zg.ZgTicketOrderDetail">
        <!-- 主键映射 -->
        <id property="id" column="id" />

        <!-- 关联键映射 -->
        <result property="orderNo" column="orderNo" />
        <result property="supplierNo" column="supplierNo" />
        <result property="ticketNo" column="ticketNo" />

        <!-- 基础信息映射 -->
        <result property="ticketName" column="ticketName" />
        <result property="buyUserId" column="buyUserId" />

        <!-- 价格信息映射 -->
        <result property="originalPrice" column="originalPrice" />
        <result property="currentPrice" column="currentPrice" />
        <result property="prepayAmount" column="prepayAmount" />
        <result property="postpayAmount" column="postpayAmount" />

        <!-- 业务信息映射 -->
        <result property="quantity" column="quantity" />

        <!-- 时间戳映射 -->
        <result property="createTime" column="createTime" />
        <result property="updateTime" column="updateTime" />

        <!-- 逻辑删除标志 -->
        <result property="isDelete" column="isDelete" />
    </resultMap>

    <sql id="Base_Column_List">
        id, orderNo, supplierNo, ticketNo, ticketName, buyUserId,
        originalPrice, currentPrice, prepayAmount, postpayAmount, quantity,
        createTime, updateTime, isDelete
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from zg_ticket_order_detail
        where id = #{id}
    </select>
    <select id="selectByOrderNo" resultType="com.jmt.model.zg.ZgTicketOrderDetail">
        SELECT
            id,
            orderNo,
            supplierNo,
            ticketNo,
            ticketName,
            buyUserId,
            originalPrice,
            currentPrice,
            prepayAmount,
            postpayAmount,
            quantity,
            createTime,
            updateTime,
            isDelete
        FROM
            zg_ticket_order_detail
        WHERE
            orderNo = #{orderNo}
    </select>
    <select id="selectByTicketNo" resultType="com.jmt.model.zg.ZgTicketOrderDetail">
        SELECT
            id,
            orderNo,
            supplierNo,
            ticketNo,
            ticketName,
            buyUserId,
            originalPrice,
            currentPrice,
            prepayAmount,
            postpayAmount,
            quantity,
            createTime,
            updateTime,
            isDelete
        FROM
            zg_ticket_order_detail
        WHERE
            ticketNo = #{ticketNo}
    </select>


    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from zg_ticket_order_detail
        where id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.zg.ZgTicketOrderDetail" useGeneratedKeys="true">
        insert into zg_ticket_order_detail
        (id, orderNo, supplierNo, ticketNo, ticketName, buyUserId,
         originalPrice, currentPrice, prepayAmount, postpayAmount, quantity,
         createTime, updateTime, isDelete)
        values (#{id}, #{orderNo}, #{supplierNo}, #{ticketNo}, #{ticketName}, #{buyUserId},
                #{originalPrice}, #{currentPrice}, #{prepayAmount}, #{postpayAmount}, #{quantity},
                #{createTime}, #{updateTime}, #{isDelete})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.zg.ZgTicketOrderDetail" useGeneratedKeys="true">
        insert into zg_ticket_order_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="orderNo != null">orderNo,</if>
            <if test="supplierNo != null">supplierNo,</if>
            <if test="ticketNo != null">ticketNo,</if>
            <if test="ticketName != null">ticketName,</if>
            <if test="buyUserId != null">buyUserId,</if>
            <if test="originalPrice != null">originalPrice,</if>
            <if test="currentPrice != null">currentPrice,</if>
            <if test="prepayAmount != null">prepayAmount,</if>
            <if test="postpayAmount != null">postpayAmount,</if>
            <if test="quantity != null">quantity,</if>
            <if test="createTime != null">createTime,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="isDelete != null">isDelete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="supplierNo != null">#{supplierNo},</if>
            <if test="ticketNo != null">#{ticketNo},</if>
            <if test="ticketName != null">#{ticketName},</if>
            <if test="buyUserId != null">#{buyUserId},</if>
            <if test="originalPrice != null">#{originalPrice},</if>
            <if test="currentPrice != null">#{currentPrice},</if>
            <if test="prepayAmount != null">#{prepayAmount},</if>
            <if test="postpayAmount != null">#{postpayAmount},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.zg.ZgTicketOrderDetail">
        update zg_ticket_order_detail
        <set>
            <if test="orderNo != null">
                orderNo = #{orderNo},
            </if>
            <if test="supplierNo != null">
                supplierNo = #{supplierNo},
            </if>
            <if test="ticketNo != null">
                ticketNo = #{ticketNo},
            </if>
            <if test="ticketName != null">
                ticketName = #{ticketName},
            </if>
            <if test="buyUserId != null">
                buyUserId = #{buyUserId},
            </if>
            <if test="originalPrice != null">
                originalPrice = #{originalPrice},
            </if>
            <if test="currentPrice != null">
                currentPrice = #{currentPrice},
            </if>
            <if test="prepayAmount != null">
                prepayAmount = #{prepayAmount},
            </if>
            <if test="postpayAmount != null">
                postpayAmount = #{postpayAmount},
            </if>
            <if test="quantity != null">
                quantity = #{quantity},
            </if>
            <if test="createTime != null">
                createTime = #{createTime},
            </if>
            <if test="updateTime != null">
                updateTime = #{updateTime},
            </if>
            <if test="isDelete != null">
                isDelete = #{isDelete},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.jmt.model.zg.ZgTicketOrderDetail">
        update zg_ticket_order_detail
        set
            orderNo = #{orderNo},
            supplierNo = #{supplierNo},
            ticketNo = #{ticketNo},
            ticketName = #{ticketName},
            buyUserId = #{buyUserId},
            originalPrice = #{originalPrice},
            currentPrice = #{currentPrice},
            prepayAmount = #{prepayAmount},
            postpayAmount = #{postpayAmount},
            quantity = #{quantity},
            createTime = #{createTime},
            updateTime = #{updateTime},
            isDelete = #{isDelete}
        where id = #{id}
    </update>

    <update id="updateCartStatus">
        UPDATE zg_ticket_cart
        SET
            isDelete = 1,  -- 标记为已删除（逻辑删除）
            updateTime = #{date}  -- 更新时间为当前操作时间
        WHERE
            buyUserId = #{buyUserId}  -- 匹配用户ID
          AND ticketNo = #{ticketNo}  -- 匹配囤货券编号
    </update>
</mapper>