<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ZgProductOrderMapper">

    <resultMap id="BaseResultMap" type="com.jmt.model.zg.ZgProductOrder">
        <id property="id" column="id" />
        <result property="buyUserId" column="buyUserId" />
        <result property="addressId" column="addressId" />
        <result property="orderNo" column="orderNo" />
        <result property="orderName" column="orderName" />
        <result property="supplierNo" column="supplierNo" />
        <result property="supplierName" column="supplierName" />
        <result property="ticketSeqNo" column="ticketSeqNo" />
        <result property="ticketNo" column="ticketNo" />
        <result property="ticketName" column="ticketName" />
        <result property="originalPrice" column="originalPrice" />
        <result property="currentPrice" column="currentPrice" />
        <result property="prepayAmount" column="prepayAmount" />
        <result property="postpayAmount" column="postpayAmount" />
        <result property="effectiveStartTime" column="effectiveStartTime" />
        <result property="effectiveEndTime" column="effectiveEndTime" />
        <result property="productPicture" column="productPicture" />
        <result property="productName" column="productName" />
        <result property="productSize" column="productSize" />
        <result property="productNum" column="productNum" />
        <result property="payType" column="payType" />
        <result property="orderStatus" column="orderStatus" />
        <result property="usedQrCode" column="usedQrCode" />
        <result property="createTime" column="createTime" />
        <result property="updateTime" column="updateTime" />
        <result property="isDelete" column="isDelete" />
    </resultMap>

    <sql id="Base_Column_List">
        id, buyUserId, addressId, orderNo, orderName, supplierNo,
        supplierName, ticketSeqNo, ticketNo, ticketName, originalPrice,
        currentPrice, prepayAmount, postpayAmount, effectiveStartTime, effectiveEndTime,
        productPicture, productName, productSize, productNum, payType,
        orderStatus, usedQrCode, createTime, updateTime, isDelete
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from zg_product_order
        where id = #{id}
    </select>

    <select id="getProductOrderPage" parameterType="com.jmt.model.zg.ZgProductOrder" resultType="com.jmt.model.zg.ZgProductOrder">
        SELECT
        <include refid="Base_Column_List" />
        FROM zg_product_order t
        <where>
            <!-- 主键ID条件 -->
            <if test="id != null">
                AND t.id = #{id}
            </if>

            <!-- 下单人统一账号uaaId条件 -->
            <if test="buyUserId != null">
                AND t.buyUserId = #{buyUserId}
            </if>

            <!-- 配送地址id条件 -->
            <if test="addressId != null">
                AND t.addressId = #{addressId}
            </if>

            <!-- 订单编号条件 -->
            <if test="orderNo != null and orderNo != ''">
                AND t.orderNo = #{orderNo}
            </if>

            <!-- 订单名称（支持模糊查询） -->
            <if test="orderName != null and orderName != ''">
                AND t.orderName LIKE CONCAT('%', #{orderName}, '%')
            </if>

            <!-- 供应商编号条件 -->
            <if test="supplierNo != null and supplierNo != ''">
                AND t.supplierNo = #{supplierNo}
            </if>

            <!-- 供应商名称（支持模糊查询） -->
            <if test="supplierName != null and supplierName != ''">
                AND t.supplierName LIKE CONCAT('%', #{supplierName}, '%')
            </if>

            <!-- 囤货券实例序列号条件 -->
            <if test="ticketSeqNo != null and ticketSeqNo != ''">
                AND t.ticketSeqNo = #{ticketSeqNo}
            </if>

            <!-- 囤货券编号条件 -->
            <if test="ticketNo != null and ticketNo != ''">
                AND t.ticketNo = #{ticketNo}
            </if>

            <!-- 囤货券名称（支持模糊查询） -->
            <if test="ticketName != null and ticketName != ''">
                AND t.ticketName LIKE CONCAT('%', #{ticketName}, '%')
            </if>

            <!-- 原价条件 -->
            <if test="originalPrice != null">
                AND t.originalPrice = #{originalPrice}
            </if>

            <!-- 现价条件 -->
            <if test="currentPrice != null">
                AND t.currentPrice = #{currentPrice}
            </if>

            <!-- 预付金额条件 -->
            <if test="prepayAmount != null">
                AND t.prepayAmount = #{prepayAmount}
            </if>

            <!-- 尾款金额条件 -->
            <if test="postpayAmount != null">
                AND t.postpayAmount = #{postpayAmount}
            </if>

            <!-- 有效期开始时间范围（大于等于） -->
            <if test="effectiveStartTime != null">
                AND t.effectiveStartTime >= #{effectiveStartTime}
            </if>

            <!-- 有效期结束时间范围（小于等于） -->
            <if test="effectiveEndTime != null">
                AND t.effectiveEndTime &amp;lt; #{effectiveEndTime}
            </if>

            <!-- 商品图片条件 -->
            <if test="productPicture != null and productPicture != ''">
                AND t.productPicture = #{productPicture}
            </if>

            <!-- 商品名称（支持模糊查询） -->
            <if test="productName != null and productName != ''">
                AND t.productName LIKE CONCAT('%', #{productName}, '%')
            </if>

            <!-- 商品规格条件 -->
            <if test="productSize != null and productSize != ''">
                AND t.productSize = #{productSize}
            </if>

            <!-- 商品数量条件 -->
            <if test="productNum != null">
                AND t.productNum = #{productNum}
            </if>

            <!-- 支付方式条件 -->
            <if test="payType != null">
                AND t.payType = #{payType}
            </if>

            <!-- 订单状态条件 -->
            <if test="orderStatus != null">
                AND t.orderStatus = #{orderStatus}
            </if>

            <!-- 核销二维码条件 -->
            <if test="usedQrCode != null and usedQrCode != ''">
                AND t.usedQrCode = #{usedQrCode}
            </if>

            <!-- 创建时间范围（大于等于） -->
            <if test="createTime != null">
                AND t.createTime >= #{createTime}
            </if>

            <!-- 更新时间范围（小于等于） -->
            <if test="updateTime != null">
                AND t.updateTime &amp;lt; #{updateTime}
            </if>

            <!-- 逻辑删除状态 -->
            <if test="isDelete != null">
                AND t.isDelete = #{isDelete}
            </if>
        </where>
        <!-- 按创建时间降序排序 -->
        ORDER BY t.createTime DESC
    </select>
    <!-- 根据订单编号查询商品订单 -->
    <select id="selectByOrderNo" resultType="com.jmt.model.zg.ZgProductOrder">
        SELECT
        id,
        buyUserId,
        addressId,
        orderNo,
        orderName,
        supplierNo,
        supplierName,
        ticketSeqNo,
        ticketNo,
        ticketName,
        originalPrice,
        currentPrice,
        prepayAmount,
        postpayAmount,
        effectiveStartTime,
        effectiveEndTime,
        productPicture,
        productName,
        productSize,
        productNum,
        payType,
        orderStatus,
        usedQrCode,
        createTime,
        updateTime,
        isDelete
        FROM zg_product_order
        WHERE orderNo = #{orderNo}
        AND isDelete = 0 <!-- 排除已删除的记录 -->
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from zg_product_order
        where id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.zg.ZgProductOrder" useGeneratedKeys="true">
        insert into zg_product_order
        (id, buyUserId, addressId, orderNo, orderName, supplierNo,
         supplierName, ticketSeqNo, ticketNo, ticketName, originalPrice,
         currentPrice, prepayAmount, postpayAmount, effectiveStartTime, effectiveEndTime,
         productPicture, productName, productSize, productNum, payType,
         orderStatus, usedQrCode, createTime, updateTime, isDelete)
        values (#{id}, #{buyUserId}, #{addressId}, #{orderNo}, #{orderName}, #{supplierNo},
                #{supplierName}, #{ticketSeqNo}, #{ticketNo}, #{ticketName}, #{originalPrice},
                #{currentPrice}, #{prepayAmount}, #{postpayAmount}, #{effectiveStartTime}, #{effectiveEndTime},
                #{productPicture}, #{productName}, #{productSize}, #{productNum}, #{payType},
                #{orderStatus}, #{usedQrCode}, #{createTime}, #{updateTime}, #{isDelete})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.zg.ZgProductOrder" useGeneratedKeys="true">
        insert into zg_product_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="buyUserId != null">buyUserId,</if>
            <if test="addressId != null">addressId,</if>
            <if test="orderNo != null">orderNo,</if>
            <if test="orderName != null">orderName,</if>
            <if test="supplierNo != null">supplierNo,</if>
            <if test="supplierName != null">supplierName,</if>
            <if test="ticketSeqNo != null">ticketSeqNo,</if>
            <if test="ticketNo != null">ticketNo,</if>
            <if test="ticketName != null">ticketName,</if>
            <if test="originalPrice != null">originalPrice,</if>
            <if test="currentPrice != null">currentPrice,</if>
            <if test="prepayAmount != null">prepayAmount,</if>
            <if test="postpayAmount != null">postpayAmount,</if>
            <if test="effectiveStartTime != null">effectiveStartTime,</if>
            <if test="effectiveEndTime != null">effectiveEndTime,</if>
            <if test="productPicture != null">productPicture,</if>
            <if test="productName != null">productName,</if>
            <if test="productSize != null">productSize,</if>
            <if test="productNum != null">productNum,</if>
            <if test="payType != null">payType,</if>
            <if test="orderStatus != null">orderStatus,</if>
            <if test="usedQrCode != null">usedQrCode,</if>
            <if test="createTime != null">createTime,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="isDelete != null">isDelete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="buyUserId != null">#{buyUserId},</if>
            <if test="addressId != null">#{addressId},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="orderName != null">#{orderName},</if>
            <if test="supplierNo != null">#{supplierNo},</if>
            <if test="supplierName != null">#{supplierName},</if>
            <if test="ticketSeqNo != null">#{ticketSeqNo},</if>
            <if test="ticketNo != null">#{ticketNo},</if>
            <if test="ticketName != null">#{ticketName},</if>
            <if test="originalPrice != null">#{originalPrice},</if>
            <if test="currentPrice != null">#{currentPrice},</if>
            <if test="prepayAmount != null">#{prepayAmount},</if>
            <if test="postpayAmount != null">#{postpayAmount},</if>
            <if test="effectiveStartTime != null">#{effectiveStartTime},</if>
            <if test="effectiveEndTime != null">#{effectiveEndTime},</if>
            <if test="productPicture != null">#{productPicture},</if>
            <if test="productName != null">#{productName},</if>
            <if test="productSize != null">#{productSize},</if>
            <if test="productNum != null">#{productNum},</if>
            <if test="payType != null">#{payType},</if>
            <if test="orderStatus != null">#{orderStatus},</if>
            <if test="usedQrCode != null">#{usedQrCode},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.zg.ZgProductOrder">
        update zg_product_order
        <set>
            <if test="buyUserId != null">
                buyUserId = #{buyUserId},
            </if>
            <if test="addressId != null">
                addressId = #{addressId},
            </if>
            <if test="orderNo != null">
                orderNo = #{orderNo},
            </if>
            <if test="orderName != null">
                orderName = #{orderName},
            </if>
            <if test="supplierNo != null">
                supplierNo = #{supplierNo},
            </if>
            <if test="supplierName != null">
                supplierName = #{supplierName},
            </if>
            <if test="ticketSeqNo != null">
                ticketSeqNo = #{ticketSeqNo},
            </if>
            <if test="ticketNo != null">
                ticketNo = #{ticketNo},
            </if>
            <if test="ticketName != null">
                ticketName = #{ticketName},
            </if>
            <if test="originalPrice != null">
                originalPrice = #{originalPrice},
            </if>
            <if test="currentPrice != null">
                currentPrice = #{currentPrice},
            </if>
            <if test="prepayAmount != null">
                prepayAmount = #{prepayAmount},
            </if>
            <if test="postpayAmount != null">
                postpayAmount = #{postpayAmount},
            </if>
            <if test="effectiveStartTime != null">
                effectiveStartTime = #{effectiveStartTime},
            </if>
            <if test="effectiveEndTime != null">
                effectiveEndTime = #{effectiveEndTime},
            </if>
            <if test="productPicture != null">
                productPicture = #{productPicture},
            </if>
            <if test="productName != null">
                productName = #{productName},
            </if>
            <if test="productSize != null">
                productSize = #{productSize},
            </if>
            <if test="productNum != null">
                productNum = #{productNum},
            </if>
            <if test="payType != null">
                payType = #{payType},
            </if>
            <if test="orderStatus != null">
                orderStatus = #{orderStatus},
            </if>
            <if test="usedQrCode != null">
                usedQrCode = #{usedQrCode},
            </if>
            <if test="createTime != null">
                createTime = #{createTime},
            </if>
            <if test="updateTime != null">
                updateTime = #{updateTime},
            </if>
            <if test="isDelete != null">
                isDelete = #{isDelete},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.jmt.model.zg.ZgProductOrder">
        update zg_product_order
        set
            buyUserId = #{buyUserId},
            addressId = #{addressId},
            orderNo = #{orderNo},
            orderName = #{orderName},
            supplierNo = #{supplierNo},
            supplierName = #{supplierName},
            ticketSeqNo = #{ticketSeqNo},
            ticketNo = #{ticketNo},
            ticketName = #{ticketName},
            originalPrice = #{originalPrice},
            currentPrice = #{currentPrice},
            prepayAmount = #{prepayAmount},
            postpayAmount = #{postpayAmount},
            effectiveStartTime = #{effectiveStartTime},
            effectiveEndTime = #{effectiveEndTime},
            productPicture = #{productPicture},
            productName = #{productName},
            productSize = #{productSize},
            productNum = #{productNum},
            payType = #{payType},
            orderStatus = #{orderStatus},
            usedQrCode = #{usedQrCode},
            createTime = #{createTime},
            updateTime = #{updateTime},
            isDelete = #{isDelete}
        where id = #{id}
    </update>
</mapper>