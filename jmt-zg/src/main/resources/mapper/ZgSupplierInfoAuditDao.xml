<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ZgSupplierInfoAuditDao">


    <sql id="Base_Column_List">
        id, auditUser, supplierNo, auditStatus, reason, createTime,
        updateTime, isDelete
    </sql>

    <select id="selectById" parameterType="java.lang.Long" resultType="com.jmt.model.zg.entity.ZgSupplierInfoAudit">
        select
        <include refid="Base_Column_List" />
        from zg_supplier_info_audit
        where id = #{id} AND isDelete = 0
    </select>

    <select id="selectBySupplierNo" resultType="com.jmt.model.zg.entity.ZgSupplierInfoAudit"
            parameterType="java.lang.String">
        select
        <include refid="Base_Column_List" />
        from zg_supplier_info_audit
        where supplierNo = #{supplierNo}
    </select>

    <delete id="delete" parameterType="java.lang.Long">
        UPDATE zg_supplier_info_audit SET isDelete = 1, updateTime = NOW() WHERE id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.zg.entity.ZgSupplierInfoAudit" useGeneratedKeys="true">
        insert into zg_supplier_info_audit
        (id, auditUser, supplierNo, auditStatus, reason, createTime,
         updateTime, isDelete)
        values (#{id}, #{auditUser}, #{supplierNo}, #{auditStatus}, #{reason}, #{createTime},
                #{updateTime}, #{isDelete})
    </insert>



    <update id="updateById" parameterType="com.jmt.model.zg.entity.ZgSupplierInfoAudit">
        update zg_supplier_info_audit
        <set>
            <if test="auditUser != null">auditUser = #{auditUser},</if>
            <if test="supplierNo != null">supplierNo = #{supplierNo},</if>
            <if test="auditStatus != null">auditStatus = #{auditStatus},</if>
            <if test="reason != null">reason = #{reason},</if>
            updateTime = NOW()
        </set>
        where id = #{id}
    </update>

</mapper>