<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ZgCouponPackageDao">


    <sql id="Base_Column_List">
        id,buyUserId,couponNo,tStatus,createTime, updateTime, isDelete
    </sql>

    <!-- 插入 -->
    <insert id="insert" parameterType="com.jmt.model.zg.entity.ZgCouponPackage" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO zg_coupon_package (
            buyUserId,couponNo,tStatus,createTime, updateTime, isDelete
        ) VALUES (
                     #{buyUserId}, #{couponNo}, #{tStatus},  #{createTime}, #{updateTime}, #{isDelete}
                 )
    </insert>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultType="com.jmt.model.zg.entity.ZgCouponPackage">
        SELECT
        <include refid="Base_Column_List" />
        FROM zg_coupon_package
        WHERE id = #{id}
        AND isDelete = 0
    </select>

    <!-- 根据优惠券编号查询 -->
    <select id="selectByUserId" parameterType="java.lang.Long" resultType="com.jmt.model.zg.entity.ZgCouponPackage">
        SELECT
        <include refid="Base_Column_List" />
        FROM zg_coupon_package
        WHERE buyUserId = #{buyUserId}
        AND isDelete = 0
    </select>


    <resultMap id="CouponVOResultMap" type="com.jmt.model.zg.vo.ZgCouponVO">
        <id column="id" property="id" />
        <result column="couponNo" property="couponNo" />
        <result column="couponName" property="couponName" />
        <result column="categoryId" property="categoryId" />
        <result column="categoryName" property="categoryName" />
        <result column="validityPeriod" property="validityPeriod" />
        <result column="couponAmount" property="couponAmount" />
        <result column="description" property="description" />
        <result column="isReceived" property="isReceived" />
        <result column="receiveStatusText" property="receiveStatusText" />
        <result column="isValid" property="isValid" />
        <result column="remainingDays" property="remainingDays" />
    </resultMap>
    <!-- 首页优惠券列表查询 -->
    <select id="selectHomePageCoupons" resultMap="CouponVOResultMap">
        SELECT
            pc.id,
            pc.couponNo,
            pc.couponName,
            pc.categoryId,
            c.categoryName,
            pc.validityPeriod,
            pc.couponAmount,
            pc.description,
            CASE
                WHEN cp.couponNo IS NOT NULL THEN 1
                ELSE 0
                END as isReceived,
            CASE
                WHEN cp.couponNo IS NOT NULL THEN '已领取'
                ELSE '立即领取'
                END as receiveStatusText,
            CASE
                WHEN pc.validityPeriod IS NULL OR pc.validityPeriod >= NOW() THEN 1
                ELSE 0
                END as isValid,
            CASE
                WHEN pc.validityPeriod IS NOT NULL THEN
                    DATEDIFF(pc.validityPeriod, NOW())
                ELSE NULL
                END as remainingDays
        FROM zg_product_coupon pc
                 LEFT JOIN zg_product_category c ON pc.categoryId = c.id AND c.isDelete = 0
                 LEFT JOIN zg_coupon_package cp ON pc.couponNo = cp.couponNo
            AND cp.buyUserId = #{userId}
            AND cp.isDelete = 0
        WHERE pc.isDelete = 0
          AND (pc.validityPeriod IS NULL OR pc.validityPeriod >= CURDATE())
        ORDER BY
            isReceived ASC,  -- 未领取的排在前面
            pc.validityPeriod ASC,  -- 快过期的优先显示
            pc.createTime DESC
    </select>

    <select id="selectAllAvailableCoupons" resultMap="CouponVOResultMap">
        SELECT
            pc.id,
            pc.couponNo,
            pc.couponName,
            pc.categoryId,
            c.categoryName,
            pc.validityPeriod,
            pc.couponAmount,
            pc.description,
            0 as isReceived,  -- 未登录用户都显示未领取
            '立即领取' as receiveStatusText,
            CASE
                WHEN pc.validityPeriod IS NULL OR pc.validityPeriod >= NOW() THEN 1
                ELSE 0
                END as isValid,
            CASE
                WHEN pc.validityPeriod IS NOT NULL THEN
                    DATEDIFF(pc.validityPeriod, NOW())
                ELSE NULL
                END as remainingDays
        FROM zg_product_coupon pc
                 LEFT JOIN zg_product_category c ON pc.categoryId = c.id AND c.isDelete = 0
        WHERE pc.isDelete = 0
          AND (pc.validityPeriod IS NULL OR pc.validityPeriod >= CURDATE())
        ORDER BY
            pc.validityPeriod ASC,  -- 快过期的优先
            pc.createTime DESC
    </select>

    <update id="updateById" parameterType="com.jmt.model.zg.entity.ZgCouponPackage">
        UPDATE zg_coupon_package
        <set>
            <if test="couponNo != null">couponNo = #{couponNo},</if>
            <if test="tStatus != null">tStatus = #{tStatus},</if>
            <if test="buyUserId != null">buyUserId = #{buyUserId},</if>
            updateTime = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 逻辑删除 -->
    <update id="delete" parameterType="java.lang.Long">
        UPDATE zg_coupon_package
        SET isDelete = 1, updateTime = NOW()
        WHERE id = #{id}
    </update>




</mapper>