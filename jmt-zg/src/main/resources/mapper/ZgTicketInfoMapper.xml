<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ZgTicketInfoMapper">

    <resultMap id="BaseResultMap" type="com.jmt.model.zg.ZgTicketInfo">
        <id property="id" column="id" />
        <result property="supplierNo" column="supplierNo" />
        <result property="supplierName" column="supplierName" />
        <result property="ticketNo" column="ticketNo" />
        <result property="ticketName" column="ticketName" />
        <result property="originalPrice" column="originalPrice" />
        <result property="currentPrice" column="currentPrice" />
        <result property="prepayAmount" column="prepayAmount" />
        <result property="postpayAmount" column="postpayAmount" />
        <result property="effectiveStartTime" column="effectiveStartTime" />
        <result property="effectiveEndTime" column="effectiveEndTime" />
        <result property="productPicture" column="productPicture" />
        <result property="productName" column="productName" />
        <result property="productSize" column="productSize" />
        <result property="productNum" column="productNum" />
        <result property="stock" column="stock" />
        <result property="buyLimit" column="buyLimit" />
        <result property="auditStatus" column="auditStatus" />
        <result property="remark" column="remark" />
        <result property="reason" column="reason" />
        <result property="createTime" column="createTime" />
        <result property="updateTime" column="updateTime" />
        <result property="isDelete" column="isDelete" />
    </resultMap>

    <sql id="Base_Column_List">
        id, supplierNo, supplierName, ticketNo, ticketName, originalPrice,
        currentPrice, prepayAmount, postpayAmount, effectiveStartTime, effectiveEndTime,
        productPicture, productName, productSize, productNum, stock,
        buyLimit, auditStatus, remark, reason, createTime,
        updateTime, isDelete
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from zg_ticket_info
        where id = #{id}
    </select>

    <select id="getTicketInfoPage" resultType="com.jmt.model.zg.ZgTicketInfo"
            parameterType="com.jmt.model.zg.ZgTicketInfo">
        SELECT
        id,
        supplierNo,
        supplierName,
        ticketNo,
        ticketName,
        originalPrice,
        currentPrice,
        prepayAmount,
        postpayAmount,
        effectiveStartTime,
        effectiveEndTime,
        productPicture,
        productName,
        productSize,
        productNum,
        stock,
        buyLimit,
        auditStatus,
        remark,
        reason,
        createTime,
        updateTime,
        isDelete
        FROM
        zg_ticket_info
        WHERE
        isDelete = 0
        <if test="supplierNo != null and supplierNo != ''">
            AND supplierNo = #{supplierNo}
        </if>
        <if test="ticketNo != null and ticketNo != ''">
            AND ticketNo = #{ticketNo}
        </if>
        <if test="ticketName != null and ticketName != ''">
            AND ticketName LIKE CONCAT('%', #{ticketName}, '%')
        </if>
        <if test="auditStatus != null">
            AND auditStatus = #{auditStatus}
        </if>
        ORDER BY
        createTime DESC
    </select>

    <select id="selectByTicketNo" resultType="com.jmt.model.zg.ZgTicketInfo" parameterType="java.lang.String">
        SELECT
            id,
            supplierNo,
            supplierName,
            ticketNo,
            ticketName,
            originalPrice,
            currentPrice,
            prepayAmount,
            postpayAmount,
            effectiveStartTime,
            effectiveEndTime,
            productPicture,
            productName,
            productSize,
            productNum,
            stock,
            buyLimit,
            auditStatus,
            remark,
            reason,
            createTime,
            updateTime,
            isDelete
        FROM
            zg_ticket_info
        WHERE
            ticketNo = #{ticketNo}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from zg_ticket_info
        where id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.zg.ZgTicketInfo" useGeneratedKeys="true">
        insert into zg_ticket_info
        (id, supplierNo, supplierName, ticketNo, ticketName, originalPrice,
         currentPrice, prepayAmount, postpayAmount, effectiveStartTime, effectiveEndTime,
         productPicture, productName, productSize, productNum, stock,
         buyLimit, auditStatus, remark, reason, createTime,
         updateTime, isDelete)
        values (#{id}, #{supplierNo}, #{supplierName}, #{ticketNo}, #{ticketName}, #{originalPrice},
                #{currentPrice}, #{prepayAmount}, #{postpayAmount}, #{effectiveStartTime}, #{effectiveEndTime},
                #{productPicture}, #{productName}, #{productSize}, #{productNum}, #{stock},
                #{buyLimit}, #{auditStatus}, #{remark}, #{reason}, #{createTime},
                #{updateTime}, #{isDelete})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.zg.ZgTicketInfo" useGeneratedKeys="true">
        insert into zg_ticket_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="supplierNo != null">supplierNo,</if>
            <if test="supplierName != null">supplierName,</if>
            <if test="ticketNo != null">ticketNo,</if>
            <if test="ticketName != null">ticketName,</if>
            <if test="originalPrice != null">originalPrice,</if>
            <if test="currentPrice != null">currentPrice,</if>
            <if test="prepayAmount != null">prepayAmount,</if>
            <if test="postpayAmount != null">postpayAmount,</if>
            <if test="effectiveStartTime != null">effectiveStartTime,</if>
            <if test="effectiveEndTime != null">effectiveEndTime,</if>
            <if test="productPicture != null">productPicture,</if>
            <if test="productName != null">productName,</if>
            <if test="productSize != null">productSize,</if>
            <if test="productNum != null">productNum,</if>
            <if test="stock != null">stock,</if>
            <if test="buyLimit != null">buyLimit,</if>
            <if test="auditStatus != null">auditStatus,</if>
            <if test="remark != null">remark,</if>
            <if test="reason != null">reason,</if>
            <if test="createTime != null">createTime,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="isDelete != null">isDelete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="supplierNo != null">#{supplierNo},</if>
            <if test="supplierName != null">#{supplierName},</if>
            <if test="ticketNo != null">#{ticketNo},</if>
            <if test="ticketName != null">#{ticketName},</if>
            <if test="originalPrice != null">#{originalPrice},</if>
            <if test="currentPrice != null">#{currentPrice},</if>
            <if test="prepayAmount != null">#{prepayAmount},</if>
            <if test="postpayAmount != null">#{postpayAmount},</if>
            <if test="effectiveStartTime != null">#{effectiveStartTime},</if>
            <if test="effectiveEndTime != null">#{effectiveEndTime},</if>
            <if test="productPicture != null">#{productPicture},</if>
            <if test="productName != null">#{productName},</if>
            <if test="productSize != null">#{productSize},</if>
            <if test="productNum != null">#{productNum},</if>
            <if test="stock != null">#{stock},</if>
            <if test="buyLimit != null">#{buyLimit},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="remark != null">#{remark},</if>
            <if test="reason != null">#{reason},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.zg.ZgTicketInfo">
        update zg_ticket_info
        <set>
            <if test="supplierNo != null">
                supplierNo = #{supplierNo},
            </if>
            <if test="supplierName != null">
                supplierName = #{supplierName},
            </if>
            <if test="ticketNo != null">
                ticketNo = #{ticketNo},
            </if>
            <if test="ticketName != null">
                ticketName = #{ticketName},
            </if>
            <if test="originalPrice != null">
                originalPrice = #{originalPrice},
            </if>
            <if test="currentPrice != null">
                currentPrice = #{currentPrice},
            </if>
            <if test="prepayAmount != null">
                prepayAmount = #{prepayAmount},
            </if>
            <if test="postpayAmount != null">
                postpayAmount = #{postpayAmount},
            </if>
            <if test="effectiveStartTime != null">
                effectiveStartTime = #{effectiveStartTime},
            </if>
            <if test="effectiveEndTime != null">
                effectiveEndTime = #{effectiveEndTime},
            </if>
            <if test="productPicture != null">
                productPicture = #{productPicture},
            </if>
            <if test="productName != null">
                productName = #{productName},
            </if>
            <if test="productSize != null">
                productSize = #{productSize},
            </if>
            <if test="productNum != null">
                productNum = #{productNum},
            </if>
            <if test="stock != null">
                stock = #{stock},
            </if>
            <if test="buyLimit != null">
                buyLimit = #{buyLimit},
            </if>
            <if test="auditStatus != null">
                auditStatus = #{auditStatus},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="reason != null">
                reason = #{reason},
            </if>
            <if test="createTime != null">
                createTime = #{createTime},
            </if>
            <if test="updateTime != null">
                updateTime = #{updateTime},
            </if>
            <if test="isDelete != null">
                isDelete = #{isDelete},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.jmt.model.zg.ZgTicketInfo">
        update zg_ticket_info
        set
            supplierNo = #{supplierNo},
            supplierName = #{supplierName},
            ticketNo = #{ticketNo},
            ticketName = #{ticketName},
            originalPrice = #{originalPrice},
            currentPrice = #{currentPrice},
            prepayAmount = #{prepayAmount},
            postpayAmount = #{postpayAmount},
            effectiveStartTime = #{effectiveStartTime},
            effectiveEndTime = #{effectiveEndTime},
            productPicture = #{productPicture},
            productName = #{productName},
            productSize = #{productSize},
            productNum = #{productNum},
            stock = #{stock},
            buyLimit = #{buyLimit},
            auditStatus = #{auditStatus},
            remark = #{remark},
            reason = #{reason},
            createTime = #{createTime},
            updateTime = #{updateTime},
            isDelete = #{isDelete}
        where id = #{id}
    </update>
</mapper>