<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ZgProductPreOrderDetailMapper">

    <resultMap id="BaseResultMap" type="com.jmt.model.zg.ZgProductPreOrderDetail">
        <id property="id" column="id" />
        <result property="productPreOrderNo" column="productPreOrderNo" />
        <result property="productOrderNo" column="productOrderNo" />
        <result property="createTime" column="createTime" />
        <result property="updateTime" column="updateTime" />
        <result property="isDelete" column="isDelete" />
    </resultMap>

    <sql id="Base_Column_List">
        id, productPreOrderNo, productOrderNo, createTime, updateTime, isDelete
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from zg_product_pre_order_detail
        where id = #{id}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from zg_product_pre_order_detail
        where id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.zg.ZgProductPreOrderDetail" useGeneratedKeys="true">
        insert into zg_product_pre_order_detail
        (id, productPreOrderNo, productOrderNo, createTime, updateTime, isDelete)
        values (#{id}, #{productPreOrderNo}, #{productOrderNo}, #{createTime}, #{updateTime}, #{isDelete})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.zg.ZgProductPreOrderDetail" useGeneratedKeys="true">
        insert into zg_product_pre_order_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="productPreOrderNo != null">productPreOrderNo,</if>
            <if test="productOrderNo != null">productOrderNo,</if>
            <if test="createTime != null">createTime,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="isDelete != null">isDelete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="productPreOrderNo != null">#{productPreOrderNo},</if>
            <if test="productOrderNo != null">#{productOrderNo},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.zg.ZgProductPreOrderDetail">
        update zg_product_pre_order_detail
        <set>
            <if test="productPreOrderNo != null">
                productPreOrderNo = #{productPreOrderNo},
            </if>
            <if test="productOrderNo != null">
                productOrderNo = #{productOrderNo},
            </if>
            <if test="createTime != null">
                createTime = #{createTime},
            </if>
            <if test="updateTime != null">
                updateTime = #{updateTime},
            </if>
            <if test="isDelete != null">
                isDelete = #{isDelete},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.jmt.model.zg.ZgProductPreOrderDetail">
        update zg_product_pre_order_detail
        set
            productPreOrderNo = #{productPreOrderNo},
            productOrderNo = #{productOrderNo},
            createTime = #{createTime},
            updateTime = #{updateTime},
            isDelete = #{isDelete}
        where id = #{id}
    </update>
</mapper>