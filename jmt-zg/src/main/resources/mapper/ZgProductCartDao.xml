<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ZgProductCartDao">


    <sql id="Base_Column_List">
        id, supplierNo, productNo, productName, buyUserId, originalPrice,
        currentPrice, quantity, createTime, updateTime, isDelete
    </sql>

    <insert id="insert" parameterType="com.jmt.model.zg.entity.ZgProductCart" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO zg_product_cart (
            id,supplierNo, productNo, productName, buyUserId,
            originalPrice, currentPrice, quantity,
            createTime, updateTime, isDelete
        ) VALUES (
                     #{id},#{supplierNo}, #{productNo}, #{productName}, #{buyUserId},
                     #{originalPrice}, #{currentPrice}, #{quantity},
                     NOW(), NOW(), 0
                 )
    </insert>



    <delete id="delete" parameterType="java.lang.Long">
        UPDATE zg_product_cart
        SET isDelete = 1, updateTime = NOW()
        WHERE id = #{id} AND isDelete = 0
    </delete>


<!--    清空购物车-->
    <delete id="deleteByUserId">
        UPDATE zg_product_cart
        SET isDelete = 1, updateTime = NOW()
        WHERE buyUserId = #{buyUserId} AND isDelete = 0
    </delete>



    <update id="updateById" parameterType="com.jmt.model.zg.entity.ZgProductCart">
        UPDATE zg_product_cart
        <set>
<!--            <if test="supplierNo != null">supplierNo = #{supplierNo},</if>-->
<!--            <if test="productNo != null">productNo = #{productNo},</if>-->
<!--            <if test="productName != null">productName = #{productName},</if>-->
<!--            <if test="buyUserId != null">buyUserId = #{buyUserId},</if>-->
<!--            <if test="originalPrice != null">originalPrice = #{originalPrice},</if>-->
<!--            <if test="currentPrice != null">currentPrice = #{currentPrice},</if>-->
            <if test="quantity != null">quantity = #{quantity},</if>
            updateTime = NOW()
        </set>
        WHERE id = #{id} AND isDelete = 0
    </update>

    <update id="updateQuantity">
        UPDATE zg_product_cart
        SET quantity = #{quantity},
            updateTime = NOW()
        WHERE id = #{id} AND isDelete = 0
    </update>

    <select id="selectById" parameterType="java.lang.Long" resultType="com.jmt.model.zg.entity.ZgProductCart">
        SELECT
        <include refid="Base_Column_List" />
        FROM zg_product_cart
        WHERE id = #{id} AND isDelete = 0
    </select>

    <select id="selectByCondition" parameterType="com.jmt.model.zg.entity.ZgProductCart" resultType="com.jmt.model.zg.entity.ZgProductCart">
        SELECT
        <include refid="Base_Column_List" />
        FROM zg_product_cart
        WHERE isDelete = 0 and buyUserId = #{buyUserId}
        <if test="productName != null and productName != ''">
            AND productName LIKE CONCAT('%', #{productName}, '%')
        </if>
        ORDER BY createTime DESC
    </select>

    <select id="selectCartCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM zg_product_cart
        WHERE buyUserId = #{buyUserId} AND isDelete = 0
    </select>

    <!-- 增加数量 -->
    <update id="increaseQuantity">
        UPDATE zg_product_cart
        SET quantity = quantity + #{delta},
            updateTime = NOW()
        WHERE id = #{id} AND isDelete = 0
    </update>

    <!-- 减少数量 -->
    <update id="decreaseQuantity">
        UPDATE zg_product_cart
        SET quantity = quantity - #{delta},
            updateTime = NOW()
        WHERE id = #{id} AND isDelete = 0 AND quantity >= #{delta}
    </update>

    <select id="selectByIdsAndUserId" resultType="com.jmt.model.zg.entity.ZgProductCart">
        SELECT <include refid="Base_Column_List" />
        FROM zg_product_cart
        WHERE id IN
        <foreach item="id" collection="cartIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND buyUserId = #{buyUserId}
        AND isDelete = 0
    </select>


    <select id="selectCartWithProductInfo" resultType="com.jmt.model.zg.vo.ZgProductCartVO">
        SELECT
            c.id,
            c.productNo ,
            c.productName ,
            c.originalPrice ,
            c.currentPrice,
            c.quantity ,

            p.productBarCode,
            p.productSize,
            p.productMainPicture,
            p.productNature,
            p.categoryId ,
            pc.categoryName

        FROM zg_product_cart c
                 LEFT JOIN zg_product_info p ON c.productNo = p.productNo AND p.isDelete = 0
                 LEFT JOIN zg_product_category pc ON p.categoryId = pc.id AND pc.isDelete = 0
        WHERE c.buyUserId = #{buyUserId}
          AND c.isDelete = 0
        <if test="cartIds != null and cartIds.size() > 0">
            AND c.id IN
            <foreach collection="cartIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        ORDER BY c.createTime DESC
    </select>

    <update id="deleteByIds">
        UPDATE zg_product_cart
        SET isDelete = 1, updateTime = NOW()
        WHERE id IN
        <foreach item="id" collection="cartIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="findByUserAndProduct" resultType="com.jmt.model.zg.entity.ZgProductCart">
        SELECT <include refid="Base_Column_List" />
        FROM zg_product_cart
        WHERE buyUserId = #{buyUserId}
        AND productNo = #{productNo}
        AND isDelete = 0
        LIMIT 1
    </select>
</mapper>