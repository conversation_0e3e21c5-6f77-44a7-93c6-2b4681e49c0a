<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ZgProductPreOrderMapper">

    <resultMap id="BaseResultMap" type="com.jmt.model.zg.ZgProductPreOrder">
        <id property="id" column="id" />
        <result property="orderNo" column="orderNo" />
        <result property="orderName" column="orderName" />
        <result property="supplierNo" column="supplierNo" />
        <result property="ticketNo" column="ticketNo" />
        <result property="ticketName" column="ticketName" />
        <result property="productTotal" column="productTotal" />
        <result property="amountTotal" column="amountTotal" />
        <result property="orderCount" column="orderCount" />
        <result property="orderStatus" column="orderStatus" />
        <result property="reason" column="reason" />
        <result property="createTime" column="createTime" />
        <result property="updateTime" column="updateTime" />
        <result property="isDelete" column="isDelete" />
    </resultMap>

    <sql id="Base_Column_List">
        id, orderNo, orderName, supplierNo, ticketNo, ticketName,
        productTotal, amountTotal, orderCount, orderStatus, reason,
        createTime, updateTime, isDelete
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from zg_product_pre_order
        where id = #{id}
    </select>

    <select id="getTicketInfoPage" resultType="com.jmt.model.zg.ZgProductPreOrder"
            parameterType="com.jmt.model.zg.ZgProductPreOrder">
        SELECT
        id,
        orderNo,
        orderName,
        supplierNo,
        ticketNo,
        ticketName,
        productTotal,
        amountTotal,
        orderCount,
        orderStatus,
        reason,
        createTime,
        updateTime,
        isDelete
        FROM
        zg_product_pre_order
        WHERE
        isDelete = 0
        <if test="orderNo != null and orderNo != ''">
            AND orderNo = #{orderNo}
        </if>
        <if test="orderName != null and orderName != ''">
            AND orderName LIKE CONCAT('%', #{orderName}, '%')
        </if>
        <if test="supplierNo != null and supplierNo != ''">
            AND supplierNo = #{supplierNo}
        </if>
        <if test="ticketNo != null and ticketNo != ''">
            AND ticketNo = #{ticketNo}
        </if>
        <if test="ticketName != null and ticketName != ''">
            AND ticketName LIKE CONCAT('%', #{ticketName}, '%')
        </if>
        <if test="orderStatus != null">
            AND orderStatus = #{orderStatus}
        </if>
        ORDER BY
        createTime DESC
    </select>
    <select id="selectByOrderNo" resultType="com.jmt.model.zg.ZgProductPreOrder">
        SELECT
        id,
        orderNo,
        orderName,
        supplierNo,
        ticketNo,
        ticketName,
        productTotal,
        amountTotal,
        orderCount,
        orderStatus,
        reason,
        createTime,
        updateTime,
        isDelete
        FROM zg_product_pre_order
        WHERE orderNo = #{orderNo}
        AND isDelete = 0
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from zg_product_pre_order
        where id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.zg.ZgProductPreOrder" useGeneratedKeys="true">
        insert into zg_product_pre_order
        (id, orderNo, orderName, supplierNo, ticketNo, ticketName,
         productTotal, amountTotal, orderCount, orderStatus, reason,
         createTime, updateTime, isDelete)
        values (#{id}, #{orderNo}, #{orderName}, #{supplierNo}, #{ticketNo}, #{ticketName},
                #{productTotal}, #{amountTotal}, #{orderCount}, #{orderStatus}, #{reason},
                #{createTime}, #{updateTime}, #{isDelete})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.zg.ZgProductPreOrder" useGeneratedKeys="true">
        insert into zg_product_pre_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="orderNo != null">orderNo,</if>
            <if test="orderName != null">orderName,</if>
            <if test="supplierNo != null">supplierNo,</if>
            <if test="ticketNo != null">ticketNo,</if>
            <if test="ticketName != null">ticketName,</if>
            <if test="productTotal != null">productTotal,</if>
            <if test="amountTotal != null">amountTotal,</if>
            <if test="orderCount != null">orderCount,</if>
            <if test="orderStatus != null">orderStatus,</if>
            <if test="reason != null">reason,</if>
            <if test="createTime != null">createTime,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="isDelete != null">isDelete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="orderName != null">#{orderName},</if>
            <if test="supplierNo != null">#{supplierNo},</if>
            <if test="ticketNo != null">#{ticketNo},</if>
            <if test="ticketName != null">#{ticketName},</if>
            <if test="productTotal != null">#{productTotal},</if>
            <if test="amountTotal != null">#{amountTotal},</if>
            <if test="orderCount != null">#{orderCount},</if>
            <if test="orderStatus != null">#{orderStatus},</if>
            <if test="reason != null">#{reason},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.zg.ZgProductPreOrder">
        update zg_product_pre_order
        <set>
            <if test="orderNo != null">
                orderNo = #{orderNo},
            </if>
            <if test="orderName != null">
                orderName = #{orderName},
            </if>
            <if test="supplierNo != null">
                supplierNo = #{supplierNo},
            </if>
            <if test="ticketNo != null">
                ticketNo = #{ticketNo},
            </if>
            <if test="ticketName != null">
                ticketName = #{ticketName},
            </if>
            <if test="productTotal != null">
                productTotal = #{productTotal},
            </if>
            <if test="amountTotal != null">
                amountTotal = #{amountTotal},
            </if>
            <if test="orderCount != null">
                orderCount = #{orderCount},
            </if>
            <if test="orderStatus != null">
                orderStatus = #{orderStatus},
            </if>
            <if test="reason != null">
                reason = #{reason},
            </if>
            <if test="createTime != null">
                createTime = #{createTime},
            </if>
            <if test="updateTime != null">
                updateTime = #{updateTime},
            </if>
            <if test="isDelete != null">
                isDelete = #{isDelete},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.jmt.model.zg.ZgProductPreOrder">
        update zg_product_pre_order
        set
            orderNo = #{orderNo},
            orderName = #{orderName},
            supplierNo = #{supplierNo},
            ticketNo = #{ticketNo},
            ticketName = #{ticketName},
            productTotal = #{productTotal},
            amountTotal = #{amountTotal},
            orderCount = #{orderCount},
            orderStatus = #{orderStatus},
            reason = #{reason},
            createTime = #{createTime},
            updateTime = #{updateTime},
            isDelete = #{isDelete}
        where id = #{id}
    </update>
</mapper>