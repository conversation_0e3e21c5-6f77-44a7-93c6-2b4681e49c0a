<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ZgProductInfoDao">


    <sql id="Base_Column_List">
        id, supplierNo, supplierName, productNo, productName, categoryId, licensePicture,
        qualityReport, originalPrice, currentPrice, productMainPicture, productSubPicture,
        productDetailPicture, productBarCode, productBrand, productSize, productArticleNo,
        productUnit, productNature, servicePoint, fundPoint, stock, buyLimit, auditStatus,
        remark, reason, createTime, updateTime, isDelete
    </sql>

    <insert id="insert" parameterType="com.jmt.model.zg.entity.ZgProductInfo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO zg_product_info (
            id,supplierNo, supplierName, productNo, productName, categoryId,
            licensePicture, qualityReport, originalPrice, currentPrice,
            productMainPicture, productSubPicture, productDetailPicture,
            productBarCode, productBrand, productSize, productArticleNo,
            productUnit, productNature, servicePoint, fundPoint, stock,
            buyLimit, auditStatus, remark, reason, createTime, updateTime, isDelete
        ) VALUES (
                     #{id},#{supplierNo}, #{supplierName}, #{productNo}, #{productName}, #{categoryId},
                     #{licensePicture}, #{qualityReport}, #{originalPrice}, #{currentPrice},
                     #{productMainPicture}, #{productSubPicture}, #{productDetailPicture},
                     #{productBarCode}, #{productBrand}, #{productSize}, #{productArticleNo},
                     #{productUnit}, #{productNature}, #{servicePoint}, #{fundPoint}, #{stock},
                     #{buyLimit}, #{auditStatus}, #{remark}, #{reason}, NOW(), NOW(), 0
                 )
    </insert>


    <delete id="delete" parameterType="java.lang.Long">
        UPDATE zg_product_info
        SET isDelete = 1, updateTime = NOW()
        WHERE id = #{id} AND isDelete = 0
    </delete>


    <update id="updateById" parameterType="com.jmt.model.zg.entity.ZgProductInfo">
        UPDATE zg_product_info
        <set>
            <if test="supplierNo != null">supplierNo = #{supplierNo},</if>
            <if test="supplierName != null">supplierName = #{supplierName},</if>
            <if test="productNo != null">productNo = #{productNo},</if>
            <if test="productName != null">productName = #{productName},</if>
            <if test="categoryId != null">categoryId = #{categoryId},</if>
            <if test="licensePicture != null">licensePicture = #{licensePicture},</if>
            <if test="qualityReport != null">qualityReport = #{qualityReport},</if>
            <if test="originalPrice != null">originalPrice = #{originalPrice},</if>
            <if test="currentPrice != null">currentPrice = #{currentPrice},</if>
            <if test="productMainPicture != null">productMainPicture = #{productMainPicture},</if>
            <if test="productSubPicture != null">productSubPicture = #{productSubPicture},</if>
            <if test="productDetailPicture != null">productDetailPicture = #{productDetailPicture},</if>
            <if test="productBarCode != null">productBarCode = #{productBarCode},</if>
            <if test="productBrand != null">productBrand = #{productBrand},</if>
            <if test="productSize != null">productSize = #{productSize},</if>
            <if test="productArticleNo != null">productArticleNo = #{productArticleNo},</if>
            <if test="productUnit != null">productUnit = #{productUnit},</if>
            <if test="productNature != null">productNature = #{productNature},</if>
            <if test="servicePoint != null">servicePoint = #{servicePoint},</if>
            <if test="fundPoint != null">fundPoint = #{fundPoint},</if>
            <if test="stock != null">stock = #{stock},</if>
            <if test="buyLimit != null">buyLimit = #{buyLimit},</if>
            <if test="auditStatus != null">auditStatus = #{auditStatus},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="reason != null">reason = #{reason},</if>
            updateTime = NOW()
        </set>
        WHERE id = #{id} AND isDelete = 0
    </update>

    <select id="selectById" parameterType="java.lang.Long" resultType="com.jmt.model.zg.entity.ZgProductInfo">
        SELECT
        <include refid="Base_Column_List" />
        FROM zg_product_info
        WHERE id = #{id} AND isDelete = 0
    </select>

    <select id="selectByProductNo" parameterType="java.lang.String" resultType="com.jmt.model.zg.entity.ZgProductInfo">
        SELECT
        <include refid="Base_Column_List" />
        FROM zg_product_info
        WHERE productNo = #{productNo} AND isDelete = 0
    </select>

    <select id="selectAll" resultType="com.jmt.model.zg.entity.ZgProductInfo">
        SELECT
        <include refid="Base_Column_List" />
        FROM zg_product_info
        WHERE isDelete = 0
    </select>

    <sql id="ZgProductInfo_List">
        s.id, s.productNo, s.supplierName, s.productMainPicture, s.categoryId, s.productSize,
        s.originalPrice, s.currentPrice, s.stock, s.buyLimit, s.auditStatus,c.categoryName

    </sql>

    <select id="selectByCondition" parameterType="com.jmt.model.zg.entity.ZgProductInfo" resultType="com.jmt.model.zg.vo.ZgProductInfoVO">
        SELECT
        <include refid="ZgProductInfo_List" />
        FROM zg_product_info s
        LEFT JOIN zg_product_category c ON s.categoryId = c.id AND c.isDelete = 0
        WHERE s.isDelete = 0
        <if test="supplierNo != null and supplierNo != ''">AND s.supplierNo LIKE CONCAT('%', #{supplierNo}, '%')</if>
        <if test="supplierName != null and supplierName != ''">AND s.supplierName LIKE CONCAT('%', #{supplierName}, '%')</if>
        <if test="productNo != null and productNo != ''">AND s.productNo LIKE CONCAT('%', #{productNo}, '%')</if>
        <if test="productName != null and productName != ''">AND s.productName LIKE CONCAT('%', #{productName}, '%')</if>
        <if test="auditStatus != null">AND auditStatus = #{auditStatus}</if>
        ORDER BY s.createTime DESC
    </select>

    <select id="selectMyListByCondition" parameterType="com.jmt.model.zg.entity.ZgProductInfo" resultType="com.jmt.model.zg.vo.ZgMyProductListVO">
        SELECT
        <include refid="Base_Column_List" />
        FROM zg_product_info
        WHERE isDelete = 0
        <if test="supplierNo != null and supplierNo != ''">AND supplierNo = #{supplierNo}</if>
        <if test="productNo != null and productNo != ''">AND productNo LIKE CONCAT('%', #{productNo}, '%')</if>
        <if test="productName != null and productName != ''">AND productName LIKE CONCAT('%', #{productName}, '%')</if>
        <if test="auditStatus != null">AND auditStatus = #{auditStatus}</if>
        ORDER BY createTime DESC
    </select>


    <select id="selectMallListByCondition" parameterType="com.jmt.model.zg.entity.ZgProductInfo" resultType="com.jmt.model.zg.vo.ZgMallProductListVO">
        SELECT
        <include refid="Base_Column_List" />
        FROM zg_product_info
        WHERE isDelete = 0 and auditStatus = 1
        <if test="productName != null and productName != ''">AND productName LIKE CONCAT('%', #{productName}, '%')</if>
        <if test="categoryId != null">AND categoryId = #{categoryId}</if>
        ORDER BY createTime DESC
    </select>


    <update id="updateAuditStatus" parameterType="com.jmt.model.zg.entity.ZgProductInfo">
        UPDATE zg_product_info
        SET auditStatus = #{auditStatus},
            reason = #{reason},
            updateTime = NOW()
        WHERE id = #{id} AND isDelete = 0
    </update>

    <update id="updateStock">
        UPDATE zg_product_info
        SET stock = #{stock},
            updateTime = NOW()
        WHERE id = #{id} AND isDelete = 0
    </update>

    <!-- 批量检查品类是否有商品 -->
    <select id="findCategoryIdsWithProducts" resultType="java.lang.Long">
        SELECT DISTINCT categoryId
        FROM zg_product_info
        WHERE categoryId IN
        <foreach item="categoryId" collection="list" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
        AND isDelete = 0
        AND auditStatus = 1
    </select>

</mapper>