<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ZgTicketOrderMapper">

    <resultMap id="BaseResultMap" type="com.jmt.model.zg.ZgTicketOrder">
        <id property="id" column="id" />
        <result property="orderNo" column="orderNo" />
        <result property="orderName" column="orderName" />
        <result property="buyUserId" column="buyUserId" />
        <result property="payType" column="payType" />
        <result property="payAmount" column="payAmount" />
        <result property="orderStatus" column="orderStatus" />
        <result property="createTime" column="createTime" />
        <result property="updateTime" column="updateTime" />
        <result property="isDelete" column="isDelete" />
    </resultMap>

    <sql id="Base_Column_List">
        id, orderNo, orderName, buyUserId, payType, payAmount,
        orderStatus, createTime, updateTime, isDelete
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from zg_ticket_order
        where id = #{id}
    </select>
    <select id="getTicketInfoPage" resultType="com.jmt.model.zg.TicketOrderDetail" parameterType="com.jmt.model.zg.TicketOrderDetail">
        SELECT
            -- 订单主表字段（别名与TicketOrderDetail属性对应）
            o.orderNo,
            o.orderName,
            o.buyUserId,
            o.payType,
            o.payAmount,
            o.orderStatus,
            o.createTime,
            -- 订单明细表字段（别名与TicketOrderDetail属性对应）
            d.supplierNo,
            d.ticketNo,
            d.ticketName,
            d.originalPrice,
            d.currentPrice,
            d.prepayAmount,
            d.postpayAmount,
            d.quantity,
            d.createTime AS detailCreateTime
        FROM
            zg_ticket_order o
                INNER JOIN
            zg_ticket_order_detail d
            ON o.orderNo = d.orderNo  -- 通过orderNo关联两张表
        WHERE
            o.orderNo = #{orderNo}  -- 根据订单编号查询（参数来自TicketOrderDetail的orderNo）
          AND o.isDelete = 0  -- 过滤已删除的订单
          AND d.isDelete = 0  -- 过滤已删除的明细
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from zg_ticket_order
        where id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.zg.ZgTicketOrder" useGeneratedKeys="true">
        insert into zg_ticket_order
        (id, orderNo, orderName, buyUserId, payType, payAmount,
         orderStatus, createTime, updateTime, isDelete)
        values (#{id}, #{orderNo}, #{orderName}, #{buyUserId}, #{payType}, #{payAmount},
                #{orderStatus}, #{createTime}, #{updateTime}, #{isDelete})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.zg.ZgTicketOrder" useGeneratedKeys="true">
        insert into zg_ticket_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="orderNo != null">orderNo,</if>
            <if test="orderName != null">orderName,</if>
            <if test="buyUserId != null">buyUserId,</if>
            <if test="payType != null">payType,</if>
            <if test="payAmount != null">payAmount,</if>
            <if test="orderStatus != null">orderStatus,</if>
            <if test="createTime != null">createTime,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="isDelete != null">isDelete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="orderName != null">#{orderName},</if>
            <if test="buyUserId != null">#{buyUserId},</if>
            <if test="payType != null">#{payType},</if>
            <if test="payAmount != null">#{payAmount},</if>
            <if test="orderStatus != null">#{orderStatus},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.zg.ZgTicketOrder">
        update zg_ticket_order
        <set>
            <if test="orderNo != null">
                orderNo = #{orderNo},
            </if>
            <if test="orderName != null">
                orderName = #{orderName},
            </if>
            <if test="buyUserId != null">
                buyUserId = #{buyUserId},
            </if>
            <if test="payType != null">
                payType = #{payType},
            </if>
            <if test="payAmount != null">
                payAmount = #{payAmount},
            </if>
            <if test="orderStatus != null">
                orderStatus = #{orderStatus},
            </if>
            <if test="createTime != null">
                createTime = #{createTime},
            </if>
            <if test="updateTime != null">
                updateTime = #{updateTime},
            </if>
            <if test="isDelete != null">
                isDelete = #{isDelete},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.jmt.model.zg.ZgTicketOrder">
        update zg_ticket_order
        set
            orderNo = #{orderNo},
            orderName = #{orderName},
            buyUserId = #{buyUserId},
            payType = #{payType},
            payAmount = #{payAmount},
            orderStatus = #{orderStatus},
            createTime = #{createTime},
            updateTime = #{updateTime},
            isDelete = #{isDelete}
        where id = #{id}
    </update>
</mapper>