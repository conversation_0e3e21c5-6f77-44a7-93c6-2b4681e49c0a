<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ZgProductCouponDao">


    <sql id="Base_Column_List">
        id, couponNo, couponName, categoryId, validityPeriod, couponAmount,
        description, createTime, updateTime, isDelete
    </sql>

    <!-- 插入 -->
    <insert id="insert" parameterType="com.jmt.model.zg.entity.ZgProductCoupon" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO zg_product_coupon (
            couponNo, couponName, categoryId, validityPeriod, couponAmount,
            description, createTime, updateTime, isDelete
        ) VALUES (
                     #{couponNo}, #{couponName}, #{categoryId}, #{validityPeriod}, #{couponAmount},
                     #{description}, #{createTime}, #{updateTime}, #{isDelete}
                 )
    </insert>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultType="com.jmt.model.zg.entity.ZgProductCoupon">
        SELECT
        <include refid="Base_Column_List" />
        FROM zg_product_coupon
        WHERE id = #{id}
        AND isDelete = 0
    </select>

    <!-- 根据优惠券编号查询 -->
    <select id="selectByCouponNo" parameterType="java.lang.String" resultType="com.jmt.model.zg.entity.ZgProductCoupon">
        SELECT
        <include refid="Base_Column_List" />
        FROM zg_product_coupon
        WHERE couponNo = #{couponNo}
        AND isDelete = 0
    </select>

    <sql id="ZgProductCoupon_List">
        s.id, s.couponNo, s.couponName, s.categoryId, s.validityPeriod, s.couponAmount,
        s.description, s.createTime,
        s.updateTime, c.categoryName

    </sql>

    <!-- 分页查询 -->
    <select id="selectByCondition" parameterType="com.jmt.model.zg.entity.ZgProductCoupon" resultType="com.jmt.model.zg.vo.ZgProductCouponVO">
        SELECT
        <include refid="ZgProductCoupon_List" />
        FROM zg_product_coupon s
        LEFT JOIN zg_product_category c ON s.categoryId = c.id AND c.isDelete = 0
        <where>
            s.isDelete = 0
            <if test="couponNo != null and couponNo != ''">AND s.couponNo LIKE CONCAT('%', #{couponNo}, '%')</if>
            <if test="couponName != null and couponName != ''">AND s.couponName LIKE CONCAT('%', #{couponName}, '%')</if>
            <!-- 查询有效期内的优惠券 -->
<!--            <if test="validityPeriod != null">AND validityPeriod >= NOW()</if>-->
        </where>
        ORDER BY createTime DESC
    </select>



    <update id="updateById" parameterType="com.jmt.model.zg.entity.ZgProductCoupon">
        UPDATE zg_product_coupon
        <set>
            <if test="couponNo != null">couponNo = #{couponNo},</if>
            <if test="couponName != null">couponName = #{couponName},</if>
            <if test="categoryId != null">categoryId = #{categoryId},</if>
            <if test="validityPeriod != null">validityPeriod = #{validityPeriod},</if>
            <if test="couponAmount != null">couponAmount = #{couponAmount},</if>
            <if test="description != null">description = #{description},</if>
            updateTime = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 逻辑删除 -->
    <update id="delete" parameterType="java.lang.Long">
        UPDATE zg_product_coupon
        SET isDelete = 1, updateTime = NOW()
        WHERE id = #{id}
    </update>




</mapper>