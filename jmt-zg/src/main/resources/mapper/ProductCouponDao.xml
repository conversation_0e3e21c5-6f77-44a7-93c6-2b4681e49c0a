<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ProductCouponDao">

    <select id="getPage" resultType="com.jmt.model.zg.ZgProductCouponVo">
        SELECT
            t.`id`,
            t.`couponNo`,
            t.`couponName`,
            t.`categoryId`,
            t.`validityPeriod`,
            t.`couponAmount`,
            t.`description`,
            t.`cretaTime`,
            t.`updateTime`,
            c.categoryName
        FROM
            zg_product_coupon t LEFT JOIN zg_product_category c ON t.categoryId=c.id
        WHERE
            t.isDelete = 0
            <if test="couponNo != null and couponNo !=''">
                AND t.`couponNo` LIKE CONCAT('%',#{couponNo},'%')
            </if>
            <if test="couponName != null and couponName !=''">
                AND t.`couponName` LIKE CONCAT('%',#{couponName},'%')
            </if>
            <if test="categoryId != null">
                AND t.`categoryId` = #{categoryId}
            </if>
    </select>

    <select id="getInfo" parameterType="java.lang.Long" resultType="com.jmt.model.zg.ZgProductCouponVo">
        SELECT
            t.`id`,
            t.`couponNo`,
            t.`couponName`,
            t.`categoryId`,
            t.`validityPeriod`,
            t.`couponAmount`,
            t.`description`,
            t.`cretaTime`,
            t.`updateTime`,
            c.categoryName
        FROM
            zg_product_coupon t LEFT JOIN zg_product_category c ON t.categoryId=c.id
        WHERE
            t.isDelete = 0 AND t.`id` = #{id}
    </select>

    <insert id="insert" parameterType="com.jmt.model.zg.ZgProductCouponDto" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO `zg_product_coupon`
        ( `couponNo`, `couponName`, `categoryId`, `validityPeriod`, `couponAmount`, `description`, `cretaTime`, `updateTime`, `isDelete` )
        VALUES
        ( #{couponNo}, #{couponName}, #{categoryId}, #{validityPeriod}, #{couponAmount}, #{description}, NOW(), NOW(), 0 )
    </insert>

    <update id="update" parameterType="com.jmt.model.zg.ZgProductCouponDto">
        UPDATE `zg_product_coupon` SET
            `couponName` = #{couponName},
            `categoryId` = #{categoryId},
            `validityPeriod` = #{validityPeriod},
            `couponAmount` = #{couponAmount},
            `description` = #{description},
            `updateTime` = NOW()
        WHERE
            `id` = #{id}
    </update>

    <select id="getInfoByNo" parameterType="java.lang.String" resultType="com.jmt.model.zg.ZgProductCouponVo">
        SELECT
            t.`id`,
            t.`couponNo`,
            t.`couponName`,
            t.`categoryId`,
            t.`validityPeriod`,
            t.`couponAmount`,
            t.`description`,
            t.`cretaTime`,
            t.`updateTime`,
            c.categoryName
        FROM
            zg_product_coupon t LEFT JOIN zg_product_category c ON t.categoryId=c.id
        WHERE
            t.isDelete = 0 AND t.`couponNo` = #{couponNo}
    </select>

</mapper>
