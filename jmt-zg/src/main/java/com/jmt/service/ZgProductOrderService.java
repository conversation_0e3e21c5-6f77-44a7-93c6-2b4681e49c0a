package com.jmt.service;

import cn.hutool.core.convert.Convert;
import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.client.ProfitFeignClient;
import com.jmt.client.UaaFeignClient;
import com.jmt.dao.ZgProductCartDao;
import com.jmt.dao.ZgProductOrderDao;
import com.jmt.dao.ZgProductOrderDetailDao;
import com.jmt.enums.PayTypeEnum;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.profit.dto.BalancePayDto;
import com.jmt.model.profit.dto.PointPayDTO;
import com.jmt.model.uaa.UaaUser;
import com.jmt.model.uaa.UaaUserAddress;
import com.jmt.model.uaa.UaaUserWallet;
import com.jmt.model.zg.dto.CreateOrderDTO;
import com.jmt.model.zg.dto.ZgProductOrderDTO;
import com.jmt.model.zg.entity.ZgProductCart;
import com.jmt.model.zg.entity.ZgProductOrder;
import com.jmt.model.zg.entity.ZgProductOrderDetail;
import com.jmt.model.zg.vo.*;
import com.jmt.util.GsonUtil;
import com.jmt.util.ResponseUtil;
import com.jmt.util.SysCodeGenerateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ZgProductOrderService extends BaseService {

    @Resource
    private ZgProductCartDao zgProductCartDao;

    @Resource
    private ZgProductOrderDao zgProductOrderDao;

    @Resource
    private ZgProductOrderDetailDao zgProductOrderDetailDao;

    @Resource
    private UaaFeignClient uaaFeignClient;

    @Resource
    private ProfitFeignClient profitFeignClient;


    public PageResult<ZgProductOrderIndexVO> getPage(PageQuery<ZgProductOrderDTO> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());

        ZgProductOrderDTO queryCondition = pageQuery.getQueryData() != null ?
                pageQuery.getQueryData() : new ZgProductOrderDTO();

        List<ZgProductOrderIndexVO> list = zgProductOrderDao.selectByCondition(queryCondition);
        enrichOrderInfo(list);
        return new PageResult<>(list);
    }

    /**
     * 补充订单信息（用户信息和地址信息）
     */
    private void enrichOrderInfo(List<ZgProductOrderIndexVO> orderList) {
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }

        try {
            Set<Long> uaaIds = new HashSet<>();
            Set<Long> addressIds = new HashSet<>();

            for (ZgProductOrderIndexVO order : orderList) {
                if (order.getBuyUserId() != null) {
                    uaaIds.add(order.getBuyUserId());
                }
                if (order.getAddressId() != null) {
                    addressIds.add(order.getAddressId());
                }
            }

            Map<Long, UaaUser> userMap = new HashMap<>();
            if (!uaaIds.isEmpty()) {
                try {
                    String uaaByUaaIds = uaaFeignClient.getUaaByUaaIds(new ArrayList<>(uaaIds));
                    List<UaaUser> userList = ResponseUtil.getDataList(uaaByUaaIds, UaaUser.class);

                    if (userList != null) {
                        for (UaaUser user : userList) {
                            userMap.put(user.getUaaId(), user);
                        }
                    }
                } catch (Exception e) {
                    log.error("通过Feign获取用户信息失败", e);
                }
            }
            Map<Long, UaaUserAddress> addressMap = new HashMap<>();
            if (!addressIds.isEmpty()) {
                try {
                    String addressByIds = uaaFeignClient.getAddressByIds(new ArrayList<>(addressIds));
                    List<UaaUserAddress> addressList = ResponseUtil.getDataList(addressByIds, UaaUserAddress.class);
                    if (addressList != null) {
                        for (UaaUserAddress address : addressList) {
                            addressMap.put(address.getId(), address);
                        }
                    }
                } catch (Exception e) {
                    log.error("通过Feign获取地址信息失败", e);
                }
            }

            for (ZgProductOrderIndexVO order : orderList) {
                // 填充用户信息
                UaaUser user = userMap.get(order.getBuyUserId());
                if (user != null) {
                    order.setLoginName(user.getLoginName());
                } else {
                    order.setLoginName("未知用户");
                }

                // 填充地址信息
                UaaUserAddress address = addressMap.get(order.getAddressId());
                if (address != null) {
                    order.setProvince(address.getProvince());
                    order.setCity(address.getCity());
                    order.setDetailAddress(address.getDetailAddress());
                } else {
                    order.setProvince("");
                    order.setCity("");
                    order.setDetailAddress("");
                }
            }

        } catch (Exception e) {
            log.error("通过Feign获取用户信息失败", e);
            // 这里可以选择记录日志但不抛出异常，保证主流程可用
        }
    }



    @Transactional
    public ZgProductOrder createOrder(Long buyUserId, CreateOrderDTO createOrderDTO) {
        List<ZgProductCart> cartItems = zgProductCartDao.selectByIdsAndUserId(createOrderDTO.getCartIds(), buyUserId);
        if (CollectionUtils.isEmpty(cartItems)) {
            throw new RuntimeException("购物车商品不存在或已删除");
        }

        // 2. 计算订单总金额
        BigDecimal totalAmount = calculateTotalAmount(cartItems);

        // 生成订单（状态为待支付）
        ZgProductOrder order = buildOrder(buyUserId, totalAmount);
        order.setAddressId(createOrderDTO.getAddressId());
        zgProductOrderDao.insert(order);

        // 生成订单明细
        createOrderDetails(order.getOrderNo(), cartItems);

        // 5. 清空购物车
        zgProductCartDao.deleteByIds(createOrderDTO.getCartIds());


        return order;
    }

    /**
     * 处理支付
     */
    @Transactional
    public boolean processPayment(Long uaaId, ProductPayVO productPayVO) {
        // 1. 验证订单
        ZgProductOrder order = zgProductOrderDao.selectByOrderNo(productPayVO.getOrderNo());
        if (order == null || !order.getOrderStatus().equals(0)) {
            throw new RuntimeException("订单不存在或已支付");
        }

        // 2. 更新支付方式
        order.setPayType(productPayVO.getPayType());
       // orderMapper.updateById(order);

        //校验是否设置支付密码
        String walletRes =uaaFeignClient.getWalletByUaaId(uaaId);
        UaaUserWallet wallet= ResponseUtil.getData(walletRes,UaaUserWallet.class);
        if (wallet == null) {
            throw new RuntimeException("用户钱包不存在");
        }
        if (StringUtils.isEmpty(wallet.getPayPassword())){
            log.error("未设置密码");
            throw new RuntimeException("未设置密码");
        }

        // 3. 根据支付类型处理支付
        boolean paymentResult = false;

        //去支付
        PayTypeEnum payTypeEnum = PayTypeEnum.getByCode(productPayVO.getPayType());
        switch (payTypeEnum) {
            case POINTS_PAY:
                PointPayDTO pointPayDTO = new PointPayDTO();
                pointPayDTO.setUaaId(uaaId);
                pointPayDTO.setPoints(order.getPayAmount().intValue()*100);
                pointPayDTO.setBusUaaId(1L);
                pointPayDTO.setOrderNo(order.getOrderNo());
                pointPayDTO.setPayPassword(productPayVO.getPayPassword());
                log.info("----------point");
                String payRes = profitFeignClient.pointsByUaaIdPay(pointPayDTO);

                Map<String, Object> responseMap = GsonUtil.fromJsonMap(payRes);
                String code = Convert.toStr(responseMap.get("code"));
                if(!"200".equals(code)){
                    String errorMsg = Convert.toStr(responseMap.get("msg"));
                    throw new RuntimeException(errorMsg);
                }else{
                    paymentResult=true;
                }

                break;
            case BALANCE_PAY:
                BalancePayDto payDTO = new BalancePayDto();
                payDTO.setUaaId(uaaId);
                payDTO.setBalance(order.getPayAmount());
                payDTO.setBusUaaId(1L);
                payDTO.setOrderNo(order.getOrderNo());
                payDTO.setPayPassword(productPayVO.getPayPassword());
                String balPayRes = profitFeignClient.balanceByUaaIdPay(payDTO);
                Map<String, Object> balResponseMap = GsonUtil.fromJsonMap(balPayRes);
                String balCode = Convert.toStr(balResponseMap.get("code"));
                if(!"200".equals(balCode)){
                    String errorMsg = Convert.toStr(balResponseMap.get("msg"));
                    throw new RuntimeException(errorMsg);
                }else{
                    paymentResult=true;
                }

                break;
//                case WECHAT_PAY:
//                    break;
//                case ALIPAY_PAY:
//                    break;
            default:
                throw new RuntimeException("不支持的支付方式: " + payTypeEnum.getMessage());
        }

        // 更新订单状态
        if (paymentResult) {
            updateOrderStatus(productPayVO.getOrderNo(),productPayVO.getPayType(), 1); // 支付成功
        } else {
            updateOrderStatus(productPayVO.getOrderNo(),productPayVO.getPayType(), 2); // 支付失败
        }

        return paymentResult;
    }

    private ZgProductOrder buildOrder(Long buyUserId, BigDecimal totalAmount) {
        ZgProductOrder order = new ZgProductOrder();
        String orderNo = SysCodeGenerateUtil.generateProfitCode("001");
        order.setOrderNo(orderNo);
        order.setOrderName("筷圣直供商品订单");
        order.setBuyUserId(buyUserId);
        order.setPayType(null);
        order.setPayAmount(totalAmount);
        order.setOrderStatus(0); // 0-未支付
        order.setCreateTime(new Date());
        order.setUpdateTime(new Date());
        order.setIsDelete(0);
        return order;
    }





    /**
     * 计算订单总金额
     */
    private BigDecimal calculateTotalAmount(List<ZgProductCart> cartItems) {
        return cartItems.stream()
                .map(item -> item.getCurrentPrice().multiply(new BigDecimal(item.getQuantity())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }



    /**
     * 创建订单明细
     */
    private void createOrderDetails(String orderNo, List<ZgProductCart> cartItems) {
        for (ZgProductCart cartItem : cartItems) {
            ZgProductOrderDetail detail = new ZgProductOrderDetail();
            detail.setOrderNo(orderNo);
            detail.setSupplierNo(cartItem.getSupplierNo());
            detail.setProductNo(cartItem.getProductNo());
            detail.setProductName(cartItem.getProductName());
            detail.setBuyUserId(cartItem.getBuyUserId());
            detail.setOriginalPrice(cartItem.getOriginalPrice());
            detail.setCurrentPrice(cartItem.getCurrentPrice());
            detail.setQuantity(cartItem.getQuantity());
            detail.setCreateTime(new Date());
            detail.setUpdateTime(new Date());
            detail.setIsDelete(0);

            zgProductOrderDetailDao.insert(detail);
        }
    }

    /**
     * 根据订单号查询订单
     */
    public ZgProductOrder getOrderByNo(String orderNo) {
        return zgProductOrderDao.selectByOrderNo(orderNo);
    }

    @Transactional
    public boolean assignDriverToOrder(String orderNo, String driverNo) {
        try {
            ZgProductOrder zgProductOrder = zgProductOrderDao.selectByOrderNo(orderNo);
            if(zgProductOrder.getOrderStatus() != 1){
                throw new RuntimeException("未支付成功，无法分配");
            }
            int affectedRows = zgProductOrderDao.assignDriver(orderNo, driverNo);
            if (affectedRows > 0) {
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            log.error("分配配送员时发生异常，订单ID: {}, 配送员编号: {}", orderNo, driverNo, e);
            throw new RuntimeException("分配配送员失败", e);
        }
    }




    /**
     * 根据用户ID查询订单列表
     */
//    public List<ZgProductOrder> getOrdersByUserId(Long buyUserId) {
//        return zgProductOrderDao.selectByUserId(buyUserId);
//    }

    public ZgProductOrderDetailVO getOrderDetail(String orderNo) {
        ZgProductOrderDetailVO orderVO = zgProductOrderDao.selectPcOrderDetails(orderNo);

        if (orderVO == null) {
            return null;
        }
        String userAddress = uaaFeignClient.getById(orderVO.getAddressId());
        UaaUserAddress data = ResponseUtil.getData(userAddress, UaaUserAddress.class);
        orderVO.setProvince(data.getProvince());
        orderVO.setCity(data.getCity());
        orderVO.setAddress(data.getDetailAddress());

        List<ProductOrderDetailVO> detailList = zgProductOrderDetailDao.selectOrderDetailList(orderNo);
        orderVO.setDetailVOList(detailList);

        return orderVO;
    }

    public PageResult<ZgProductOrderAppVO> getAppOrdersByUserId(Long buyUserId, Integer orderStatus, Integer pageNo, Integer pageSize) {
        PageHelper.startPage(pageNo, pageSize);

        // 创建查询对象
        ZgProductOrder query = new ZgProductOrder();
        query.setBuyUserId(buyUserId);
        query.setOrderStatus(orderStatus);

        List<ZgProductOrderAppVO> orderList = zgProductOrderDao.selectOrderBaseByUserId(query);

        if (CollectionUtils.isEmpty(orderList)) {
            return new PageResult<>(Collections.emptyList());
        }

        List<ZgProductOrderDetailAppVO> allDetails = zgProductOrderDao.selectOrderDetailsByUser(buyUserId);

        Map<String, List<ZgProductOrderDetailAppVO>> detailsByOrderNo = allDetails.stream()
                .collect(Collectors.groupingBy(ZgProductOrderDetailAppVO::getOrderNo));

        for (ZgProductOrderAppVO order : orderList) {
            List<ZgProductOrderDetailAppVO> orderDetails = detailsByOrderNo.get(order.getOrderNo());
            if (orderDetails != null) {
                order.setDetailAppVOList(orderDetails);
            } else {
                order.setDetailAppVOList(Collections.emptyList());
            }
        }

        return new PageResult<>(orderList);
    }

    /**
     * 查询订单详情
     */
    public List<ZgProductOrderDetail> getOrderDetails(String orderNo) {
        return zgProductOrderDetailDao.selectByOrderNo(orderNo);
    }

    /**
     * 更新订单状态
     */
    public boolean updateOrderStatus(String orderNo, Integer orderStatus,Integer payType) {
        int result = zgProductOrderDao.updateOrderStatus(orderNo, orderStatus,payType, new Date());
        return result > 0;
    }

    /**
     * 删除订单（软删除）
     */
    public int deleteOrder(String orderNo) {
        ZgProductOrder order = zgProductOrderDao.selectByOrderNo(orderNo);
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }
        return zgProductOrderDao.delete(order.getId());
    }

    /**
     * 取消订单
     */
//    @Transactional
//    public boolean cancelOrder(String orderNo) {
//        ZgProductOrder order = zgProductOrderDao.selectByOrderNo(orderNo);
//        if (order == null || !order.getOrderStatus().equals(0)) {
//            return false;
//        }
//        return updateOrderStatus(orderNo, 3);
//    }



}
