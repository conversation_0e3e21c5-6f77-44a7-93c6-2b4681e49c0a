package com.jmt.service;

import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.dao.ProductCouponDao;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.zg.ZgProductCouponDto;
import com.jmt.model.zg.ZgProductCouponVo;
import com.jmt.util.SysCodeGenerateUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Service
public class ProductCouponService extends BaseService {

    @Resource
    private ProductCouponDao productCouponDao;

    public PageResult<ZgProductCouponVo> getPage(PageQuery<ZgProductCouponDto> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        ZgProductCouponDto dto = pageQuery.getQueryData();
        List<ZgProductCouponVo> list = productCouponDao.getPage(dto);
        if (list != null) {
            return new PageResult<>(list);
        }
        return null;
    }

    public ZgProductCouponVo getInfo(Long id) {
        return productCouponDao.getInfo(id);
    }

    @Transactional
    public Integer add(ZgProductCouponDto dto){
        String couponNo = SysCodeGenerateUtil.generateUserCode("JMT-P2-");
        ZgProductCouponVo vo = productCouponDao.getInfoByNo(couponNo);
        while (ObjectUtil.isNotEmpty(vo)) {
            couponNo = SysCodeGenerateUtil.generateUserCode("JMT-P2-");
            vo = productCouponDao.getInfoByNo(couponNo);
        }
        return productCouponDao.insert(dto);
    }

    @Transactional
    public Integer edit(ZgProductCouponDto dto){
        return productCouponDao.update(dto);
    }

    public Integer delete(Long id) {
        return 1;
    }
}
