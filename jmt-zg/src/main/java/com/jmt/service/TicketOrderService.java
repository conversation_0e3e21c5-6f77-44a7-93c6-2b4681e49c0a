package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jmt.base.BaseService;
import com.jmt.dao.ZgTicketOrderMapper;
import com.jmt.model.page.PageQuery;
import com.jmt.model.zg.TicketOrderDetail;
import com.jmt.model.zg.ZgTicketOrder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class TicketOrderService extends BaseService {
    // 订单状态常量
    private static final int ORDER_STATUS_UNPAID = 0; // 未支付
    private static final int ORDER_STATUS_PAID = 1; // 支付成功
    private static final int ORDER_STATUS_REFUNDED = 3; // 已退款
    private static final int ORDER_STATUS_CANCELED = 3; // 已取消（与表结构定义保持一致）

    @Resource
    private ZgTicketOrderMapper zgTicketOrderMapper;

    /**

     分页查询票务订单信息
     */
    public PageInfo<TicketOrderDetail> getTicketInfoPage(PageQuery<TicketOrderDetail> pageQuery) {
        if (pageQuery == null) {
            throw new IllegalArgumentException ("分页查询参数不能为空");
        }
        PageHelper.startPage (pageQuery.getPageNo (), pageQuery.getPageSize ());
        List<TicketOrderDetail> list = zgTicketOrderMapper.getTicketInfoPage(pageQuery.getQueryData());
        return new PageInfo<>(list);
    }

    /**

     获取订单详情（主信息 + 明细）
     */
    public TicketOrderDetail getTicketOrderDetail (Long orderId) {
        if (orderId == null || orderId <= 0) {
            throw new IllegalArgumentException ("订单 ID 无效");
        }
        // 查询主订单
        ZgTicketOrder order = zgTicketOrderMapper.selectByPrimaryKey (orderId);
        if (order == null) {
            throw new IllegalStateException ("订单不存在");
        }
        // 查询订单明细
        TicketOrderDetail queryParam = new TicketOrderDetail ();
        queryParam.setOrderNo (order.getOrderNo ());
        List<TicketOrderDetail> details = zgTicketOrderMapper.getTicketInfoPage (queryParam);
        if (details.isEmpty ()) {
            throw new IllegalStateException ("订单明细不存在");
        }
        // 组装返回结果（主信息合并到第一条明细）
        TicketOrderDetail result = details.get (0);
        result.setOrderName (order.getOrderName ());
        result.setPayType (order.getPayType ());
        result.setPayAmount (order.getPayAmount ());
        result.setOrderStatus (order.getOrderStatus ());
        result.setCreateTime (order.getCreateTime ());
        return result;
    }

    /**

     订单退款处理
     */
    @Transactional (rollbackFor = Exception.class)
    public boolean refundTicketOrder (Long orderId) throws Exception {
        if (orderId == null || orderId <= 0) {
            throw new IllegalArgumentException ("订单 ID 无效");
        }
        // 查询订单
        ZgTicketOrder order = zgTicketOrderMapper.selectByPrimaryKey (orderId);
        if (order == null) {
            throw new IllegalStateException ("订单不存在");
        }
        // 校验状态：仅支付成功的订单可退款
        if (order.getOrderStatus () != ORDER_STATUS_PAID) {
            throw new IllegalStateException ("当前订单状态不支持退款操作");
        }
        // 更新订单状态
        order.setOrderStatus (ORDER_STATUS_REFUNDED);
        order.setUpdateTime (new Date ());
        int rows = zgTicketOrderMapper.updateByPrimaryKeySelective (order);
        if (rows <= 0) {
            throw new IllegalStateException ("订单状态更新失败");
        }
        // 调用支付系统退款接口（实际业务中实现）
        //refundService.processRefund (order.getOrderNo (), order.getPayAmount ());
        return true;
    }

    /**

     取消未支付订单
     */
    @Transactional (rollbackFor = Exception.class)
    public boolean cancelTicketOrder (Long orderId) throws Exception {
        if (orderId == null || orderId <= 0) {
            throw new IllegalArgumentException ("订单 ID 无效");
        }
        // 查询订单
        ZgTicketOrder order = zgTicketOrderMapper.selectByPrimaryKey (orderId);
        if (order == null) {
            throw new IllegalStateException ("订单不存在");
        }
        // 校验状态：仅未支付订单可取消
        if (order.getOrderStatus () != ORDER_STATUS_UNPAID) {
            throw new IllegalStateException ("当前订单状态不支持取消操作");
        }
        // 更新订单状态
        order.setOrderStatus (ORDER_STATUS_CANCELED);
        order.setUpdateTime (new Date ());
        int rows = zgTicketOrderMapper.updateByPrimaryKeySelective (order);
        if (rows <= 0) {
            throw new IllegalStateException ("订单状态更新失败");
        }
        // 释放相关库存（实际业务中实现）
        //releaseStock (order.getOrderNo ());
        return true;
    }
}
