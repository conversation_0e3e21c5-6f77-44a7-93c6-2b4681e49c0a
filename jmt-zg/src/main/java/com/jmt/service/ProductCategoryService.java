package com.jmt.service;

import com.jmt.base.BaseService;
import com.jmt.dao.ProductCategoryDao;
import com.jmt.dao.ZgProductInfoDao;
import com.jmt.model.zg.ZgProductCategoryVo;
import com.jmt.model.zg.entity.ZgProductCategory;
import com.jmt.model.zg.vo.ZgProductCategoryChildVo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ProductCategoryService extends BaseService {

    @Resource
    private ProductCategoryDao categoryMapper;

    @Resource
    private ZgProductInfoDao zgProductInfoDao;

    public List<ZgProductCategoryVo> selectCategory() {
        return categoryMapper.selectCategory();
    }

    public int updatePicture(ZgProductCategoryVo vo){
        return categoryMapper.updateById(vo);
    }



    public List<ZgProductCategoryVo> getCategoryTreeDirect() {
        List<ZgProductCategoryVo> level1Categories = categoryMapper.selectLevel1Categories();

        if (CollectionUtils.isEmpty(level1Categories)) {
            return Collections.emptyList();
        }

        List<Long> allSubCategoryIds = new ArrayList<>();
        Map<Long, List<ZgProductCategoryChildVo>> level1ToSubCategoriesMap = new HashMap<>();

        for (ZgProductCategoryVo level1 : level1Categories) {
            List<ZgProductCategoryChildVo> subCategories = categoryMapper.selectSubCategoriesByParentId(level1.getId());
            if (!CollectionUtils.isEmpty(subCategories)) {
                level1ToSubCategoriesMap.put(level1.getId(), subCategories);
                subCategories.forEach(sub -> allSubCategoryIds.add(sub.getId()));
            }
        }

        final List<Long> categoryIdsWithProducts;
        if (!CollectionUtils.isEmpty(allSubCategoryIds)) {
            categoryIdsWithProducts = zgProductInfoDao.findCategoryIdsWithProducts(allSubCategoryIds);
        } else {
            categoryIdsWithProducts = Collections.emptyList();
        }

        List<ZgProductCategoryVo> result = new ArrayList<>();
        for (ZgProductCategoryVo level1 : level1Categories) {
            List<ZgProductCategoryChildVo> subCategories = level1ToSubCategoriesMap.get(level1.getId());
            if (subCategories != null) {
                // 过滤出有商品的二级品类
                List<ZgProductCategoryChildVo> filteredSubCategories = subCategories.stream()
                        .filter(sub -> categoryIdsWithProducts.contains(sub.getId()))
                        .collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(filteredSubCategories)) {
                    level1.setSubCategories(filteredSubCategories);
                    result.add(level1);
                }
            }
        }

        return result;
    }

    public int insert(ZgProductCategory zgProductCategory){
        return categoryMapper.insert(zgProductCategory);
    }
}
