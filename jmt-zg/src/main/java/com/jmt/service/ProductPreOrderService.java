package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jmt.dao.ZgProductDeliveryOrderMapper;
import com.jmt.dao.ZgProductOrderMapper;
import com.jmt.dao.ZgProductPreOrderDetailMapper;
import com.jmt.dao.ZgProductPreOrderMapper;
import com.jmt.model.page.PageQuery;
import com.jmt.model.zg.ZgProductDeliveryOrder;
import com.jmt.model.zg.ZgProductOrder;
import com.jmt.model.zg.ZgProductPreOrder;
import com.jmt.model.zg.ZgProductPreOrderDetail;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class ProductPreOrderService {

    // 订单状态常量定义
    private static final int ORDER_STATUS_PENDING = 0;    // 待确认
    private static final int ORDER_STATUS_CONFIRMED = 1;  // 已确认
    private static final int ORDER_STATUS_REJECTED = 3;   // 已拒绝
    private static final int DELIVERY_STATUS_PENDING = 0; // 配送单待确认
    private static final int NOT_DELETED = 0;             // 未删除

    @Resource
    private ZgProductPreOrderMapper productPreOrderMapper;
    @Resource
    private ZgProductOrderMapper productOrderMapper;
    @Resource
    private ZgProductDeliveryOrderMapper productDeliveryOrderMapper;
    @Resource
    private ZgProductPreOrderDetailMapper preOrderDetailMapper;

    /**
     * 根据订单编号查询预订单
     */
    public ZgProductPreOrder getPreOrderByOrderNo(String orderNo) {
        if (orderNo == null || orderNo.trim().isEmpty()) {
            throw new IllegalArgumentException("订单编号不能为空");
        }
        return productPreOrderMapper.selectByOrderNo(orderNo);
    }

    /**
     * 根据订单编号确认预订单
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean confirmPreOrderByOrderNo(String orderNo) {
        ZgProductPreOrder preOrder = getValidPreOrder(orderNo);

        preOrder.setOrderStatus(ORDER_STATUS_CONFIRMED);
        preOrder.setUpdateTime(new Date());
        return productPreOrderMapper.updateByPrimaryKeySelective(preOrder) > 0;
    }

    /**
     * 根据订单编号拒绝预订单
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean rejectPreOrderByOrderNo(String orderNo, String reason) {
        if (reason == null || reason.trim().isEmpty()) {
            throw new IllegalArgumentException("拒绝原因不能为空");
        }

        ZgProductPreOrder preOrder = getValidPreOrder(orderNo);

        preOrder.setOrderStatus(ORDER_STATUS_REJECTED);
        preOrder.setReason(reason);
        preOrder.setUpdateTime(new Date());
        return productPreOrderMapper.updateByPrimaryKeySelective(preOrder) > 0;
    }

    /**
     * 根据预订单编号生成配送单
     */
    @Transactional(rollbackFor = Exception.class)
    public ZgProductDeliveryOrder generateDeliveryOrderByOrderNo(String preOrderNo) {
        ZgProductPreOrder preOrder = getValidPreOrder(preOrderNo);

        // 构建配送单信息
        ZgProductDeliveryOrder deliveryOrder = new ZgProductDeliveryOrder();
        deliveryOrder.setOrderNo(generateDeliveryOrderNo());
        deliveryOrder.setOrderName(preOrder.getOrderName());
        deliveryOrder.setDriverNo(preOrder.getSupplierNo());
        deliveryOrder.setProductTotal(preOrder.getProductTotal());
        deliveryOrder.setOrderCount(preOrder.getOrderCount());
        deliveryOrder.setOrderStatus(DELIVERY_STATUS_PENDING);
        deliveryOrder.setCreateTime(new Date());
        deliveryOrder.setUpdateTime(new Date());
        deliveryOrder.setIsDelete(NOT_DELETED);

        productDeliveryOrderMapper.insertSelective(deliveryOrder);
        return deliveryOrder;
    }

    /**
     * 分页查询预订单列表
     */
    public PageInfo<ZgProductPreOrder> getPreOrderPage(PageQuery<ZgProductPreOrder> pageQuery) {
        if (pageQuery == null) {
            throw new IllegalArgumentException("分页查询参数不能为空");
        }

        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<ZgProductPreOrder> preOrderList = productPreOrderMapper.getTicketInfoPage(pageQuery.getQueryData());
        return new PageInfo<>(preOrderList);
    }

    /**
     * 分页查询司机相关预订单信息
     */
    public PageInfo<ZgProductPreOrder> getDriverInfoPage(PageQuery<ZgProductPreOrder> pageQuery) {
        if (pageQuery == null) {
            throw new IllegalArgumentException("分页查询参数不能为空");
        }

        // 设置分页参数
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());

        // 获取查询条件
        ZgProductPreOrder query = pageQuery.getQueryData();

        // 执行数据库查询
        List<ZgProductPreOrder> preOrderList = productPreOrderMapper.getTicketInfoPage(query);

        // 封装分页结果
        return new PageInfo<>(preOrderList);
    }

    /**
     * 创建商品预订单
     */
    @Transactional(rollbackFor = Exception.class)
    public ZgProductPreOrder createProductPreOrder(String orderNo) {
        // 查询原商品订单
        ZgProductOrder productOrder = productOrderMapper.selectByOrderNo(orderNo);
        if (productOrder == null) {
            throw new IllegalArgumentException("商品订单不存在，orderNo: " + orderNo);
        }

        // 构建预订单基本信息
        Date now = new Date();
        ZgProductPreOrder preOrder = new ZgProductPreOrder();
        preOrder.setOrderNo(generatePreOrderNo());
        preOrder.setOrderName(productOrder.getOrderName());
        preOrder.setSupplierNo(productOrder.getSupplierNo());
        preOrder.setTicketNo(productOrder.getTicketNo());
        preOrder.setTicketName(productOrder.getTicketName());
        preOrder.setProductTotal(productOrder.getProductNum());
        preOrder.setAmountTotal(productOrder.getCurrentPrice());
        preOrder.setOrderCount(1); // 默认为1个订单，可根据实际业务调整
        preOrder.setCreateTime(now);
        preOrder.setUpdateTime(now);
        preOrder.setIsDelete(NOT_DELETED);
        preOrder.setOrderStatus(ORDER_STATUS_PENDING);

        productPreOrderMapper.insertSelective(preOrder);

        // 保存预订单明细
        ZgProductPreOrderDetail detail = new ZgProductPreOrderDetail();
        detail.setProductPreOrderNo(preOrder.getOrderNo());
        detail.setProductOrderNo(productOrder.getOrderNo());
        detail.setCreateTime(now);
        detail.setUpdateTime(now);
        detail.setIsDelete(NOT_DELETED);
        preOrderDetailMapper.insertSelective(detail);

        return preOrder;
    }

    /**
     * 生成预订单编号
     */
    private String generatePreOrderNo() {
        return "PRE" + System.currentTimeMillis();
    }

    /**
     * 生成配送单编号
     */
    private String generateDeliveryOrderNo() {
        return "DEL" + System.currentTimeMillis();
    }

    /**
     * 获取有效的预订单（校验存在性）
     */
    private ZgProductPreOrder getValidPreOrder(String orderNo) {
        ZgProductPreOrder preOrder = getPreOrderByOrderNo(orderNo);
        if (preOrder == null) {
            throw new IllegalStateException("预订单不存在，orderNo: " + orderNo);
        }
        return preOrder;
    }
}