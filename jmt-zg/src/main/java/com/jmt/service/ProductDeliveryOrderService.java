package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.zxing.WriterException;
import com.jmt.util.QRCodeGenerator;
import com.jmt.base.BaseService;
import com.jmt.dao.ZgProductDeliveryOrderMapper;
import com.jmt.model.page.PageQuery;
import com.jmt.model.zg.ZgProductDeliveryOrder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

@Service
public class ProductDeliveryOrderService extends BaseService {
    // 订单状态常量
    private static final int ORDER_STATUS_CONFIRMED = 1; // 已确认
    private static final int ORDER_STATUS_REJECTED = 2;  // 已拒绝
    private static final int ORDER_STATUS_COMPLETED = 3; // 已完成

    @Resource
    private ZgProductDeliveryOrderMapper orderMapper;

    /**
     * 分页查询发货订单
     */
    public PageInfo<ZgProductDeliveryOrder> getDeliveryOrderPage(PageQuery<ZgProductDeliveryOrder> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<ZgProductDeliveryOrder> list = orderMapper.getDeliveryOrderPage(pageQuery.getQueryData());
        return new PageInfo<>(list);
    }

    /**
     * 验证收货（更新订单状态为已完成）
     */
    public boolean verifyReceipt(String orderNo) {
        ZgProductDeliveryOrder order = orderMapper.selectByOrderNo(orderNo);
        if (order == null) {
            return false;
        }
        order.setOrderStatus(ORDER_STATUS_COMPLETED);
        return orderMapper.updateByPrimaryKeySelective(order) > 0;
    }

    /**
     * 生成收货二维码（首次生成时保存二维码内容）
     */
    public byte[] generateReceiptQRCode(String orderNo) throws WriterException, IOException {
        ZgProductDeliveryOrder order = orderMapper.selectByOrderNo(orderNo);
        if (order == null) {
            throw new IllegalArgumentException("订单不存在，orderNo：" + orderNo);
        }

        // 首次生成时保存二维码内容
        if (order.getTakeQrCode() == null) {
            String qrContent = "DeliveryOrderNo: " + order.getOrderNo(); // 二维码包含订单编号
            order.setTakeQrCode(qrContent);
            orderMapper.updateByPrimaryKeySelective(order);
        }

        // 生成二维码图片
        return QRCodeGenerator.generateQRCodeImage(order.getTakeQrCode(), 200, 200);
    }

    /**
     * 确认发货订单（更新状态为已确认）
     */
    public boolean confirmDeliveryOrder(String orderNo) {
        ZgProductDeliveryOrder order = getOrderByNo(orderNo);
        if (order == null) {
            return false;
        }
        order.setOrderStatus(ORDER_STATUS_CONFIRMED);
        return orderMapper.updateByPrimaryKeySelective(order) > 0;
    }

    /**
     * 拒绝发货订单（更新状态为已拒绝，记录原因）
     */
    public boolean rejectDeliveryOrder(String orderNo, String reason) {
        ZgProductDeliveryOrder order = getOrderByNo(orderNo);
        if (order == null) {
            return false;
        }
        order.setOrderStatus(ORDER_STATUS_REJECTED);
        order.setReason(reason);
        return orderMapper.updateByPrimaryKeySelective(order) > 0;
    }

    /**
     * 根据订单号查询订单
     */
    private ZgProductDeliveryOrder getOrderByNo(String orderNo) {
        return orderMapper.selectByOrderNo(orderNo);
    }
}