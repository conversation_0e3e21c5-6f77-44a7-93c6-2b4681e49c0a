package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.google.zxing.WriterException;
import com.jmt.util.QRCodeGenerator;
import com.jmt.base.BaseService;
import com.jmt.dao.ZgProductOrderMapper;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.zg.ZgProductOrder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Date;
import java.util.List;

@Service
public class ProductOrderService extends BaseService {
    // 订单状态常量定义
    private static final int ORDER_STATUS_PAID = 1; // 已支付
    private static final int ORDER_STATUS_SHIPPED = 3; // 已发货（可提货）
    private static final int ORDER_STATUS_RECEIVED = 4; // 已收货（核销完成）
    private static final int ORDER_STATUS_CANCELED = 5; // 已取消
    private static final int ORDER_STATUS_REFUNDED = 6; // 已退款

    @Resource
    private ZgProductOrderMapper orderMapper;

    /**

     分页查询商品订单
     */
    public PageResult<ZgProductOrder> getZgProductOrderPage(PageQuery<ZgProductOrder> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        ZgProductOrder queryData = pageQuery.getQueryData();
        List<ZgProductOrder> list = orderMapper.getProductOrderPage(queryData);
        return new PageResult<>(list);
    }

    /**

     根据订单编号查询商品订单
     */
    public ZgProductOrder getProductOrderByOrderNo (String orderNo) {
        return orderMapper.selectByOrderNo (orderNo);
    }

    /**

     生成商品订单提货二维码
     仅允许已支付（1）或已发货（3）状态的订单生成
     */
    public byte [] generatePickupQRCode (String orderNo) throws WriterException, IOException {
        ZgProductOrder order = getProductOrderByOrderNo (orderNo);
        if (order == null) {
            throw new IllegalArgumentException ("订单不存在，orderNo：" + orderNo);
        }
        // 校验订单状态是否允许生成提货码
        if (order.getOrderStatus () != ORDER_STATUS_PAID && order.getOrderStatus () != ORDER_STATUS_SHIPPED) {
            throw new RuntimeException ("订单状态异常，当前状态：" + order.getOrderStatus () + "，无法生成提货码");
        }
        // 生成二维码内容（包含订单号和时间戳，确保唯一性）
        String qrContent = "OrderNo:" + order.getOrderNo () + ",Time:" + System.currentTimeMillis ();
        // 更新订单状态为已发货（可提货），并保存二维码内容
        order.setUsedQrCode (qrContent);
        order.setOrderStatus (ORDER_STATUS_SHIPPED);
        order.setUpdateTime (new Date ());
        orderMapper.updateByPrimaryKeySelective (order);
        // 生成二维码图片
        return QRCodeGenerator.generateQRCodeImage (qrContent, 200, 200);
    }

    /**

     商品订单提货核销
     仅已发货（3）状态可核销为已收货（4）
     */
    public boolean verifyPickup (String orderNo) {
        ZgProductOrder order = getProductOrderByOrderNo (orderNo);
        if (order == null) {
            return false;
        }
        // 校验状态：仅已发货状态可核销
        if (order.getOrderStatus () != ORDER_STATUS_SHIPPED) {
            return false;
        }
        // 更新为已收货状态
        order.setOrderStatus (ORDER_STATUS_RECEIVED);
        order.setUpdateTime (new Date ());
        return orderMapper.updateByPrimaryKeySelective (order) > 0;
    }

    /**

     取消商品订单
     仅已支付（1）或待支付（0）状态可取消
     */
    public boolean cancelProductOrderByOrderNo (String orderNo) {
        ZgProductOrder order = getProductOrderByOrderNo (orderNo);
        if (order == null) {
            return false;
        }
        // 校验状态：仅待支付或已支付可取消
        if (order.getOrderStatus () != 0 && order.getOrderStatus () != ORDER_STATUS_PAID) {
            return false;
        }
        // 更新为已取消状态
        order.setOrderStatus (ORDER_STATUS_CANCELED);
        order.setUpdateTime (new Date ());
        return orderMapper.updateByPrimaryKeySelective (order) > 0;
    }

    /**

     商品订单退款
     仅已支付（1）或已发货（3）状态可退款
     */
    public boolean refundProductOrderByOrderNo (String orderNo) {
        ZgProductOrder order = getProductOrderByOrderNo (orderNo);
        if (order == null) {
            return false;
        }
        // 校验状态：仅已支付或已发货可退款
        if (order.getOrderStatus () != ORDER_STATUS_PAID && order.getOrderStatus () != ORDER_STATUS_SHIPPED) {
            return false;
        }
        // 更新为已退款状态
        order.setOrderStatus (ORDER_STATUS_REFUNDED);
        order.setUpdateTime (new Date ());
        return orderMapper.updateByPrimaryKeySelective (order) > 0;
    }
}