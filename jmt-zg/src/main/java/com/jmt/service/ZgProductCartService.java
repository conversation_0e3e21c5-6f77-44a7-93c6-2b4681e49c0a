package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.dao.ZgProductCartDao;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.zg.dto.CreateOrderDTO;
import com.jmt.model.zg.entity.ZgProductCart;
import com.jmt.model.zg.vo.ZgProductCartVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Service
public class ZgProductCartService extends BaseService {

    @Resource
    private ZgProductCartDao zgProductCartDao;

    public PageResult<ZgProductCartVO> getPage(PageQuery<CreateOrderDTO> pageQuery, Long uaaId) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        CreateOrderDTO queryCondition = pageQuery.getQueryData() != null ?
                pageQuery.getQueryData() : new CreateOrderDTO();
        List<ZgProductCartVO> list = zgProductCartDao.selectCartWithProductInfo(uaaId,queryCondition.getCartIds());
        return new PageResult<>(list);
    }


    @Transactional
    public Long add(ZgProductCart dto) {
        zgProductCartDao.insert(dto);
        Long generatedId = dto.getId();
        return generatedId;
    }
    /**
     * 添加商品到购物车（如果已存在则更新数量）
     */
    @Transactional
    public Long addOrUpdate(ZgProductCart dto) {
        ZgProductCart existingItem = zgProductCartDao.findByUserAndProduct(
                dto.getBuyUserId(), dto.getProductNo());

        if (existingItem != null) {
            Integer newQuantity = existingItem.getQuantity() + dto.getQuantity();
            zgProductCartDao.updateQuantity(
                    existingItem.getId(),
                    newQuantity
            );
            return existingItem.getId();
        } else {
            zgProductCartDao.insert(dto);
            Long generatedId = dto.getId();
            return generatedId;
        }
    }


    /**
     * 清空购物车
     * @param buyUserId
     * @return
     */
    @Transactional
    public int cleanCart(Long buyUserId) {
        return zgProductCartDao.deleteByUserId(buyUserId);
    }

    @Transactional
    public int delete(Long id) {
        return zgProductCartDao.delete(id);
    }

    @Transactional
    public int increaseOne(Long id) {
        return zgProductCartDao.increaseQuantity(id, 1);
    }

    @Transactional
    public int decreaseOne(Long id) {
        return zgProductCartDao.decreaseQuantity(id, 1);
    }

}
