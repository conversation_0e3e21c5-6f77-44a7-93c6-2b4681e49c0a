package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.dao.ZgProductInfoDao;
import com.jmt.dao.ZgSupplierInfoDao;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.zg.dto.ZgProductInfoAppDTO;
import com.jmt.model.zg.dto.ZgProductInfoDTO;
import com.jmt.model.zg.entity.ZgProductInfo;
import com.jmt.model.zg.entity.ZgSupplierInfo;
import com.jmt.model.zg.vo.ZgMallProductListVO;
import com.jmt.model.zg.vo.ZgMyProductListVO;
import com.jmt.model.zg.vo.ZgProductInfoVO;
import com.jmt.util.SysCodeGenerateUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Service
public class ZgProductInfoService extends BaseService {

    @Resource
    private ZgProductInfoDao zgProductInfoDao;

    @Resource
    private ZgSupplierInfoDao zgSupplierInfoDao;

    public PageResult<ZgProductInfoVO> getPage(PageQuery<ZgProductInfo> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());

        ZgProductInfo queryCondition = pageQuery.getQueryData() != null ?
                pageQuery.getQueryData() : new ZgProductInfo();

        List<ZgProductInfoVO> list = zgProductInfoDao.selectByCondition(queryCondition);

        return new PageResult<>(list);
    }

    public PageResult<ZgMyProductListVO> getMyPage(PageQuery<ZgProductInfo> pageQuery,Long uaaId) {
        ZgSupplierInfo zgSupplierInfo = zgSupplierInfoDao.selectByUaaId(uaaId);
        if(zgSupplierInfo == null){
            throw new RuntimeException("暂无商品，请先升级为供应商");
        }

        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        ZgProductInfo queryCondition = pageQuery.getQueryData() != null ?
                pageQuery.getQueryData() : new ZgProductInfo();
        queryCondition.setSupplierNo(zgSupplierInfo.getSupplierNo());

        List<ZgMyProductListVO> list = zgProductInfoDao.selectMyListByCondition(queryCondition);
        return new PageResult<>(list);
    }


    public PageResult<ZgMallProductListVO> getMallPage(PageQuery<ZgProductInfo> pageQuery) {

        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        ZgProductInfo queryCondition = pageQuery.getQueryData() != null ?
                pageQuery.getQueryData() : new ZgProductInfo();

        List<ZgMallProductListVO> list = zgProductInfoDao.selectMallListByCondition(queryCondition);
        list.forEach(vo -> vo.setSalesVolume(0));
        return new PageResult<>(list);
    }

    @Transactional
    public void add(ZgProductInfoDTO dto) {
        ZgProductInfo zgProductInfo = new ZgProductInfo();
        BeanUtils.copyProperties(dto, zgProductInfo);

        String productNo = SysCodeGenerateUtil.generateUserCode("JMT-P1");
        zgProductInfo.setProductNo(productNo);
        zgProductInfo.setAuditStatus(0);
        zgProductInfoDao.insert(zgProductInfo);
    }

    @Transactional
    public void addSj(ZgProductInfoAppDTO dto, LoginUaaUser loginUaaUser) {

        ZgSupplierInfo zgSupplierInfo = zgSupplierInfoDao.selectByUaaId(loginUaaUser.getUaaId());
        if(zgSupplierInfo == null){
            throw new RuntimeException("请先升级为供应商");
        }

        ZgProductInfo zgProductInfo = new ZgProductInfo();
        BeanUtils.copyProperties(dto, zgProductInfo);

        zgProductInfo.setSupplierNo(zgSupplierInfo.getSupplierNo());
        zgProductInfo.setSupplierName(zgSupplierInfo.getSupplierName());
        String productNo = SysCodeGenerateUtil.generateUserCode("JMT-P1");
        zgProductInfo.setProductNo(productNo);
        zgProductInfo.setAuditStatus(0);

        zgProductInfoDao.insert(zgProductInfo);
    }

    @Transactional
    public int update(ZgProductInfo dto) {
        ZgProductInfo zgProductInfo = zgProductInfoDao.selectById(dto.getId());
        if (zgProductInfo == null) {
            throw new RuntimeException("商品不存在");
        }

        return zgProductInfoDao.updateById(dto);
    }

    public ZgProductInfo getById(Long id) {
        return zgProductInfoDao.selectById(id);
    }


    public ZgProductInfo getByProductNo(String productNo) {
        return zgProductInfoDao.selectByProductNo(productNo);
    }


    @Transactional
    public int delete(Long id) {
        ZgProductInfo zgProductInfo = zgProductInfoDao.selectById(id);
        if (zgProductInfo == null) {
            throw new RuntimeException("商品不存在");
        }
        return zgProductInfoDao.delete(id);
    }

    @Transactional
    public int auditProduct(Long id, Integer auditStatus, String reason) {
        ZgProductInfo zgProductInfo = zgProductInfoDao.selectById(id);
        if (zgProductInfo == null) {
            throw new RuntimeException("商品不存在");
        }

        if (auditStatus == 2 && StringUtils.isBlank(reason)) {
            throw new RuntimeException("驳回时必须填写原因");
        }

        zgProductInfo.setAuditStatus(auditStatus);
        zgProductInfo.setReason(reason);
        return zgProductInfoDao.updateAuditStatus(zgProductInfo);
    }
}
