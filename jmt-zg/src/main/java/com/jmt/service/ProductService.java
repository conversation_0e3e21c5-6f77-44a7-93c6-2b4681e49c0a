package com.jmt.service;

import com.jmt.dao.ZgProductMapper;
import com.jmt.model.zg.ZgProduct;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service
public class ProductService {

    @Resource
    private ZgProductMapper productMapper;

    /**
     * 新增商品
     * @param product 商品对象
     * @return 插入条数
     */
    public int addProduct(ZgProduct product) {
        product.setId(null);
        Date now = new Date();

        product.setCreateTime(now);
        product.setUpdateTime(now);
        return productMapper.insertSelective(product);
    }

    /**
     * 编辑商品
     * @param product 商品对象（带上主键 ID）
     * @return 更新条数
     */
    public int editProduct(ZgProduct product) {
        Date now = new Date();
        product.setCreateTime(null);
        product.setUpdateTime(now);
        return productMapper.updateByPrimaryKeySelective(product);
    }

    /**
     * 审核商品
     * @param id 商品ID
     * @param status 审核状态（待审核/通过/驳回）
     * @return 更新条数
     */
    public int auditProduct(Long id, String status) {
        ZgProduct product = new ZgProduct();
        product.setId(id);
        product.setStatus(status);
        product.setUpdateTime(new Date());
        return productMapper.updateByPrimaryKeySelective(product);
    }
//    public List<ZgProduct> productList(PageQuery<ZgProduct> pageQuery){
//        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
//        ZgProduct queryData = pageQuery.getQueryData();
//        List<ZgProduct> list = productMapper.
//        return new PageResult<>(list);
//
//    }
    /**
     * 查询商品详情
     */
    public ZgProduct getProductById(Long id) {
        return productMapper.selectByPrimaryKey(id);
    }
}

