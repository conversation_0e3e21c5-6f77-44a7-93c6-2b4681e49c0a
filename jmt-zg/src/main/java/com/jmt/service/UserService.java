package com.jmt.service;

import com.jmt.base.BaseService;
import com.jmt.dao.ZgUserMapper;
import com.jmt.model.zg.DriverAndAuditUserDto;
import com.jmt.model.zg.SupplierAndAuditUserDto;
import com.jmt.model.zg.UserDetail;
import com.jmt.model.zg.ZgUser;
import com.jmt.util.CodeGenerator;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class UserService extends BaseService {

    // 角色类型常量
    private static final int ROLE_TYPE_NORMAL   = 0; // 普通用户
    private static final int ROLE_TYPE_SUPPLIER = 1; // 供应商
    private static final int ROLE_TYPE_DRIVER   = 2; // 司机

    @Resource
    private ZgUserMapper zgUserMapper;
    @Resource
    private ZgDriverInfoService driverService;
//    @Resource
//    private SupplierService supplierService;

    /**
     * 获取用户详细信息（包含关联角色信息）
     */
    public UserDetail getUserDetailInfo(Long uaaId) {
        if (uaaId == null) {
            throw new IllegalArgumentException("用户UAA ID不能为空");
        }

        ZgUser zgUser = zgUserMapper.selectByuaaId(uaaId);
        if (zgUser == null) {
            throw new IllegalStateException("用户信息不存在，uaaId: " + uaaId);
        }

        UserDetail userDetail = new UserDetail();
        userDetail.setZgUser(zgUser);

        Integer roleType = zgUser.getRoleType();
        Long userId = zgUser.getId();

        if (roleType != null) {
            switch (roleType) {
//                case ROLE_TYPE_SUPPLIER:
//                    userDetail.setRole(supplierService.getSupplierInfo(userId));
//                    break;
//                case ROLE_TYPE_DRIVER:
//                    userDetail.setRole(driverService.getDriverInfo(userId));
//                    break;
                case ROLE_TYPE_NORMAL:
                default:
                    // 普通用户无需额外角色信息
                    break;
            }
        }
        return userDetail;
    }

    /**
     * 申请成为供应商
     * 生成供应商编号并调用供应商服务完成申请
     */
    public String beSupplier(SupplierAndAuditUserDto supplierAndAuditUserDto) {
        if (supplierAndAuditUserDto == null) {
            throw new IllegalArgumentException("供应商申请信息不能为空");
        }

        String supplierNo = CodeGenerator.generateSupplierNo();
        supplierAndAuditUserDto.setSupplierno(supplierNo);
//        supplierService.beSupplierInfo(supplierAndAuditUserDto);

        return supplierNo;
    }

    /**
     * 申请成为司机
     * 生成司机编号并调用司机服务完成申请
     */
    public String beDriver(DriverAndAuditUserDto driverAndAuditUserDto) {
        if (driverAndAuditUserDto == null) {
            throw new IllegalArgumentException("司机申请信息不能为空");
        }

        String driverNo = CodeGenerator.generateDriverNo();
        driverAndAuditUserDto.setDriverno(driverNo);
        //driverService.beDriver(driverAndAuditUserDto);

        return driverNo;
    }
}
