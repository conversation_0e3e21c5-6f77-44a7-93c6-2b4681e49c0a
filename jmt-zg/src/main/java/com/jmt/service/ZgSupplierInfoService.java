package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.client.CmBusinessInfoFeignClient;
import com.jmt.client.UaaFeignClient;
import com.jmt.dao.ZgSupplierInfoAuditDao;
import com.jmt.dao.ZgSupplierInfoDao;
import com.jmt.model.cm.entity.CmUser;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.uaa.UaaUserBankcard;
import com.jmt.model.zg.dto.ZgSupplierInfoDTO;
import com.jmt.model.zg.entity.ZgSupplierInfo;
import com.jmt.model.zg.entity.ZgSupplierInfoAudit;
import com.jmt.model.zg.vo.ZgBaseSupplierInfoVO;
import com.jmt.model.zg.vo.ZgSupplierInfoVO;
import com.jmt.util.ResponseUtil;
import com.jmt.util.SysCodeGenerateUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class ZgSupplierInfoService extends BaseService {

    @Resource
    private ZgSupplierInfoDao zgSupplierInfoDao;

    @Resource
    private ZgSupplierInfoAuditDao zgSupplierInfoAuditDao;

    @Resource
    private UaaFeignClient uaaFeignClient;



    @Resource
    private CmBusinessInfoFeignClient cmBusinessInfoFeignClient;

    public PageResult<ZgSupplierInfoVO> getSupplierInfoPage(PageQuery<ZgSupplierInfo> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());

        // 处理空查询条件
        ZgSupplierInfo queryCondition = pageQuery.getQueryData() != null ?
                pageQuery.getQueryData() : new ZgSupplierInfo();

        List<ZgSupplierInfoVO> list = zgSupplierInfoDao.selectByCondition(queryCondition);

        return new PageResult<>(list);
    }
    @Transactional
    public void addApp(ZgSupplierInfoDTO dto,Long uaaId) {

        int count=zgSupplierInfoDao.countByUaaId(uaaId);
        if(count>0){
            throw new RuntimeException("当前已经是供应商");
        }

        if(dto.getDeliveryModel() == 1 && dto.getMinDeliveryAmount() == null){
            throw new RuntimeException("请输入起送金额");
        }
        Date now = new Date();
        ZgSupplierInfo zgSupplierInfo = new ZgSupplierInfo();
        BeanUtils.copyProperties(dto, zgSupplierInfo);

        String defaultBankcard = uaaFeignClient.getDefaultBankcard(uaaId);
        UaaUserBankcard uaaUserBankcard = ResponseUtil.getData(defaultBankcard, UaaUserBankcard.class);
        if(uaaUserBankcard.getBankName() == null || uaaUserBankcard.getCardNo() == null){
            throw new RuntimeException("未设置默认银行卡");
        }
        zgSupplierInfo.setBankCardNo(uaaUserBankcard.getCardNo());
        zgSupplierInfo.setBankName(uaaUserBankcard.getBankName());
        zgSupplierInfo.setUuaId(uaaId);
        String supplierNo= SysCodeGenerateUtil.generateUserCode("JMT-D1");
        zgSupplierInfo.setSupplierNo(supplierNo);
        zgSupplierInfo.setCreateTime(now);
        zgSupplierInfo.setUpdateTime(now);
        zgSupplierInfo.setAuditStatus(0);
        zgSupplierInfo.setIsDelete(0);
        zgSupplierInfoDao.insert(zgSupplierInfo);

        // 创建审核记录
        ZgSupplierInfoAudit zgSupplierInfoAudit = new ZgSupplierInfoAudit();
        zgSupplierInfoAudit.setSupplierNo(supplierNo);
        zgSupplierInfoAudit.setCreateTime(now);
        zgSupplierInfoAudit.setUpdateTime(now);
        zgSupplierInfoAudit.setAuditStatus(0);
        zgSupplierInfoAudit.setIsDelete(0);
        zgSupplierInfoAuditDao.insert(zgSupplierInfoAudit);
    }

    @Transactional
    public void addPc(ZgSupplierInfoDTO dto) {
        if(dto.getDeliveryModel() == 1 && dto.getMinDeliveryAmount() == null){
            throw new RuntimeException("请输入起送金额");
        }
        Date now = new Date();
        ZgSupplierInfo zgSupplierInfo = new ZgSupplierInfo();
        BeanUtils.copyProperties(dto, zgSupplierInfo);

        String creditCode=dto.getCreditCode();
        String creditStr = cmBusinessInfoFeignClient.getByCreditCode(creditCode);
        CmUser cmUser = ResponseUtil.getData(creditStr, CmUser.class);
        if(cmUser.getUaaId() != null){
            zgSupplierInfo.setUuaId(cmUser.getUaaId());
        }else{
            throw new RuntimeException("当前商家门店不存在，无法升级供应商");
        }

        String supplierNo= SysCodeGenerateUtil.generateUserCode("JMT-D1");
        zgSupplierInfo.setSupplierNo(supplierNo);
        zgSupplierInfo.setCreateTime(now);
        zgSupplierInfo.setUpdateTime(now);
        zgSupplierInfo.setAuditStatus(0);
        zgSupplierInfo.setIsDelete(0);
        zgSupplierInfoDao.insert(zgSupplierInfo);

        // 创建审核记录
        ZgSupplierInfoAudit zgSupplierInfoAudit = new ZgSupplierInfoAudit();
        zgSupplierInfoAudit.setSupplierNo(supplierNo);
        zgSupplierInfoAudit.setCreateTime(now);
        zgSupplierInfoAudit.setUpdateTime(now);
        zgSupplierInfoAudit.setAuditStatus(0);
        zgSupplierInfoAudit.setIsDelete(0);
        zgSupplierInfoAuditDao.insert(zgSupplierInfoAudit);
    }

    @Transactional
    public int update(ZgSupplierInfo dto) {
        ZgSupplierInfo zgSupplierInfo = zgSupplierInfoDao.selectById(dto.getId());
        if (zgSupplierInfo == null) {
            throw new RuntimeException("供应商不存在");
        }
        if (zgSupplierInfo.getAuditStatus() != 0) {
            throw new RuntimeException("只有待审核状态的信息可以修改");
        }
        return zgSupplierInfoDao.updateById(dto);
    }

    public ZgSupplierInfo getSupplierInfoById(Long id) {
        return zgSupplierInfoDao.selectById(id);
    }


    public ZgSupplierInfo getSupplierInfoByUaaId(Long uaaId) {
        return zgSupplierInfoDao.selectByUaaId(uaaId);
    }

    public ZgSupplierInfo getSupplierInfoBySupplierNo(String supplierNo) {
        return zgSupplierInfoDao.selectBySupplierNo(supplierNo);
    }


    @Transactional
    public int delete(Long id) {
        ZgSupplierInfo zgSupplierInfo = zgSupplierInfoDao.selectById(id);
        if (zgSupplierInfo == null) {
            throw new RuntimeException("配送员不存在");
        }
        ZgSupplierInfoAudit zgSupplierInfoAudit=zgSupplierInfoAuditDao.selectBySupplierNo(zgSupplierInfo.getSupplierNo());
        int count=zgSupplierInfoDao.delete(id);
        zgSupplierInfoAuditDao.delete(zgSupplierInfoAudit.getId());
        return count;
    }

    @Transactional
    public int auditSupplierInfo(Long id, Integer auditStatus, String reason, Long auditUserId) {
        ZgSupplierInfo zgSupplierInfo = zgSupplierInfoDao.selectById(id);
        if (zgSupplierInfo == null) {
            throw new RuntimeException("供应商不存在");
        }

        if (auditStatus == 2 && StringUtils.isBlank(reason)) {
            throw new RuntimeException("驳回时必须填写原因");
        }

        if (zgSupplierInfo.getAuditStatus() != 0) {
            throw new RuntimeException("只能审核待审核状态的信息");
        }

        // 更新供应商审核状态
        zgSupplierInfo.setAuditStatus(auditStatus);
        zgSupplierInfo.setReason(reason);
        zgSupplierInfo.setUpdateTime(new Date());
        int updateCount = zgSupplierInfoDao.updateById(zgSupplierInfo);

        // 更新审核表
        ZgSupplierInfoAudit zgSupplierInfoAudit = zgSupplierInfoAuditDao.selectById(id);
        zgSupplierInfoAudit.setAuditStatus(auditStatus);
        zgSupplierInfoAudit.setReason(reason);
        zgSupplierInfoAudit.setUpdateTime(new Date());
        zgSupplierInfoAudit.setAuditUser(auditUserId);
        zgSupplierInfoAuditDao.updateById(zgSupplierInfoAudit);

        return updateCount;
    }

    public ZgBaseSupplierInfoVO getBaseSupplierInfoByUaaId(Long uaaId) {
        return zgSupplierInfoDao.getBaseSupplierInfoByUaaId(uaaId);
    }
}
