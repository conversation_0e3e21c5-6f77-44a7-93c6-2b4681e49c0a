package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.dao.ZgProductCouponDao;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.zg.entity.ZgProductCoupon;
import com.jmt.model.zg.vo.ZgProductCouponVO;
import com.jmt.util.SysCodeGenerateUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class ZgProductCouponService extends BaseService {

    @Resource
    private ZgProductCouponDao zgProductCouponDao;

    public PageResult<ZgProductCouponVO> getPage(PageQuery<ZgProductCoupon> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());

        // 处理空查询条件
        ZgProductCoupon queryCondition = pageQuery.getQueryData() != null ?
                pageQuery.getQueryData() : new ZgProductCoupon();

        List<ZgProductCouponVO> list = zgProductCouponDao.selectByCondition(queryCondition);

        return new PageResult<>(list);
    }


    @Transactional
    public void add(ZgProductCoupon dto) {

        Date now = new Date();
        ZgProductCoupon zgProductCoupon = new ZgProductCoupon();
        BeanUtils.copyProperties(dto, zgProductCoupon);


        String couponNo= SysCodeGenerateUtil.generateUserCode("JMT-P2");
        zgProductCoupon.setCouponNo(couponNo);
        zgProductCoupon.setCreateTime(now);
        zgProductCoupon.setUpdateTime(now);
        zgProductCoupon.setIsDelete(0);
        zgProductCouponDao.insert(zgProductCoupon);


    }

    @Transactional
    public int update(ZgProductCoupon dto) {
        ZgProductCoupon zgProductCoupon = zgProductCouponDao.selectById(dto.getId());
        if (zgProductCoupon == null) {
            throw new RuntimeException("优惠券不存在");
        }

        return zgProductCouponDao.updateById(dto);
    }

    public ZgProductCoupon getById(Long id) {
        return zgProductCouponDao.selectById(id);
    }


    public ZgProductCoupon getByCouponNo(String couponNo) {
        return zgProductCouponDao.selectByCouponNo(couponNo);
    }


    @Transactional
    public int delete(Long id) {
        ZgProductCoupon zgProductCoupon = zgProductCouponDao.selectById(id);
        if (zgProductCoupon == null) {
            throw new RuntimeException("优惠券不存在");
        }
        return zgProductCouponDao.delete(id);
    }

}
