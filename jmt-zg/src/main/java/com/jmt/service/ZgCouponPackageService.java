package com.jmt.service;

import com.jmt.base.BaseService;
import com.jmt.dao.ZgCouponPackageDao;
import com.jmt.model.zg.entity.ZgCouponPackage;
import com.jmt.model.zg.vo.ZgCouponVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class ZgCouponPackageService extends BaseService {

    @Resource
    private ZgCouponPackageDao zgCouponPackageDao;


public List<ZgCouponVO> getHomePageCoupons(Long userId) {
    try {
        List<ZgCouponVO> coupons;

        if (userId == null) {
            // 未登录用户：获取所有今日可领取的优惠券
            coupons = zgCouponPackageDao.selectAllAvailableCoupons();
        } else {
            // 已登录用户：获取个性化推荐（未领取的排在前面）
            coupons = zgCouponPackageDao.selectHomePageCoupons(userId);

            // 处理显示文本
            coupons.forEach(coupon -> {
                if (coupon.getIsValid() != null && !coupon.getIsValid()) {
                    coupon.setReceiveStatusText("已过期");
                }

                // 设置剩余天数描述
                if (coupon.getRemainingDays() != null) {
                    if (coupon.getRemainingDays() <= 0) {
                        coupon.setReceiveStatusText("即将过期");
                    } else if (coupon.getRemainingDays() <= 3) {
                        coupon.setReceiveStatusText("剩" + coupon.getRemainingDays() + "天");
                    }
                }
            });
        }

        return coupons;
    } catch (Exception e) {
        throw new RuntimeException("获取优惠券列表失败");
    }
}

    @Transactional
    public void add(ZgCouponPackage dto,Long uaaId) {

        Date now = new Date();
        dto.setBuyUserId(uaaId);
        dto.setCreateTime(now);
        dto.setUpdateTime(now);
        dto.setIsDelete(0);
        dto.setTStatus(0);
        zgCouponPackageDao.insert(dto);


    }

    /**
     * 使用优惠券
     * @param id
     * @return
     */
    @Transactional
    public int update(Long id) {
        ZgCouponPackage zgCouponPackage = zgCouponPackageDao.selectById(id);
        if (zgCouponPackage == null) {
            throw new RuntimeException("优惠券不存在");
        }
        zgCouponPackage.setTStatus(1);
        return zgCouponPackageDao.updateById(zgCouponPackage);
    }

    public ZgCouponPackage getById(Long id) {
        return zgCouponPackageDao.selectById(id);
    }


    public List<ZgCouponPackage> getByUserId(Long buyUserId) {
        return zgCouponPackageDao.selectByUserId(buyUserId);
    }


    @Transactional
    public int delete(Long id) {
        ZgCouponPackage zgCouponPackage = zgCouponPackageDao.selectById(id);
        if (zgCouponPackage == null) {
            throw new RuntimeException("优惠券不存在");
        }
        return zgCouponPackageDao.delete(id);
    }
}
