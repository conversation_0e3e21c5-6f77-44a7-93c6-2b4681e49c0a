package com.jmt.controller;

import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.zg.SettleTicketOrderRequest;
import com.jmt.model.zg.TicketOrderResponse;
import com.jmt.service.TicketService;
import com.jmt.util.LoginUserUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 购物车管理控制器
 * 提供囤货券订单结算等功能
 */
@RestController
@RequestMapping("/zg/shoppingCart/v1")
public class ShoppingCartController {

    @Resource
    private TicketService ticketService;

    /**
     * 结算囤货券订单
     * @param orderRequest 囤货券订单结算请求，包含商品信息、优惠券信息等
     * @param request HTTP请求对象，用于获取登录用户信息
     * @return 囤货券订单结算结果，包含订单号、金额、优惠信息等
     */
    @PostMapping("/ticketOrder")
    public TicketOrderResponse settleTicketOrder(@RequestBody SettleTicketOrderRequest orderRequest, HttpServletRequest request) {
        // 从请求中获取登录用户信息
        LoginUaaUser loginUser = LoginUserUtil.get(request);

        // 设置购买用户ID
        orderRequest.setBuyUserId(loginUser.getUaaId());

        // 调用服务层处理订单结算
        return ticketService.settleTicketOrder(orderRequest);
    }
}