package com.jmt.controller;

import cn.hutool.core.util.ObjectUtil;
import com.jmt.base.BaseController;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.zg.ZgProductCouponDto;
import com.jmt.model.zg.ZgProductCouponVo;
import com.jmt.service.ProductCouponService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 商品抵扣券控制器
 */
@RestController
@RequestMapping("/zg/product/Coupon/v1")
public class ProductCouponController extends BaseController {

    @Resource
    private ProductCouponService productCouponService;

    /**
     * 分页查询
     * @param pageQuery 列表查询条件
     * @return String
     */

    @ResponseBody
    @GetMapping(value = "/page")
    public String getPage(@RequestBody PageQuery<ZgProductCouponDto> pageQuery) {
        PageResult<ZgProductCouponVo> page = productCouponService.getPage(pageQuery);
        return super.responseSuccess(page,"查询成功");
    }

    /**
     * 查看
     * @param id 主键ID
     * @return String
     */
    @GetMapping(value = "/info/{id}")
    public String getInfo(@PathVariable Long id) {
        ZgProductCouponVo vo = productCouponService.getInfo(id);
        if (ObjectUtil.isEmpty(vo)) {
            return super.responseFail("查询失败");
        }
        return super.responseSuccess(vo,"查询成功");
    }
    /**
     * 新增
     * @param dto 新增参数
     * @return ResBody
     */
    @ResponseBody
    @PostMapping(value = "/add")
    public String add(@RequestBody ZgProductCouponDto dto) {
        Integer r = productCouponService.add(dto);
        if (r==1) {
            return super.responseSuccess("新增成功");
        } else {
            return super.responseFail("新增失败");
        }

    }

    /**
     * 编辑
     * @param id 编辑参数
     * @return ResBody
     */
    @ResponseBody
    @PostMapping(value = "/delete/{id}")
    public String edit(@PathVariable Long id) {
        Integer r = productCouponService.delete(id);
        if (r==1) {
            return super.responseSuccess("删除成功");
        } else {
            return super.responseFail("删除失败");
        }
    }

    /**
     * 编辑
     * @param dto 编辑参数
     * @return ResBody
     */
    @ResponseBody
    @PostMapping(value = "/edit")
    public String edit(@RequestBody ZgProductCouponDto dto) {
        Integer r = productCouponService.edit(dto);
        if (r==1) {
            return super.responseSuccess("编辑成功");
        } else {
            return super.responseFail("编辑失败");
        }
    }
}
