package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.zg.dto.ZgProductInfoAppDTO;
import com.jmt.model.zg.dto.ZgProductInfoDTO;
import com.jmt.model.zg.entity.ZgProductInfo;
import com.jmt.model.zg.vo.ZgMallProductListVO;
import com.jmt.model.zg.vo.ZgMyProductListVO;
import com.jmt.model.zg.vo.ZgProductInfoVO;
import com.jmt.service.ZgProductInfoService;
import com.jmt.util.LoginUserUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/zg/productInfo/v1")
public class ZgProductInfoController extends BaseController {

    @Resource
    private ZgProductInfoService zgProductInfoService;

    @PostMapping("/page")
    public String getPage(@RequestBody(required = false) PageQuery<ZgProductInfo> pageQuery) {
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
            // 设置默认页码和页大小，根据你的业务需求调整
            pageQuery.setPageNo(1);
            pageQuery.setPageSize(10);
        }
        PageResult<ZgProductInfoVO> result = zgProductInfoService.getPage(pageQuery);

        return super.responseSuccess(result, "查询成功");
    }

    @PostMapping("/getMyPage")
    public String getMyPage(@RequestBody(required = false) PageQuery<ZgProductInfo> pageQuery,HttpServletRequest req) {
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
            // 设置默认页码和页大小，根据你的业务需求调整
            pageQuery.setPageNo(1);
            pageQuery.setPageSize(10);
        }
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        PageResult<ZgMyProductListVO> result = zgProductInfoService.getMyPage(pageQuery,loginUser.getUaaId());

        return super.responseSuccess(result, "查询成功");
    }

    @PostMapping("/getMallPage")
    public String getMallPage(@RequestBody(required = false) PageQuery<ZgProductInfo> pageQuery) {
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
            // 设置默认页码和页大小，根据你的业务需求调整
            pageQuery.setPageNo(1);
            pageQuery.setPageSize(10);
        }
        PageResult<ZgMallProductListVO> result = zgProductInfoService.getMallPage(pageQuery);

        return super.responseSuccess(result, "查询成功");
    }


    @PostMapping(value = "/add")
    public String add(@RequestBody ZgProductInfoDTO dto) {
        try {
            zgProductInfoService.add(dto);
            return super.responseSuccess("新增成功");
        } catch (Exception e) {
            return super.responseFail("系统错误: " + e.getMessage());
        }
    }

    @PostMapping(value = "/addSj")
    public String addSj(@RequestBody ZgProductInfoAppDTO dto,HttpServletRequest req) {
        try {
            LoginUaaUser loginUser = LoginUserUtil.get(req);
            zgProductInfoService.addSj(dto,loginUser);
            return super.responseSuccess("新增成功");
        } catch (Exception e) {
            return super.responseFail("系统错误: " + e.getMessage());
        }
    }

    /**
     * 修改
     * @param dto
     * @return
     */
    @PostMapping("/update")
    @ResponseBody
    public String update(@RequestBody ZgProductInfo dto) {
        try {
            zgProductInfoService.update(dto);
            return super.responseSuccess("修改成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    @PostMapping("/delete/{id}")
    public String delete(@PathVariable Long id) {
        try {
            zgProductInfoService.delete(id);
            return super.responseSuccess("删除成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }



    @GetMapping("/id/{id}")
    public String getById(@PathVariable("id") Long id) {
        ZgProductInfo zgProductInfo = zgProductInfoService.getById(id);
        return super.responseSuccess(zgProductInfo, "查询成功");
    }


    @GetMapping("/productNo/{productNo}")
    public String getSupplierInfoByProductNo(@PathVariable("productNo") String productNo) {
        ZgProductInfo zgProductInfo = zgProductInfoService.getByProductNo(productNo);
        return super.responseSuccess(zgProductInfo, "查询成功");
    }

    @PostMapping("/audit")
    public String auditSupplier(@RequestParam Long id,
                                @RequestParam Integer auditStatus, @RequestParam(required = false) String reason) {
        try {
            zgProductInfoService.auditProduct(id, auditStatus,reason);
            return super.responseSuccess("审核成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }
}
