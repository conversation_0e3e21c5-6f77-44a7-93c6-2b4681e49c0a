package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.zg.dto.CreateOrderDTO;
import com.jmt.model.zg.entity.ZgProductCart;
import com.jmt.model.zg.vo.ZgProductCartVO;
import com.jmt.service.ZgProductCartService;
import com.jmt.util.LoginUserUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/zg/productCart/v1")
public class ZgProductCartController extends BaseController {
    @Resource
    private ZgProductCartService productCartService;

    @PostMapping("/page")
    public String getPage(@RequestBody(required = false) PageQuery<CreateOrderDTO> pageQuery, HttpServletRequest req) {
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
            pageQuery.setPageNo(1);
            pageQuery.setPageSize(10);
        }
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        PageResult<ZgProductCartVO> result = productCartService.getPage(pageQuery,loginUser.getUaaId());

        return super.responseSuccess(result, "查询成功");
    }


    @PostMapping(value = "/add")
    public String add(@RequestBody ZgProductCart dto, HttpServletRequest req) {
        try {
            LoginUaaUser loginUser = LoginUserUtil.get(req);
            dto.setBuyUserId(loginUser.getUaaId());
            Long id = productCartService.addOrUpdate(dto);
            return super.responseSuccess(id,"新增成功");
        } catch (Exception e) {
            return super.responseFail("系统错误: " + e.getMessage());
        }
    }




    @PostMapping("/delete/{id}")
    public String delete(@PathVariable Long id) {
        try {
            productCartService.delete(id);
            return super.responseSuccess("删除成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    @PostMapping("/cleanCart")
    public String cleanCart(HttpServletRequest req) {
        try {
            LoginUaaUser loginUser = LoginUserUtil.get(req);
            productCartService.cleanCart(loginUser.getUaaId());
            return super.responseSuccess("删除成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    @PostMapping("/increaseOne/{id}")
    public String increaseOne(@PathVariable Long id) {
        try {
            productCartService.increaseOne(id);
            return super.responseSuccess("新增成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    @PostMapping("/decreaseOne/{id}")
    public String decreaseOne(@PathVariable Long id) {
        try {
            productCartService.decreaseOne(id);
            return super.responseSuccess("减少成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }


}
