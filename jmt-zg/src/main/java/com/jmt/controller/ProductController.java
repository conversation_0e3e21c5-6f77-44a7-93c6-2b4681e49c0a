package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.zg.ZgProduct;
import com.jmt.service.ProductService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/zg/product/v1")
public class ProductController extends BaseController {

    @Resource
    private ProductService productService;

    /**
     * 新增商品
     */
    @PostMapping("/add")
    public Object addProduct(@RequestBody ZgProduct product) {
        int rows = productService.addProduct(product);
        return rows > 0 ? super.responseSuccess("新增成功") : super.responseFail("新增失败");
    }

    /**
     * 编辑商品
     */
    @PostMapping("/edit")
    public Object editProduct(@RequestBody ZgProduct product) {
        int rows = productService.editProduct(product);
        return rows > 0 ? super.responseSuccess("编辑成功") : super.responseFail("编辑失败");

    }

    /**
     * 审核商品
     */
    @PostMapping("/audit")
    public Object auditProduct(@RequestParam Long id,
                               @RequestParam String status) {
        int rows = productService.auditProduct(id, status);
        return rows > 0 ? super.responseSuccess("审核成功") : super.responseFail("审核失败");

    }

    /**
     * 查询商品详情
     */
    @GetMapping("/detail/{id}")
    public Object getProduct(@PathVariable Long id) {
        ZgProduct product = productService.getProductById(id);
        return product!=null ? super.responseSuccess(product,"查询成功") : super.responseFail("查询失败");

    }
//    @PostMapping("/page")
//    public Object auditProduct(@RequestBody(required = false) PageQuery<ZgProduct> pageQuery) {
//        productService.productList();
//        return rows > 0 ? super.responseSuccess("审核成功") : super.responseFail("审核失败");
//
//    }
}
