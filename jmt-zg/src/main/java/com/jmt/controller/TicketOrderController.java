package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.zg.TicketOrderDetail;
import com.jmt.service.TicketOrderService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 囤货券订单管理控制器
 * 提供囤货券订单的取消、详情查询、退款等操作
 */
@RestController
@RequestMapping("/zg/ticketOrder/v1")
public class TicketOrderController extends BaseController {

    @Resource
    private TicketOrderService ticketOrderService;

    /**
     * 取消囤货券订单
     * @param orderId 订单ID（必填）
     * @return 操作结果：true-取消成功，false-取消失败（如订单状态不允许取消）
     * @throws Exception 处理过程中可能抛出的异常（如订单不存在、数据库操作异常等）
     */
    @PostMapping("/cancel")
    public String cancelTicketOrder(@RequestParam Long orderId) throws Exception {
        boolean result = ticketOrderService.cancelTicketOrder(orderId);
        return super.responseSuccess(result, "订单取消操作结果");
    }

    /**
     * 查看囤货券订单详情
     * @param orderId 订单ID（必填）
     * @return 订单详情信息，包含订单基本信息、商品信息、价格信息等
     */
    @GetMapping("/detail")
    public String getTicketOrderDetail(@RequestParam Long orderId) {
        TicketOrderDetail detail = ticketOrderService.getTicketOrderDetail(orderId);
        return super.responseSuccess(detail, "订单详情查询成功");
    }

    /**
     * 囤货券订单退款
     * @param orderId 订单ID（必填）
     * @return 操作结果：true-退款成功，false-退款失败（如订单状态不允许退款、退款流程异常等）
     * @throws Exception 处理过程中可能抛出的异常（如订单不存在、支付系统交互异常等）
     */
    @PostMapping("/refund")
    public String refundTicketOrder(
            @RequestParam Long orderId
    ) throws Exception {
        boolean result = ticketOrderService.refundTicketOrder(orderId);
        return super.responseSuccess(result, "订单退款操作结果");
    }

}