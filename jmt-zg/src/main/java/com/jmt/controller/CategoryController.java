package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.zg.ZgProductCategoryVo;
import com.jmt.model.zg.entity.ZgProductCategory;
import com.jmt.service.ProductCategoryService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 商品品类控制器
 */
@RestController
@RequestMapping("/zg/product/category/v1")
public class CategoryController extends BaseController {

    @Resource
    private ProductCategoryService categoryService;

    /**
     * 商品品类下拉框数据
     */
    @GetMapping("/select")
    public String selectCategory() {
        List<ZgProductCategoryVo> datas = categoryService.selectCategory();
        return super.responseSuccess(datas, "下拉框数据查询成功");

    }
    @PostMapping("/updatePicture")
    public String updatePicture(@RequestBody ZgProductCategoryVo vo){
        categoryService.updatePicture(vo);
        return super.responseSuccess( "修改成功");
    }
    @GetMapping("/getCategoryTree")
    public String getCategoryTreeDirect() {
        List<ZgProductCategoryVo> datas = categoryService.getCategoryTreeDirect();
        return super.responseSuccess(datas, "下拉框数据查询成功");

    }

    @PostMapping("/insert")
    public String updatePicture(@RequestBody ZgProductCategory zgProductCategory){
        categoryService.insert(zgProductCategory);
        return super.responseSuccess( "新增成功");
    }

}
