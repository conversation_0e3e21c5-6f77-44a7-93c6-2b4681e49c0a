package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.zg.DriverAndAuditUserDto;
import com.jmt.model.zg.SupplierAndAuditUserDto;
import com.jmt.model.zg.UserDetail;
import com.jmt.service.UserService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 用户中心控制器
 * 负责用户信息查询、申请成为供应商、申请成为司机等用户相关业务操作
 */
@RestController
@RequestMapping("/zg/user/v1")
public class UserController extends BaseController {

    @Resource
    private UserService userService;

    /**
     * 获取用户详细信息
     *
     * @param uaaId 用户统一账号唯一标识（uaa系统用户ID）
     * @return 包含用户基本信息、角色权限、关联业务数据（如已申请的供应商/司机信息）的详细信息
     */
    @GetMapping("/getUserInfo")
    public String getUserInfo(@RequestParam Long uaaId) {
        // 调用服务层获取用户详情
        UserDetail userDetail = userService.getUserDetailInfo(uaaId);
        return super.responseSuccess(userDetail, "获取用户信息成功");
    }

    /**
     * 用户申请成为供应商
     *
     * @param supplierAndAuditUserDto 供应商申请信息DTO
     *                               包含：用户基础信息（uaaId）、供应商资质信息（营业执照、经营范围等）、联系人信息
     * @return 申请提交结果（申请成功后进入审核流程）
     */
    @PostMapping("/beZgSupplier")
    public String beZgSupplier(@RequestBody SupplierAndAuditUserDto supplierAndAuditUserDto) {

        // 调用服务层处理供应商申请
        String supplierNo = userService.beSupplier(supplierAndAuditUserDto);
        return super.responseSuccess(supplierNo,"申请成为供应商已提交，等待审核");
    }

    /**
     * 用户申请成为司机
     *
     * @param driverAndAuditUserDto 司机申请信息DTO
     *                             包含：用户基础信息（uaaId）、司机资质信息（驾驶证、车辆信息等）、联系方式
     * @return 申请提交结果（申请成功后进入审核流程）
     */
    @PostMapping("/beDriver")
    public String beDriver(@RequestBody DriverAndAuditUserDto driverAndAuditUserDto) {


        // 调用服务层处理司机申请
        String driverNo = userService.beDriver(driverAndAuditUserDto);
        return super.responseSuccess(driverNo,"申请成为司机已提交，等待审核");
    }
}