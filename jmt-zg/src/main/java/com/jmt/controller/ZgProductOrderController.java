package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.zg.dto.CreateOrderDTO;
import com.jmt.model.zg.dto.ZgProductOrderDTO;
import com.jmt.model.zg.entity.ZgProductOrder;
import com.jmt.model.zg.vo.*;
import com.jmt.service.ZgProductOrderService;
import com.jmt.util.LoginUserUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;


@RestController
@RequestMapping("/zg/productOrder/v1")
public class ZgProductOrderController extends BaseController {

    @Resource
    private ZgProductOrderService zgProductOrderService;


    @PostMapping("/getPcPage")
    public String getPcPage(@RequestBody(required = false) PageQuery<ZgProductOrderDTO> pageQuery) {
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
            // 设置默认页码和页大小，根据你的业务需求调整
            pageQuery.setPageNo(1);
            pageQuery.setPageSize(10);
        }
        PageResult<ZgProductOrderIndexVO> result = zgProductOrderService.getPage(pageQuery);

        return super.responseSuccess(result, "查询成功");
    }

    @PostMapping(value = "/create")
    public String create(@RequestBody CreateOrderDTO createOrderDTO, HttpServletRequest req) {
        try {
            LoginUaaUser loginUser = LoginUserUtil.get(req);
            ZgProductOrder order = zgProductOrderService.createOrder(loginUser.getUaaId(), createOrderDTO);
            return super.responseSuccess(order,"创建成功");
        } catch (Exception e) {
            return super.responseFail("系统错误: " + e.getMessage());
        }
    }

    @PostMapping(value = "/payOrder")
    public String payOrder(@RequestBody ProductPayVO productPayVO, HttpServletRequest req) {
        try {
            LoginUaaUser loginUser = LoginUserUtil.get(req);
            Boolean isSuccess = zgProductOrderService.processPayment(loginUser.getUaaId(), productPayVO);
            if(isSuccess){
                return super.responseSuccess("支付成功");
            }else{
                return super.responseFail("支付失败");
            }
        } catch (Exception e) {
            return super.responseFail("系统错误: " + e.getMessage());
        }
    }


    /**
     *
     * @param orderNo
     * @return
     */
    @GetMapping(value = "/payment/status/{orderNo}")
    public String getPaymentStatus(@PathVariable String orderNo) {
        try {
            ZgProductOrder order = zgProductOrderService.getOrderByNo(orderNo);
            return super.responseSuccess(order.getOrderStatus(),"获取订单状态成功");
        } catch (Exception e) {
            return super.responseFail("系统错误: " + e.getMessage());
        }
    }



    @PostMapping("/assignDriver")
    public String assignDriver(@RequestParam String orderNo,@RequestParam  String driverNo) {
        try {
            boolean success = zgProductOrderService.assignDriverToOrder(orderNo,driverNo);

            if(success){
                return super.responseSuccess("分配成功");
            }
            return super.responseFail("分配失败");
        } catch (Exception e) {
            return super.responseFail("系统错误: " + e.getMessage());
        }
    }


    @PostMapping("/getOrderDetail")
    public String getOrderDetail(@RequestParam String orderNo) {
        try {
            ZgProductOrderDetailVO zgProductOrderDetailVO = zgProductOrderService.getOrderDetail(orderNo);
            return super.responseSuccess(zgProductOrderDetailVO,"查询成功");

        } catch (Exception e) {
            return super.responseFail("系统错误: " + e.getMessage());
        }
    }

    @PostMapping("/app/listPage")
    public String getOrdersByUserPage(@RequestBody PageQuery<ZgProductOrder> pageQuery, HttpServletRequest req) {
        try {
            LoginUaaUser loginUser = LoginUserUtil.get(req);

            // 获取查询条件
            Integer orderStatus = null;
            if (pageQuery.getQueryData() != null) {
                orderStatus = pageQuery.getQueryData().getOrderStatus();
            }

            PageResult<ZgProductOrderAppVO> result = zgProductOrderService.getAppOrdersByUserId(
                    loginUser.getUaaId(),
                    orderStatus,
                    pageQuery.getPageNo(),
                    pageQuery.getPageSize()
            );
            return super.responseSuccess(result, "查询成功");
        } catch (Exception e) {
            return super.responseFail("查询失败: " + e.getMessage());
        }
    }

}
