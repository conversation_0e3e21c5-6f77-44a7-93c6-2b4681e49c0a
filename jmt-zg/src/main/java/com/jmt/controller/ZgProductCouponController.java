package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.zg.entity.ZgProductCoupon;
import com.jmt.model.zg.vo.ZgProductCouponVO;
import com.jmt.service.ZgProductCouponService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/zg/productCoupon/v1")
public class ZgProductCouponController extends BaseController {

    @Resource
    private ZgProductCouponService zgProductCouponService;

    @PostMapping("/page")
    public String getPage(@RequestBody(required = false) PageQuery<ZgProductCoupon> pageQuery) {
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
            // 设置默认页码和页大小，根据你的业务需求调整
            pageQuery.setPageNo(1);
            pageQuery.setPageSize(10);
        }
        PageResult<ZgProductCouponVO> result = zgProductCouponService.getPage(pageQuery);

        return super.responseSuccess(result, "查询成功");
    }



    @PostMapping(value = "/add")
    public String add(@RequestBody ZgProductCoupon zgProductCoupon) {
        try {
            zgProductCouponService.add(zgProductCoupon);
            return super.responseSuccess("新增成功");
        } catch (Exception e) {
            return super.responseFail("系统错误: " + e.getMessage());
        }
    }

    /**
     * 修改
     * @param dto
     * @return
     */
    @PostMapping("/update")
    @ResponseBody
    public String update(@RequestBody ZgProductCoupon dto) {
        try {
            zgProductCouponService.update(dto);
            return super.responseSuccess("修改成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    @PostMapping("/delete/{id}")
    public String delete(@PathVariable Long id) {
        try {
            zgProductCouponService.delete(id);
            return super.responseSuccess("删除成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }



    @GetMapping("/id/{id}")
    public String getById(@PathVariable("id") Long id) {
        ZgProductCoupon zgProductCoupon = zgProductCouponService.getById(id);
        return super.responseSuccess(zgProductCoupon, "查询成功");
    }


    @GetMapping("/couponNo/{couponNo}")
    public String getSupplierInfoBySupplierNo(@PathVariable("couponNo") String couponNo) {
        ZgProductCoupon zgProductCoupon = zgProductCouponService.getByCouponNo(couponNo);
        return super.responseSuccess(zgProductCoupon, "查询成功");
    }
}
