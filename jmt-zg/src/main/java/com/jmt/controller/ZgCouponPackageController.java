package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.zg.entity.ZgCouponPackage;
import com.jmt.model.zg.vo.ZgCouponVO;
import com.jmt.service.ZgCouponPackageService;
import com.jmt.util.LoginUserUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("/zg/couponPackage/v1")
public class ZgCouponPackageController extends BaseController {

    @Resource
    private ZgCouponPackageService zgCouponPackageService;

    @GetMapping("/homePage")
    public String getHomePageCoupons(HttpServletRequest req) {
        // 获取登录用户（未登录时为null）
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        // 未登录用户的uaaId为null，作为参数传入
        Long uaaId = loginUser != null ? loginUser.getUaaId() : null;

        // 调用服务层时传入可能为null的uaaId，由服务层处理未登录逻辑
        List<ZgCouponVO> coupons = zgCouponPackageService.getHomePageCoupons(uaaId);

        return super.responseSuccess(coupons, "查询成功");
    }
//    @PostMapping("/page")
//    public String getPage(@RequestBody(required = false) PageQuery<ZgCouponPackage> pageQuery) {
//        if (pageQuery == null) {
//            pageQuery = new PageQuery<>();
//            // 设置默认页码和页大小，根据你的业务需求调整
//            pageQuery.setPageNo(1);
//            pageQuery.setPageSize(10);
//        }
//        PageResult<ZgCouponPackage> result = zgCouponPackageService.getPage(pageQuery);
//
//        return super.responseSuccess(result, "查询成功");
//    }



    @PostMapping(value = "/add")
    public String add(@RequestBody ZgCouponPackage zgCouponPackage, HttpServletRequest req) {
        try {
            LoginUaaUser loginUser = LoginUserUtil.get(req);
            zgCouponPackageService.add(zgCouponPackage,loginUser.getUaaId());
            return super.responseSuccess("新增成功");
        } catch (Exception e) {
            return super.responseFail("系统错误: " + e.getMessage());
        }
    }

    /**
     * 使用优惠券
     * @param id
     * @return
     */
    @PostMapping("/useCoupon")
    @ResponseBody
    public String update(@RequestParam Long id) {
        try {
            zgCouponPackageService.update(id);
            return super.responseSuccess("修改成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    @PostMapping("/delete/{id}")
    public String delete(@PathVariable Long id) {
        try {
            zgCouponPackageService.delete(id);
            return super.responseSuccess("删除成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }



    @GetMapping("/id/{id}")
    public String getById(@PathVariable("id") Long id) {
        ZgCouponPackage zgCouponPackage = zgCouponPackageService.getById(id);
        return super.responseSuccess(zgCouponPackage, "查询成功");
    }


    @GetMapping("/buyUserId")
    public String getSupplierInfoBySupplierNo(HttpServletRequest req) {
        try {
            LoginUaaUser loginUser = LoginUserUtil.get(req);
            List<ZgCouponPackage> list = zgCouponPackageService.getByUserId(loginUser.getUaaId());
            return super.responseSuccess(list, "查询成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }
}
