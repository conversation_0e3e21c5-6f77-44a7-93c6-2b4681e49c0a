package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.zg.dto.ZgSupplierInfoDTO;
import com.jmt.model.zg.entity.ZgSupplierInfo;
import com.jmt.model.zg.vo.ZgBaseSupplierInfoVO;
import com.jmt.model.zg.vo.ZgSupplierInfoVO;
import com.jmt.service.ZgSupplierInfoService;
import com.jmt.util.LoginUserUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;


@RestController
@RequestMapping("/zg/supplier/v1")
public class SupplierController extends BaseController {

    @Resource
    private ZgSupplierInfoService zgSupplierInfoService;

    @PostMapping("/page")
    public String getPage(@RequestBody(required = false) PageQuery<ZgSupplierInfo> pageQuery) {
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
            // 设置默认页码和页大小，根据你的业务需求调整
            pageQuery.setPageNo(1);
            pageQuery.setPageSize(10);
        }
        PageResult<ZgSupplierInfoVO> result = zgSupplierInfoService.getSupplierInfoPage(pageQuery);

        return super.responseSuccess(result, "查询成功");
    }


    @PostMapping(value = "/addPc")
    public String addPc(@RequestBody ZgSupplierInfoDTO zgSupplierInfoDTO) {
        try {
            zgSupplierInfoService.addPc(zgSupplierInfoDTO);
            return super.responseSuccess("新增成功");
        } catch (Exception e) {
            return super.responseFail("系统错误: " + e.getMessage());
        }
    }

    @PostMapping(value = "/addApp")
    public String addApp(@RequestBody ZgSupplierInfoDTO zgSupplierInfoDTO,HttpServletRequest req) {
        try {
            LoginUaaUser loginUser = LoginUserUtil.get(req);
            zgSupplierInfoService.addApp(zgSupplierInfoDTO,loginUser.getUaaId());
            return super.responseSuccess("新增成功");
        } catch (Exception e) {
            return super.responseFail("系统错误: " + e.getMessage());
        }
    }

    /**
     * 修改
     * @param dto
     * @return
     */
    @PostMapping("/update")
    @ResponseBody
    public String update(@RequestBody ZgSupplierInfo dto) {
        try {
            zgSupplierInfoService.update(dto);
            return super.responseSuccess("修改成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    @PostMapping("/delete/{id}")
    public String delete(@PathVariable Long id) {
        try {
            zgSupplierInfoService.delete(id);
            return super.responseSuccess("删除成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }



    @GetMapping("/id/{id}")
    public String getSupplierInfoById(@PathVariable("id") Long id) {
        ZgSupplierInfo supplierInfo = zgSupplierInfoService.getSupplierInfoById(id);
        return super.responseSuccess(supplierInfo, "查询成功");
    }

    @GetMapping("/getMySupplierInfo")
    public String getMySupplierInfo(HttpServletRequest req) {
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        ZgSupplierInfo supplierInfo = zgSupplierInfoService.getSupplierInfoByUaaId(loginUser.getUaaId());
        return super.responseSuccess(supplierInfo, "查询成功");
    }

    @GetMapping("/getMyBaseSupplierInfo")
    public String getMyBaseSupplierInfo(HttpServletRequest req) {
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        ZgBaseSupplierInfoVO supplierInfo = zgSupplierInfoService.getBaseSupplierInfoByUaaId(loginUser.getUaaId());
        return super.responseSuccess(supplierInfo, "查询成功");
    }


    @GetMapping("/uaaId/{uaaId}")
    public String getSupplierInfoByUaaId(@PathVariable("uaaId") Long uaaId) {
        ZgSupplierInfo supplierInfo = zgSupplierInfoService.getSupplierInfoByUaaId(uaaId);
        return super.responseSuccess(supplierInfo, "查询成功");
    }

    @GetMapping("/supplierNo/{supplierNo}")
    public String getSupplierInfoBySupplierNo(@PathVariable("supplierNo") String supplierNo) {
        ZgSupplierInfo supplierInfo = zgSupplierInfoService.getSupplierInfoBySupplierNo(supplierNo);
        return super.responseSuccess(supplierInfo, "查询成功");
    }


    /**
     * 审核配送员
     * @param id
     * @param auditStatus
     * @param reason
     * @param req
     * @return
     */
    @PostMapping("/audit")
    public String auditSupplier(@RequestParam Long id,
                              @RequestParam Integer auditStatus, @RequestParam(required = false) String reason, HttpServletRequest req) {
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        try {
            zgSupplierInfoService.auditSupplierInfo(id, auditStatus,reason, loginUser.getUaaId());
            return super.responseSuccess("审核成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }
}