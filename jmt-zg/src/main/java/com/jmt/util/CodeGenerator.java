package com.jmt.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import java.util.Date;
import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantLock;


public class CodeGenerator {

    // 业务前缀常量
    public static final String PREFIX_SUPPLIER = "SP"; // 供应商
    public static final String PREFIX_DRIVER = "DR";   // 配送员
    public static final String PREFIX_TICKET = "TK";   // 配送员

    // 存储各业务的自增序号（key：业务标识+日期，value：自增序号）
    private static final ConcurrentHashMap<String, AtomicInteger> SEQ_MAP = new ConcurrentHashMap<>(16);

    // 锁，用于清理过期数据
    private static final ReentrantLock CLEANUP_LOCK = new ReentrantLock();

    // 上次清理时间（每天清理一次）
    private static long lastCleanupTime = System.currentTimeMillis();

    // 清理间隔（24小时）
    private static final long CLEANUP_INTERVAL = 24 * 60 * 60 * 1000;

    /**
     * 生成供应商编号
     */
    public static String generateSupplierNo() {
        return generateCode(PREFIX_SUPPLIER);
    }

    /**
     * 生成配送员（骑手）编号
     */
    public static String generateDriverNo() {
        return generateCode(PREFIX_DRIVER);
    }
    /**
     * 生成囤货券编号
     */
    public static String generateTicketNo() {
        return generateCode(PREFIX_TICKET);
    }
    /**
     * 核心生成逻辑
     */
    private static String generateCode(String prefix) {
        // 1. 自动清理过期数据（每天执行一次）
        cleanupExpiredData();

        // 2. 获取当前日期（精确到天）
        String dateKey = DateUtil.format(new Date(), "yyyyMMdd");

        // 3. 业务+日期的唯一key
        String seqKey = prefix + "_" + dateKey;

        // 4. 获取或初始化自增序号（每天每个业务从1开始，最多999999）
        AtomicInteger seq = SEQ_MAP.computeIfAbsent(seqKey, k -> new AtomicInteger(1));
        int currentSeq = seq.getAndIncrement();
        // 超过最大值则重置
        if (currentSeq > 999999) {
            seq.set(1);
            currentSeq = 1;
        }

        // 5. 生成更详细的时间戳（精确到毫秒，共17位：yyyyMMddHHmmssSSS）
        String timestamp = DateUtil.format(new Date(), "yyyyMMddHHmmssSSS");

        // 6. 生成随机数（5位，增强唯一性）
        int randomNum = RandomUtil.randomInt(10000, 99999);

        // 7. 生成机器标识（简化版，取IP后两段的哈希值，0-999）
        int machineId = getMachineId();

        // 8. 拼接完整编号（总长度≈2+17+6+5+3=33位）
        return String.format("%s%s%06d%05d%03d",
                prefix,        // 2位：业务前缀
                timestamp,     // 17位：时间戳
                currentSeq,    // 6位：自增序号
                randomNum,     // 5位：随机数
                machineId      // 3位：机器标识
        );
    }

    /**
     * 清理过期的序号数据（超过24小时未使用的）
     */
    private static void cleanupExpiredData() {
        // 检查是否需要清理（每天一次）
        if (System.currentTimeMillis() - lastCleanupTime < CLEANUP_INTERVAL) {
            return;
        }

        // 加锁，确保只有一个线程执行清理
        if (CLEANUP_LOCK.tryLock()) {
            try {
                // 双重检查，避免多线程重复清理
                if (System.currentTimeMillis() - lastCleanupTime >= CLEANUP_INTERVAL) {
                    String currentDate = DateUtil.format(new Date(), "yyyyMMdd");
                    Iterator<Map.Entry<String, AtomicInteger>> iterator = SEQ_MAP.entrySet().iterator();

                    while (iterator.hasNext()) {
                        Map.Entry<String, AtomicInteger> entry = iterator.next();
                        String key = entry.getKey();
                        // 提取日期部分（格式：业务前缀_yyyyMMdd）
                        String datePart = key.substring(key.indexOf("_") + 1);

                        // 如果日期不是今天的，移除
                        if (!datePart.equals(currentDate)) {
                            iterator.remove();
                        }
                    }

                    lastCleanupTime = System.currentTimeMillis();
                }
            } finally {
                CLEANUP_LOCK.unlock();
            }
        }
    }

    /**
     * 获取机器标识（简化版，取IP后两段的哈希值）
     */
    private static int getMachineId() {
        try {
            // 实际生产环境建议使用更可靠的机器标识（如容器ID、配置文件指定）
            String ip = java.net.InetAddress.getLocalHost().getHostAddress();
            String[] parts = ip.split("\\.");
            if (parts.length >= 2) {
                return (parts[parts.length - 2] + parts[parts.length - 1]).hashCode() % 1000;
            }
        } catch (Exception e) {
            // 出错时返回随机机器ID
        }
        return RandomUtil.randomInt(0, 999);
    }
}