package com.jmt.dao;

import com.jmt.model.zg.ZgUser;

/**
* <AUTHOR>
* @description 针对表【zg_user(筷圣直供用户表)】的数据库操作Mapper
* @createDate 2025-07-11 09:52:02
* @Entity com.jmt.model.zg.ZgUser
*/
public interface ZgUserMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ZgUser record);

    int insertSelective(ZgUser record);

    ZgUser selectByPrimaryKey(Long id);
    ZgUser selectByuaaId(Long id);
    int updateByPrimaryKeySelective(ZgUser record);

    int updateByPrimaryKey(ZgUser record);

}
