package com.jmt.dao;

import com.jmt.model.zg.entity.ZgProductOrderDetail;
import com.jmt.model.zg.vo.ProductOrderDetailVO;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface ZgProductOrderDetailDao {
    int insert(ZgProductOrderDetail detail);

    List<ZgProductOrderDetail> selectByOrderNo(@Param("orderNo") String orderNo);

    List<ZgProductOrderDetail> selectByUserId(@Param("buyUserId") Long buyUserId);

    int delete(@Param("orderNo") String orderNo);

    List<ProductOrderDetailVO> selectOrderDetailList(@Param("orderNo")String orderNo);

}
