package com.jmt.dao;

import com.jmt.model.zg.entity.ZgProductCart;
import com.jmt.model.zg.vo.ZgProductCartVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ZgProductCartDao {
    int insert(ZgProductCart record);

    int deleteByUserId(Long buyUserId);
    int delete(Long id);
    ZgProductCart selectById(Long id);

    int updateQuantity(@Param("id") Long id, @Param("quantity")Integer quantity);
    List<ZgProductCart> selectByCondition(ZgProductCart zgProductCart);
    int selectCartCount(@Param("buyUserId")Long buyUserId);

    // 增加数量
    int increaseQuantity(@Param("id") Long id, @Param("delta") Integer delta);
    // 减少数量
    int decreaseQuantity(@Param("id") Long id, @Param("delta") Integer delta);

    List<ZgProductCartVO> selectCartWithProductInfo(@Param("buyUserId") Long buyUserId,@Param("cartIds") List<Long> cartIds);

    List<ZgProductCart> selectByIdsAndUserId( @Param("cartIds") List<Long> cartIds,@Param("buyUserId") Long buyUserId);

    void deleteByIds(@Param("cartIds") List<Long> cartIds);

    ZgProductCart findByUserAndProduct(@Param("buyUserId") Long buyUserId,
                                       @Param("productNo") String productNo);
}
