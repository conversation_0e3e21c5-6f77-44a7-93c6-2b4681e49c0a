package com.jmt.dao;

import com.jmt.model.zg.entity.ZgCouponPackage;
import com.jmt.model.zg.vo.ZgCouponVO;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface ZgCouponPackageDao {
    int insert(ZgCouponPackage zgCouponPackage);
    int delete(Long id);
    ZgCouponPackage selectById(Long id);

    List<ZgCouponPackage> selectByUserId(Long buyUserId);
    int updateById(ZgCouponPackage zgCouponPackage);
    List<ZgCouponVO> selectHomePageCoupons(@Param("userId") Long userId);

    // 获取所有可领取的优惠券（用于未登录用户）
    List<ZgCouponVO> selectAllAvailableCoupons();
}
