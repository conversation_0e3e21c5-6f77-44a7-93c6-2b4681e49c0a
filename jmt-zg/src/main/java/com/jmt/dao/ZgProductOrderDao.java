package com.jmt.dao;

import com.jmt.model.zg.dto.ZgProductOrderDTO;
import com.jmt.model.zg.entity.ZgProductOrder;
import com.jmt.model.zg.vo.ZgProductOrderAppVO;
import com.jmt.model.zg.vo.ZgProductOrderDetailAppVO;
import com.jmt.model.zg.vo.ZgProductOrderDetailVO;
import com.jmt.model.zg.vo.ZgProductOrderIndexVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface ZgProductOrderDao {
    int insert(ZgProductOrder order);

    ZgProductOrder selectByOrderNo(@Param("orderNo") String orderNo);

    List<ZgProductOrder> selectByUserId(@Param("buyUserId") Long buyUserId);

    int updateById(ZgProductOrder order);

    int delete(@Param("id") Long id);

    int updateOrderStatus(@Param("orderNo") String orderNo,
                          @Param("orderStatus") Integer orderStatus,
                          @Param("payType") Integer payType,
                          @Param("updateTime") Date updateTime);

    List<ZgProductOrderIndexVO> selectByCondition(ZgProductOrderDTO zgProductOrder);

    int assignDriver(@Param("orderNo") String orderNo, @Param("driverNo") String driverNo);

    ZgProductOrderDetailVO selectPcOrderDetails(String orderNo);


    List<ZgProductOrderDetailAppVO> selectOrderDetailsByUser(@Param("buyUserId") Long buyUserId);


    List<ZgProductOrderAppVO> selectOrderBaseByUserId(ZgProductOrder zgProductOrder);
}
