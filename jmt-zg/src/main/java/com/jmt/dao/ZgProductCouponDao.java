package com.jmt.dao;

import com.jmt.model.zg.entity.ZgProductCoupon;
import com.jmt.model.zg.vo.ZgProductCouponVO;

import java.util.List;

public interface ZgProductCouponDao {
    int insert(ZgProductCoupon zgProductCoupon);
    int delete(Long id);
    ZgProductCoupon selectById(Long id);

    ZgProductCoupon selectByCouponNo(String couponNo);
    int updateById(ZgProductCoupon zgProductCoupon);
    List<ZgProductCouponVO> selectByCondition(ZgProductCoupon zgProductCoupon);
}
