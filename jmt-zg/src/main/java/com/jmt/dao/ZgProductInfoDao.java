package com.jmt.dao;

import com.jmt.model.zg.entity.ZgProductInfo;
import com.jmt.model.zg.vo.ZgMallProductListVO;
import com.jmt.model.zg.vo.ZgMyProductListVO;
import com.jmt.model.zg.vo.ZgProductInfoVO;

import java.util.List;

public interface ZgProductInfoDao {
    int insert(ZgProductInfo record);
    int delete(Long id);
    ZgProductInfo selectById(Long id);
    ZgProductInfo selectByProductNo(String productNo);
    int updateById(ZgProductInfo record);
    List<ZgProductInfoVO> selectByCondition(ZgProductInfo zgProductInfo);
    List<ZgMyProductListVO> selectMyListByCondition(ZgProductInfo zgProductInfo);
    List<ZgMallProductListVO> selectMallListByCondition(ZgProductInfo zgProductInfo);
    int updateAuditStatus(ZgProductInfo zgProductInfo);

    List<Long> findCategoryIdsWithProducts(List<Long> allSubCategoryIds);
}
