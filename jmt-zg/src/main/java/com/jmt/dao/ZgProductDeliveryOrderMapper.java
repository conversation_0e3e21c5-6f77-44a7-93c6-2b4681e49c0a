package com.jmt.dao;

import com.jmt.model.zg.ZgProductDeliveryOrder;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【zg_product_delivery_order(筷圣直供商品配送订单表)】的数据库操作Mapper
* @createDate 2025-07-11 09:52:02
* @Entity com.jmt.model.zg.ZgProductDeliveryOrder
*/
public interface ZgProductDeliveryOrderMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ZgProductDeliveryOrder record);

    int insertSelective(ZgProductDeliveryOrder record);

    ZgProductDeliveryOrder selectByPrimaryKey(Long id);
    int updateByPrimaryKeySelective(ZgProductDeliveryOrder record);

    int updateByPrimaryKey(ZgProductDeliveryOrder record);

    List<ZgProductDeliveryOrder> getDeliveryOrderPage(ZgProductDeliveryOrder queryData);

    ZgProductDeliveryOrder selectByOrderNo( String orderNo);
}
