package com.jmt.dao;

import com.jmt.model.zg.TicketOrderDetail;
import com.jmt.model.zg.ZgTicketOrder;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【zg_ticket_order(筷圣直供囤货券订单表)】的数据库操作Mapper
* @createDate 2025-07-11 09:52:02
* @Entity com.jmt.model.zg.ZgTicketOrder
*/
public interface ZgTicketOrderMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ZgTicketOrder record);

    int insertSelective(ZgTicketOrder record);

    ZgTicketOrder selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ZgTicketOrder record);

    int updateByPrimaryKey(ZgTicketOrder record);

    List<TicketOrderDetail> getTicketInfoPage(TicketOrderDetail ticketOrderDetail);
}
