package com.jmt.dao;

import com.jmt.model.zg.ZgTicketInfo;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【zg_ticket_info(筷圣直供囤货券信息表)】的数据库操作Mapper
* @createDate 2025-07-11 09:52:02
* @Entity com.jmt.model.zg.ZgTicketInfo
*/
public interface ZgTicketInfoMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ZgTicketInfo record);

    int insertSelective(ZgTicketInfo record);

    ZgTicketInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ZgTicketInfo record);

    int updateByPrimaryKey(ZgTicketInfo record);

    List<ZgTicketInfo> getTicketInfoPage(ZgTicketInfo queryData);

    ZgTicketInfo selectByTicketNo(String ticketNo);
}
