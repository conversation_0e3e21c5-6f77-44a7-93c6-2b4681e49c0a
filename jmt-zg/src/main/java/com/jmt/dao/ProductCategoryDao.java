package com.jmt.dao;

import com.jmt.model.zg.ZgProductCategoryVo;
import com.jmt.model.zg.entity.ZgProductCategory;
import com.jmt.model.zg.vo.ZgProductCategoryChildVo;

import java.util.List;

public interface ProductCategoryDao {

    List<ZgProductCategoryVo> selectCategory();

    int updateById(ZgProductCategoryVo vo);


    List<ZgProductCategoryVo> selectLevel1Categories();
    List<ZgProductCategoryChildVo> selectSubCategoriesByParentId(Long parentId);

    int insert(ZgProductCategory zgProductCategory);
}
