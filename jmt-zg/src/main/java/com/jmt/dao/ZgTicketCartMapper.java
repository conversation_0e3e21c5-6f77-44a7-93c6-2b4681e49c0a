package com.jmt.dao;

import com.jmt.model.zg.ZgTicketCart;
import com.jmt.model.zg.ZgTicketInfo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
* <AUTHOR>
* @description 针对表【zg_ticket_cart(筷圣直供购物车表)】的数据库操作Mapper
* @createDate 2025-07-11 09:52:02
* @Entity com.jmt.model.zg.ZgTicketCart
*/
public interface ZgTicketCartMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ZgTicketCart record);

    int insertSelective(ZgTicketCart record);

    ZgTicketCart selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ZgTicketCart record);

    int updateByPrimaryKey(ZgTicketCart record);

    void updateCartstatus(@Param("buyUserId") Long buyUserId, @Param("ticketNo") String ticketNo, @Param("date") Date date);

    ZgTicketCart selectByTicketNoAndBuyUserId(
            @Param("ticketNo") String ticketNo,
            @Param("buyUserId") Long buyUserId
    );

    ZgTicketInfo selectByTicketNo(String ticketNo);

    void updateCartStatus(
            @Param("buyUserId") Long buyUserId,  // 指定参数名 buyUserId
            @Param("ticketNo") String ticketNo,  // 指定参数名 ticketNo
            @Param("date") Date date             // 指定参数名 date
    );
}
