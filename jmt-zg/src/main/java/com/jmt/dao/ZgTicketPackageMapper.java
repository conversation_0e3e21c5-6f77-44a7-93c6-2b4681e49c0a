package com.jmt.dao;

import com.jmt.model.zg.ZgTicketPackage;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【zg_ticket_pakage(筷圣直供囤货券包表)】的数据库操作Mapper
* @createDate 2025-07-11 09:52:02
* @Entity com.jmt.model.zg.ZgTicketPackage
*/
public interface ZgTicketPackageMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ZgTicketPackage record);

    int insertSelective(ZgTicketPackage record);

    ZgTicketPackage selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ZgTicketPackage record);

    int updateByPrimaryKey(ZgTicketPackage record);

    ZgTicketPackage selectByTicketNoAndBuyUserId( @Param("ticketSeqNo") String ticketSeqNo,
                                                  @Param("buyUserId") Long buyUserId );

    ZgTicketPackage selectByUserIdAndTicketNo(@Param("buyUserId") Long buyUserId,
                                              @Param("ticketNo") String ticketNo);
}
