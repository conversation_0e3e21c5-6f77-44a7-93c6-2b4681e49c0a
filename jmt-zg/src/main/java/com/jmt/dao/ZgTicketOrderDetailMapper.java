package com.jmt.dao;

import com.jmt.model.zg.ZgTicketOrderDetail;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【zg_ticket_order_detail(筷圣直供囤货券订单明细表)】的数据库操作Mapper
* @createDate 2025-07-11 09:52:02
* @Entity com.jmt.model.zg.ZgTicketOrderDetail
*/
public interface ZgTicketOrderDetailMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ZgTicketOrderDetail record);

    int insertSelective(ZgTicketOrderDetail record);

    ZgTicketOrderDetail selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ZgTicketOrderDetail record);

    int updateByPrimaryKey(ZgTicketOrderDetail record);

    void updateCartStatus(Long buyUserId, String ticketNo, Date date);

    List<ZgTicketOrderDetail> selectByOrderNo(String orderNo);
    ZgTicketOrderDetail selectByTicketNo(String ticketNo);
}
