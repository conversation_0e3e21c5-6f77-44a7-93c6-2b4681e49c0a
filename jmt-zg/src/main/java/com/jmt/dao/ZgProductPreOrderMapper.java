package com.jmt.dao;

import com.jmt.model.zg.ZgProductPreOrder;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【zg_product_pre_order(筷圣直供商品预订订单表)】的数据库操作Mapper
* @createDate 2025-07-11 09:52:02
* @Entity com.jmt.model.zg.ZgProductPreOrder
*/
public interface ZgProductPreOrderMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ZgProductPreOrder record);

    int insertSelective(ZgProductPreOrder record);

    ZgProductPreOrder selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ZgProductPreOrder record);

    int updateByPrimaryKey(ZgProductPreOrder record);

    List<ZgProductPreOrder> getTicketInfoPage(ZgProductPreOrder queryData);

    ZgProductPreOrder selectByOrderNo(String orderNo);
}
