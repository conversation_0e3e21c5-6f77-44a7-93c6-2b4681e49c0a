package com.jmt.dao;

import com.jmt.model.zg.ZgProductDeliveryOrderDetail;

/**
* <AUTHOR>
* @description 针对表【zg_product_delivery_order_detail(筷圣直供商品配送订单明细表)】的数据库操作Mapper
* @createDate 2025-07-11 09:52:02
* @Entity com.jmt.model.zg.ZgProductDeliveryOrderDetail
*/
public interface ZgProductDeliveryOrderDetailMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ZgProductDeliveryOrderDetail record);

    int insertSelective(ZgProductDeliveryOrderDetail record);

    ZgProductDeliveryOrderDetail selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ZgProductDeliveryOrderDetail record);

    int updateByPrimaryKey(ZgProductDeliveryOrderDetail record);

}
