package com.jmt.dao;

import com.jmt.model.zg.entity.ZgSupplierInfo;
import com.jmt.model.zg.vo.ZgBaseSupplierInfoVO;
import com.jmt.model.zg.vo.ZgSupplierInfoVO;

import java.util.List;

public interface ZgSupplierInfoDao {
    int insert(ZgSupplierInfo zgSupplierInfo);
    int delete(Long id);
    ZgSupplierInfo selectById(Long id);
    ZgSupplierInfo selectByUaaId(Long uaaId);
    ZgSupplierInfo selectBySupplierNo(String supplierNo);
    int updateById(ZgSupplierInfo zgSupplierInfo);
    List<ZgSupplierInfoVO> selectByCondition(ZgSupplierInfo zgSupplierInfo);

    int countByUaaId(Long uaaId);

    ZgBaseSupplierInfoVO getBaseSupplierInfoByUaaId(Long uaaId);
}
