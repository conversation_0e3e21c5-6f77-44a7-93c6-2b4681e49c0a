package com.jmt.dao;

import com.jmt.model.page.PageQuery;
import com.jmt.model.zg.ZgProduct;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【zg_product(商品信息表)】的数据库操作Mapper
* @createDate 2025-08-20 10:49:35
* @Entity generator.domain.ZgProduct
*/
public interface ZgProductMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ZgProduct record);

    int insertSelective(ZgProduct record);

    ZgProduct selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ZgProduct record);

    int updateByPrimaryKey(ZgProduct record);

    List<ZgProduct> listProduct(PageQuery<ZgProduct> pageQuery);
}
