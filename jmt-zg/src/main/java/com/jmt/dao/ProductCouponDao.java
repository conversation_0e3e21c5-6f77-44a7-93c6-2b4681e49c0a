package com.jmt.dao;

import com.jmt.model.zg.ZgProductCouponDto;
import com.jmt.model.zg.ZgProductCouponVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProductCouponDao {

    List<ZgProductCouponVo> getPage(ZgProductCouponDto dto);

    ZgProductCouponVo getInfo(@Param("id") Long id);

    Integer insert(ZgProductCouponDto dto);

    Integer update(ZgProductCouponDto dto);

    ZgProductCouponVo getInfoByNo(String couponNo);
}
