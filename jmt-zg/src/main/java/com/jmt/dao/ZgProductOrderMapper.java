package com.jmt.dao;

import com.jmt.model.zg.ZgProductOrder;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【zg_product_order(筷圣直供商品订单表)】的数据库操作Mapper
* @createDate 2025-07-11 09:52:02
* @Entity com.jmt.model.zg.ZgProductOrder
*/
public interface ZgProductOrderMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ZgProductOrder record);

    int insertSelective(ZgProductOrder record);

    ZgProductOrder selectByPrimaryKey(Long id);
    int updateByPrimaryKeySelective(ZgProductOrder record);

    int updateByPrimaryKey(ZgProductOrder record);
    List<ZgProductOrder> getProductOrderPage(ZgProductOrder queryData);

    ZgProductOrder selectByOrderNo(String orderNo);
}
