package com.jmt.dao;

import com.jmt.model.zg.ZgProductPreOrderDetail;

/**
* <AUTHOR>
* @description 针对表【zg_product_pre_order_detail(筷圣直供商品预订订单明细表)】的数据库操作Mapper
* @createDate 2025-07-11 09:52:02
* @Entity com.jmt.model.zg.ZgProductPreOrderDetail
*/
public interface ZgProductPreOrderDetailMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ZgProductPreOrderDetail record);

    int insertSelective(ZgProductPreOrderDetail record);

    ZgProductPreOrderDetail selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ZgProductPreOrderDetail record);

    int updateByPrimaryKey(ZgProductPreOrderDetail record);

}
