package com.jmt.interceptor;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.jmt.constant.JmtConstant;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.util.JwtTokenUtil;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

public class TokenRequestInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate template) {

        Thread currentThread = Thread.currentThread();
        String threadName = currentThread.getName();
        if (threadName.startsWith("Scheduler_Worker")) {
            LoginUaaUser userDetails = new LoginUaaUser();
            userDetails.setUaaId(-1L);
            userDetails.setLoginName("Scheduler_Worker");
            String schedulerToken = JwtTokenUtil.createToken(BeanUtil.beanToMap(userDetails),userDetails.getLoginName());
            template.header(JmtConstant.AUTHORIZATION, schedulerToken);
        } else {
            ServletRequestAttributes servletRequest = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
            HttpServletRequest request = servletRequest.getRequest();
            String token = request.getHeader(JmtConstant.AUTHORIZATION);
            if (StrUtil.isNotEmpty(token)){
                // 将Token添加到请求的头部信息中
                template.header(JmtConstant.AUTHORIZATION, token);
            }
        }

    }
}

