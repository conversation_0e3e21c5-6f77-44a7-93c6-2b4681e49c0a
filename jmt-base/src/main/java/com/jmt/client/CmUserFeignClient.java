package com.jmt.client;

import com.jmt.config.FeignConfig;
import com.jmt.model.jhh.dto.TransferSalesmanEqDto;
import com.jmt.model.uaa.dto.UaaUserDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="jmt-cm",path ="/cm/user/v1",configuration = FeignConfig.class)
public interface CmUserFeignClient {

    @PostMapping(value = "/add")
    String add(@RequestBody UaaUserDTO userDTO);

    @GetMapping(value = "/getUserList")
    String getUserList(@RequestParam("operatorNo") String operatorNo);

    @PostMapping(value = "/transferSalesman")
    String transferSalesman(@RequestBody TransferSalesmanEqDto transferSalesmanEqDto);

    @GetMapping(value = "/getCmUserNo")
    String getCmUserNo(@RequestParam("uaaId") Long uaaId);
}
