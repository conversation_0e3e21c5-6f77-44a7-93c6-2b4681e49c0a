package com.jmt.client;

import com.jmt.config.FeignConfig;
import com.jmt.model.profit.dto.BalancePayDto;
import com.jmt.model.profit.dto.PointPayDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name="jmt-profit",path ="/profit",configuration = FeignConfig.class)
public interface ProfitFeignClient {

    @PostMapping("/payment/v1/balancePayByUaaId")
    String balanceByUaaIdPay(@RequestBody BalancePayDto dto);

    @PostMapping("/payment/v1/pointsPayByUaaId")
    String pointsByUaaIdPay(@RequestBody PointPayDTO dto);

    @GetMapping("/project/v1/getShareProjectSelect")
    String getShareProjectSelect();
}
