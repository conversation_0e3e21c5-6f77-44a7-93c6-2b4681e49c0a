package com.jmt.client;

import com.jmt.config.FeignConfig;
import com.jmt.model.cm.entity.CmBusinessMange;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;


@FeignClient(name="jmt-cm",path ="/cm/business/v1",configuration = FeignConfig.class)
public interface CmBusinessInfoFeignClient {
    @GetMapping("/{id}")
    String getById(@PathVariable("id") Long id);

    @GetMapping("/getNumberByProvince")
    String getNumberByProvince(@RequestParam("province") String province);

    @PostMapping("/getBusinessList")
    String getBusinessList(@RequestBody(required = false)CmBusinessMange cmBusinessMange);

    @PostMapping(value = "/getByCreditCode")
    String getByCreditCode(@RequestParam("creditCode") String creditCode);

}
