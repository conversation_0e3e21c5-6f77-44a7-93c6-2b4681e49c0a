package com.jmt.client;
import com.jmt.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="jmt-eq",path ="/eq/v1",configuration = FeignConfig.class)
public interface EqFeignClient {

    @PostMapping(value = "/getInfoByBusNo")
    String getInfoByBusNo(@RequestParam("busNo") String busNo);

    @PostMapping(value = "/getInfoByShopNo")
    String getInfoByShopNo(@RequestParam("shopNo") String shopNo);
}
