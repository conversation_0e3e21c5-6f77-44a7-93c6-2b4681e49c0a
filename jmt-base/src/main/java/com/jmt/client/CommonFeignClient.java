package com.jmt.client;

import com.jmt.config.FeignConfig;
import com.jmt.model.auth.SmsCodeDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

@FeignClient(name="jmt-common",path ="/common",configuration = FeignConfig.class)
public interface CommonFeignClient {

    @PostMapping("/sms/send")
    String sendSms(@RequestBody SmsCodeDto smsCodeDto);
}
