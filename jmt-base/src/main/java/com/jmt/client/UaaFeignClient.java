package com.jmt.client;

import com.jmt.config.FeignConfig;
import com.jmt.model.uaa.dto.UaaUserDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

@FeignClient(name="jmt-uaa", path ="/uaa", configuration = FeignConfig.class)
public interface UaaFeignClient {

    @PostMapping(value ="/v1/addUaaUser", produces = "application/json")
    String registerUser(@RequestBody UaaUserDTO userDTO);

    @PostMapping(value ="/v1/lock", produces = "application/json")
    String lock(@RequestParam("uaaId") Long uaaId);

    @PostMapping(value ="/v1/lock", produces = "application/json")
    String unlock(@RequestParam("uaaId") Long uaaId);

    @PostMapping("/wallet/v1/decreaseBalance")
    String decreaseBalance(@RequestParam("uaaId") Long uaaId, @RequestParam("amount") BigDecimal amount);

    @PostMapping("/wallet/v1/validatePayPassword")
    String validatePayPassword(@RequestParam("uaaId") Long uaaId, @RequestParam("payPassword") String payPassword);

    @PostMapping("/wallet/v1/decreasePoints")
    String decreasePoints(@RequestParam("uaaId") Long uaaId, @RequestParam("points") Integer points);

    @GetMapping("/wallet/v1/{uaaId}")
    String getWalletByUaaId(@PathVariable("uaaId") Long uaaId);

    @GetMapping("/bankcard/v1/{id}")
    String getBankcardById(@PathVariable("id") Long id);

    @PostMapping("/wallet/v1/increasePoints")
    String increasePoints(@RequestParam("uaaId") Long uaaId, @RequestParam("points") Integer points);

    @PostMapping("/wallet/v1/increaseBalance")
    String increaseBalance(@RequestParam("uaaId") Long uaaId, @RequestParam("amount") BigDecimal amount);

    @GetMapping("/address/v1/default/{id}")
    String getDefaultAddress(@PathVariable("id") Long id);

    @GetMapping("/bankcard/v1/default/{id}")
    String getDefaultBankcard(@PathVariable("id") Long id);

    @PostMapping("/v1/delete/{id}")
    String deleteUaa(@PathVariable("id") Long id);

    @GetMapping("/v1/getByPhone/{telPhone}")
    String existsByTelPhone(@PathVariable("telPhone") String telPhone);


    @GetMapping("/address/v1/getByIds")
    String getAddressByIds(@RequestParam("ids") List<Long> ids);

    @GetMapping("/v1/getUaaByUaaIds")
    String getUaaByUaaIds(@RequestParam("ids") List<Long> ids);

    @GetMapping("/address/v1/{id}")
    String getById(@PathVariable("id") Long id);
}
