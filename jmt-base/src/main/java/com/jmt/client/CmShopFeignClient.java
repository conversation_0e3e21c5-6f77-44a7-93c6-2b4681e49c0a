package com.jmt.client;

import com.jmt.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="jmt-cm",path ="/cm/businessShop/v1",configuration = FeignConfig.class)
public interface CmShopFeignClient {

    @GetMapping(value = "/getShopByNo")
    String getShopByNo(@RequestParam("shopNo") String shopNo);
}
