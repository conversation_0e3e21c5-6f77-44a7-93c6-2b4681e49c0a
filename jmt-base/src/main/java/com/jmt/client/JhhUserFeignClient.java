package com.jmt.client;

import com.jmt.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="jmt-jhh",path ="/jhh",configuration = FeignConfig.class)
public interface JhhUserFeignClient {

    @GetMapping(value = "/user/v1/getJhhUserNo")
    String getJhhUserNo(@RequestParam("uaaId") Long uaaId);

}
