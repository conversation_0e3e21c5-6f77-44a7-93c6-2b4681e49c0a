package com.jmt.client;
import com.jmt.config.FeignConfig;
import com.jmt.model.eq.dto.InvestorEqPageQueryDto;
import com.jmt.model.jhh.dto.AssignInvestorEqDto;
import com.jmt.model.jhh.dto.TransferOperatorEqDto;
import com.jmt.model.jhh.dto.TransferSalesmanEqDto;
import com.jmt.model.page.PageQuery;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="jmt-eq",path ="/eq/investor/v1",configuration = FeignConfig.class)
public interface InvestorEqFeignClient {

    @PostMapping(value = "/assign")
    String assign(@RequestBody AssignInvestorEqDto assignInvestorEqDto);

    @PostMapping(value = "/page")
    String getInvestorEqPage(@RequestBody PageQuery<String> pageQuery);

    @PostMapping(value = "/optionalPage")
    String getOptionalPage(@RequestBody PageQuery<InvestorEqPageQueryDto> pageQuery);
}
