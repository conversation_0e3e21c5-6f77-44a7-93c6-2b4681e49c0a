package com.jmt.client;

import com.jmt.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="jmt-jhh",path ="/jhh/salesman/v1",configuration = FeignConfig.class)
public interface JhhSalesmanFeignClient {

    @GetMapping(value = "/getInfoBySalesmanNo")
    String getInfoBySalesmanNo(@RequestParam("salesmanNo") String salesmanNo);

    @GetMapping(value = "/getSalesmanInfoById")
    String getSalesmanInfoById(@RequestParam("id") Long id);
}
