package com.jmt.client;
import com.jmt.config.FeignConfig;
import com.jmt.model.eq.dto.InvestorEqPageQueryDto;
import com.jmt.model.jhh.dto.AssignInvestorEqDto;
import com.jmt.model.jhh.dto.TransferOperatorEqDto;
import com.jmt.model.jhh.dto.TransferSalesmanEqDto;
import com.jmt.model.page.PageQuery;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="jmt-eq",path ="/eq/operator/v1",configuration = FeignConfig.class)
public interface OperatorEqFeignClient {

    @PostMapping(value = "/transfer")
    String transfer(@RequestBody TransferOperatorEqDto transferOperatorEqDto);
}
