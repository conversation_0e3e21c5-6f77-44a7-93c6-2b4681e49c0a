package com.jmt.client;

import com.jmt.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
@FeignClient(name="jmt-jhh",path ="/jhh/operator/v1",configuration = FeignConfig.class)
public interface JhhOperatorFeignClient {

    @GetMapping(value = "/getOperatorInfoByUaaId")
    String getOperatorInfoByUaaId(@RequestParam("uaaId") Long uaaId);

    @GetMapping(value = "/getOperatorInfoByOperatorNo")
    String getOperatorInfoByOperatorNo(@RequestParam("operatorNo") String operatorNo);

    @GetMapping(value = "/getShopRatioByOperatorNo")
    String getShopRatioByOPN(@RequestParam("operatorNo") String operatorNo);
}
