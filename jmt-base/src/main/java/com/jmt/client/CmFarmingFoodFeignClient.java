package com.jmt.client;

import com.jmt.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="jmt-cm",path ="/cm/farmingFoodInfo",configuration = FeignConfig.class)
public interface CmFarmingFoodFeignClient {

    @GetMapping("/v1/orderNo/{orderNo}")
    String getOrderByOrderNo(@PathVariable("orderNo") String orderNo);

    @PostMapping("/v1/updateOrderStatus/{orderNo}")
    String updateStatusByOrderNo(@PathVariable("orderNo") String orderNo,@RequestParam("auditStatus") Integer auditStatus);
}
