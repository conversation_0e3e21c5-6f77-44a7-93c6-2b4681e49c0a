package com.jmt.client;

import com.jmt.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name="jmt-cm",path ="/cm/workEqApply",configuration = FeignConfig.class)
public interface CmWorkEqApplyFeignClient {

    @GetMapping("/v1/getNumberByProvince")
    String getNumberByProvince(@RequestParam("province") String province);
}
