package com.jmt.constant;

/**
 * RedisKey常量类
 * <AUTHOR>
 */
public class RedisKeyConstant {

    /**
     * token key
     */
    public static final String TOKEN_CACHE_KEY = "auth:token:";
    /**
     * token 15天过期
     */
    public static final long TOKEN_CACHE_EXPIRE = 15 * 24 * 60 * 60L;
    /**
     * 登录验证码 key
     */
    public static final String CAPTCHA_CACHE_KEY = "auth:captcha:";
    /**
     * 登录验证码 5分钟过期
     */
    public static final long CAPTCHA_CACHE_EXPIRE = 5 * 60L;

    /**
     * 短信验证key
     */
    public static final String SMS_CACHE_KEY = "sms:code:";
    /**
     * 短信验证码 5分钟过期
     */
    public static final long SMS_CACHE_EXPIRE = 5 * 60L;
    /**
     * 限制重复获取短信验证key
     */
    public static final String SMS_SEND_STINT = "sms:stint:";

    /**
     * 10秒内禁止重复获取验证码
     */
    public static final long SMS_SEND_STINT_EXPIRE = 10L;

    /**
     * 微信token
     */
    public static final String WX_TOKEN_CACHE_KEY = "wx:access:token:";
    /**
     * 微信token 1小时过期
     */
    public static final long WX_TOKEN_CACHE_EXPIRE = 60 * 60L;
}

