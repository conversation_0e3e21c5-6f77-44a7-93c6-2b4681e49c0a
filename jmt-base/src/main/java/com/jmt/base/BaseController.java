package com.jmt.base;

import cn.hutool.core.map.MapUtil;
import com.jmt.util.GsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Map;


/**
 * Controller基类
 */
public class BaseController {

	protected final Logger logger = LoggerFactory.getLogger(this.getClass());

	/**
	 * 系统错误编码
	 */
	private static final Integer SERVICE_RESPONSE_ERROR_CODE = 500;
	/**
	 * 无访问权限编码
	 */
	private static final Integer SERVICE_RESPONSE_NO_ACCESS_CODE = 403;
	/**
	 * 身份认证失败编码
	 */
	private static final Integer SERVICE_RESPONSE_FAIL_AUTH_CODE = 301;
	/**
	 * 身份认证成功编码
	 */
	private static final Integer SERVICE_RESPONSE_SUCCESS_AUTH_CODE = 200;
	/**
	 * 服务端返回失败编码
	 */
	private static final Integer SERVICE_RESPONSE_FAIL_CODE = 201;
	/**
	 * 服务端返回成功编码
	 */
	private static final Integer SERVICE_RESPONSE_SUCCESS_CODE = 200;

	/**
	 * 成功
	 *
	 * @param msg 服务端输出对象
	 * @return 输出处理结果给前段JSON格式数据
	 * @since 2015-01-06
	 */
	protected String responseSuccess(String msg) {
		Map<Object, Object> resMap = MapUtil.builder().map();
		resMap.put("code",SERVICE_RESPONSE_SUCCESS_CODE);
		resMap.put("msg",msg);
		resMap.put("data","");
		return GsonUtil.toJson(resMap);
	}

	protected String responseSuccess(Object obj, String msg) {
		Map<Object, Object> resMap = MapUtil.builder().map();
		resMap.put("code",SERVICE_RESPONSE_SUCCESS_CODE);
		resMap.put("msg",msg);
		resMap.put("data",obj);
		return GsonUtil.toJson(resMap);
	}
	/**
	 * 失败
	 *
	 * @param msg 错误信息
	 * @return 输出失败的JSON格式数据
	 */
	protected String responseFail(String msg) {
		Map<Object, Object> resMap = MapUtil.builder().map();
		resMap.put("code",SERVICE_RESPONSE_FAIL_CODE);
		resMap.put("msg",msg);
		resMap.put("data","");
		return GsonUtil.toJson(resMap);
	}

	/**
	 * 认证成功
	 *
	 * @param token token
	 * @return 输出失败的JSON格式数据
	 */
	protected String responseSuccessAuth(String token) {
		Map<Object, Object> resMap = MapUtil.builder().map();
		resMap.put("code",SERVICE_RESPONSE_SUCCESS_AUTH_CODE);
		resMap.put("msg","认证成功");
		resMap.put("data",token);
		return GsonUtil.toJson(resMap);
	}

	/**
	 * 认证失败
	 * @return 输出失败的JSON格式数据
	 */
	protected String responseFailAuth(String msg) {
		Map<Object, Object> resMap = MapUtil.builder().map();
		resMap.put("code",SERVICE_RESPONSE_FAIL_AUTH_CODE);
		resMap.put("msg",msg);
		resMap.put("data",msg);
		return GsonUtil.toJson(resMap);
	}

	/**
	 * 无权限访问
	 * @return 输出失败的JSON格式数据
	 */
	@ResponseBody
	public String responseNoAccess() {
		Map<Object, Object> resMap = MapUtil.builder().map();
		resMap.put("code",SERVICE_RESPONSE_NO_ACCESS_CODE);
		resMap.put("msg","无访问权限");
		resMap.put("data","");
		return GsonUtil.toJson(resMap);
	}


	@ExceptionHandler(value = { Exception.class })
	@ResponseBody
	public String responseError(Exception e) {
		logger.error("api service occurred exception", e);
		Map<Object, Object> resMap = MapUtil.builder().map();
		resMap.put("code",SERVICE_RESPONSE_ERROR_CODE);
		resMap.put("msg",e.getMessage());
		resMap.put("data","");
		return GsonUtil.toJson(resMap);
	}

}
