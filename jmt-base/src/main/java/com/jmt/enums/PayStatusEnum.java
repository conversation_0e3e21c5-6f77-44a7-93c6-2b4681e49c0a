package com.jmt.enums;

/**
 * 支付状态枚举
 */
public enum PayStatusEnum {
    UNPAID(0, "未支付"),
    SUCCESS(1, "支付成功"),
    FAILED(2, "支付失败"),
    CANCELLED(3, "取消支付");

    private final Integer code;
    private final String description;

    PayStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据code获取枚举
     * @param code 状态码
     * @return 对应的枚举，找不到返回null
     */
    public static PayStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (PayStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

}
