package com.jmt.enums;

/**
 * 积分相关常量枚举类
 */
public enum PointConstantEnum {

    INSTANCE;

    // 最低提现积分
    private final int MIN_POINT = 10;

    // 最高提现积分
    private final int MAX_POINT = 5000000;

    // 每日兑现积分上限
    private final int MAX_DAY_COUNT = 3;

    // 每周兑现次数上限
    private final int MAX_WEEK_COUNT = 10;

    // 每月兑现次数上限
    private final int MAX_MONTH_COUNT = 20;

    // 积分余额兑换比例
    private final Double POINT_RATIO = 0.01;

    // 税率
    private final Double TAX_RATIO = 0.05;

    // getter方法
    public int getMinPoint() {
        return MIN_POINT;
    }

    public int getMaxPoint() {
        return MAX_POINT;
    }

    public int getMaxDayCount() {
        return MAX_DAY_COUNT;
    }

    public int getMaxWeekCount() {
        return MAX_WEEK_COUNT;
    }

    public int getMaxMonthCount() {
        return MAX_MONTH_COUNT;
    }

    public Double getPointRatio() {
        return POINT_RATIO;
    }

    public Double getTaxRatio() {
        return TAX_RATIO;
    }
}
