package com.jmt.enums;

public enum PayTypeEnum {
    POINTS_PAY(0, "积分支付"),
    BALANCE_PAY(1, "余额支付"),
    WECHAT_PAY(2, "微信支付"),
    ALIPAY_PAY(3, "支付宝支付");

    private int code;
    private String message;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    PayTypeEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }
    public static PayTypeEnum getByCode(int code) {
        for (PayTypeEnum type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的支付类型编码: " + code);
    }

    public static PayTypeEnum fromAccountType(String accountType) {
        if (accountType == null) {
            return null;
        }
        switch (accountType.toUpperCase()) {
            case "WECHAT":
                return WECHAT_PAY;
            case "ALIPAY":
                return ALIPAY_PAY;
            default:
                return null;
        }
    }
}
