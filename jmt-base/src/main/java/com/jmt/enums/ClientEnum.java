package com.jmt.enums;

/**
 * 审核状态枚举
 */
public enum ClientEnum {

    JMT_ADMIN("jmt-admin", "管理平台"),
    JMT_JHH("jmt-jhh", "聚合伙"),
    JMT_TG("jmt-tg", "投广"),
    JMT_BD("jmt-bd", "补电"),
    JMT_CM("jmt-cm", "餐谋");

    private final String code;
    private final String description;

    ClientEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据code获取枚举
     * @param code 状态码
     * @return 对应的枚举，找不到返回null
     */
    public static ClientEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ClientEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
