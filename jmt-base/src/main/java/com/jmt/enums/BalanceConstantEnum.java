package com.jmt.enums;

import java.math.BigDecimal;

/**
 * 余额相关常量枚举
 */
public enum BalanceConstantEnum {

    INSTANCE;

    // 最低提现余额
    private final BigDecimal MIN_BALANCE = new BigDecimal("10");

    // 最高提现余额
    private final BigDecimal MAX_BALANCE = new BigDecimal("50000");

    // 需财务审核的金额下限
    private final BigDecimal MIN_AUDIT_BALANCE = new BigDecimal("1000");

    // 每日提现上限
    private final int MAX_DAY_COUNT = 5;

    // 每周提现上限
    private final int MAX_WEEK_COUNT = 10;

    // 每月提现上限
    private final int MAX_MONTH_COUNT = 20;

    // 最小充值金额
    private final BigDecimal MIN_RECHARGE_AMOUNT = new BigDecimal("10");

    // 失败备注
    private final String FAIL_REMARK = "提现失败 退还余额";

    // getter方法
    public BigDecimal getMinBalance() {
        return MIN_BALANCE;
    }

    public BigDecimal getMaxBalance() {
        return MAX_BALANCE;
    }

    public BigDecimal getMinAuditBalance() {
        return MIN_AUDIT_BALANCE;
    }

    public int getMaxDayCount() {
        return MAX_DAY_COUNT;
    }

    public int getMaxWeekCount() {
        return MAX_WEEK_COUNT;
    }

    public int getMaxMonthCount() {
        return MAX_MONTH_COUNT;
    }

    public BigDecimal getMinRechargeAmount() {
        return MIN_RECHARGE_AMOUNT;
    }

    public String getFailRemark() {
        return FAIL_REMARK;
    }
}
