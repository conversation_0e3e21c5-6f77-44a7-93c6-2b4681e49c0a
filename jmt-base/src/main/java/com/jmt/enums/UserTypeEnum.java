package com.jmt.enums;

/**
 * 审核状态枚举
 */
public enum UserTypeEnum {

    OPERATOR(0, "运营商和其他用户"),
    INVESTOR(1, "设备收益人"),
    SALESMAN(2, "业务员");

    private final Integer code;
    private final String description;

    UserTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据code获取枚举
     * @param code 状态码
     * @return 对应的枚举，找不到返回null
     */
    public static UserTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (UserTypeEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
