package com.jmt.enums;

/**
 * 收益分润相关枚举类
 * 包含支付状态、分润类型、分润状态等常量定义
 */
public enum ProfitEnum {

    // ======================== 支付状态 ========================
    /** 支付状态：成功 */
    PAY_STATUS_SUCCESS(1, "支付成功"),

    // ======================== 分润类型 ========================
    /** 分润类型：比例分润（按百分比分配） */
    SHARE_TYPE_RATIO(0, "比例分润"),
    /** 分润类型：补贴分润（按固定金额分配） */
    SHARE_TYPE_SUBSIDY(1, "补贴分润"),

    // ======================== 预分润状态 ========================
    /** 预分润状态：待分润（初始状态） */
    PREPROFIT_STATUS_INIT(0, "待分润"),
    /** 预分润状态：已分润（处理完成） */
    PREPROFIT_STATUS_DONE(1, "已分润"),

    // ======================== 正式分润状态 ========================
    /** 正式分润状态：未入账（初始状态） */
    POSTPROFIT_STATUS_INIT(0, "未入账"),
    /** 正式分润状态：已入账（处理完成） */
    POSTPROFIT_STATUS_DONE(1, "已入账"),

    // ======================== 其他常量 ========================
    /** 金额转积分倍率（1元 = 10积分） */
    POINTS_RATE(10, "金额转积分倍率");

    /** 枚举值 */
    private final int code;
    /** 描述 */
    private final String desc;

    /**
     * 构造方法
     * @param code 枚举值
     * @param desc 描述
     */
    ProfitEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    // getter方法
    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
