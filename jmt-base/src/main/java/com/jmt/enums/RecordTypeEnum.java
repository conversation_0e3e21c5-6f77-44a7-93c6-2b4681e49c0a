package com.jmt.enums;

/**
 * 记录类型枚举
 */
public enum RecordTypeEnum {

    DELIVERY(0, "发货"),
    RETURN(1, "退货");

    private final Integer code;
    private final String description;

    RecordTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据code获取枚举
     * @param code 类型编码
     * @return 对应的枚举，找不到返回null
     */
    public static RecordTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (RecordTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 判断是否为发货类型
     * @return 是否为发货类型
     */
    public boolean isDelivery() {
        return this == DELIVERY;
    }

    /**
     * 判断是否为退货类型
     * @return 是否为退货类型
     */
    public boolean isReturn() {
        return this == RETURN;
    }
}
