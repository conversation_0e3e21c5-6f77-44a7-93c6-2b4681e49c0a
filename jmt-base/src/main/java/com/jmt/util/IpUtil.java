package com.jmt.util;

import cn.hutool.core.util.StrUtil;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

public class IpUtil {
	public static String getRemoteHost() {
		ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
		HttpServletRequest request = requestAttributes.getRequest();
		//client,proxy,proxy
		String ip = request.getHeader("X-Forwarded-For");
		if (StrUtil.isNotBlank(ip)) {
			List<String> xff = StrUtil.split(ip, ',');
			for (String s : xff) {
				if (StrUtil.isNotBlank(s) && !"unknown".equalsIgnoreCase(s)) {
					ip = s;
					break;
				} else
					ip = null;
			}
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip))
			ip = request.getHeader("X-Real-IP");

		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip))
			ip = request.getHeader("Proxy-Client-IP");

		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip))
			ip = request.getHeader("WL-Proxy-Client-IP");

		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip))
			ip = request.getRemoteAddr();

		if (ip == null) {
			return null;
		}
		ip = "0:0:0:0:0:0:0:1".equals(ip) ? "127.0.0.1" : ip;
		return ip.contains("%") ? ip.substring(0, ip.indexOf("%")) : ip;
	}
}
