package com.jmt.util;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * 验证码工具类
 * ChenHongJie
 */
public class CaptchaUtil {

    private static final char[] codeSequence = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9' };

    public  static  Map<String,Object> initCode() {
    	Map<String,Object> map= null;
        int width = 80;
        int codeCount = 4;
        int x1 = width / (codeCount + 1);
        int height = 30;
        int fontHeight = height - 2;
        int codeY = height - 4;
        BufferedImage buffImg = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_GRAY);
        Graphics2D g = buffImg.createGraphics();
        Random random = new Random();
        g.setColor(Color.WHITE);         
        g.fillRect(0, 0, width, height);
        Font font = new Font("Fixedsys", Font.PLAIN, fontHeight);
        g.setFont(font);
        g.setColor(Color.BLACK);         
        g.drawRect(0, 0, width - 1, height - 1);
        g.setColor(Color.BLACK);         
        for (int i = 0; i < 10; i++) {         
            int x = random.nextInt(width);         
            int y = random.nextInt(height);         
            int xl = random.nextInt(12);         
            int yl = random.nextInt(12);         
            g.drawLine(x, y, x + xl, y + yl);         
        }
        StringBuilder randomCode = new StringBuilder();
        int red, green, blue;
        for (int i = 0; i < codeCount; i++) {
            String strRand = String.valueOf(codeSequence[random.nextInt(codeSequence.length-1)]);
            red = random.nextInt(255);         
            green = random.nextInt(255);         
            blue = random.nextInt(255);
            g.setColor(new Color(red, green, blue));         
            g.drawString(strRand, (i + 1) * x1, codeY);
            randomCode.append(strRand);
            map= new HashMap<>();
            map.put("code", randomCode.toString());
            map.put("image", buffImg);
          
        }
        return map;
    }
}
