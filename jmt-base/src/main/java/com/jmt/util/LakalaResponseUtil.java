package com.jmt.util;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.jmt.exception.BusinessException;

public class LakalaResponseUtil {
    private static final Gson GSON = new Gson();

    public static JsonObject parse(String json) {
        return GSON.fromJson(json, JsonObject.class);
    }

    public static void checkSuccess(JsonObject response) {
        String code = response.get("code").getAsString();
        if (!"000000".equals(code)) {
            String msg = response.get("msg").getAsString();
            throw new BusinessException(msg);
        }
    }
    public static void checkBBSSuccess(JsonObject response) {
        String code = response.get("code").getAsString();
        if (!"BBS00000".equals(code)) {
            String msg = response.get("msg").getAsString();
            throw new BusinessException(msg);
        }
    }

    public static String getCounterUrl(JsonObject response) {
        return response.getAsJsonObject("resp_data").get("counter_url").getAsString();
    }

    /**
     * 获取支付宝二维码信息
     * @param response
     * @return
     */
    public static String getAliQRCodeUrl(JsonObject response) {
        return response.getAsJsonObject("resp_data").getAsJsonObject("acc_resp_fields").get("code").getAsString();
    }

    /**
     * 获取银联二维码信息
     * @param response
     * @return
     */
    public static String getUnionQRCodeUrl(JsonObject response) {
        return response.getAsJsonObject("resp_data").getAsJsonObject("acc_resp_fields").get("redirect_url").getAsString();
    }
}
