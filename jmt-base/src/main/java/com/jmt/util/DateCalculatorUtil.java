package com.jmt.util;

import java.util.Calendar;
import java.util.Date;

/**
 * 时间计算工具类：根据创建时间和时间范围计算结束时间
 */
public class DateCalculatorUtil {

    /**
     * 根据创建时间和selloutDay计算结束时间
     * @param createTime 创建时间（不能为空）
     * @param selloutDay 时间范围标识（0=10天，1=30天，2=60天，3=90天）
     * @return 计算后的结束时间（createTime + 对应天数）
     * @throws IllegalArgumentException 当参数不合法时抛出
     */
    public static Date calculateEndTime(Date createTime, Integer selloutDay) {
        // 校验参数合法性
        if (createTime == null) {
            throw new IllegalArgumentException("创建时间createTime不能为空");
        }
        if (selloutDay == null || selloutDay < 0 || selloutDay > 3) {
            throw new IllegalArgumentException("selloutDay必须为0、1、2、3中的一个");
        }

        // 根据selloutDay获取对应的天数
        int days = getDaysBySelloutDay(selloutDay);

        // 计算结束时间：createTime + days天
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(createTime);
        calendar.add(Calendar.DAY_OF_MONTH, days); // 增加指定天数

        return calendar.getTime();
    }

    /**
     * 将selloutDay转换为对应的天数
     * @param selloutDay 时间范围标识
     * @return 对应的天数（10/30/60/90）
     */
    private static int getDaysBySelloutDay(Integer selloutDay) {
        switch (selloutDay) {
            case 0:
                return 10;
            case 1:
                return 30;
            case 2:
                return 60;
            case 3:
                return 90;
            default:
                // 此处理论上不会触发，因为上层已做参数校验
                throw new IllegalArgumentException("不支持的selloutDay值：" + selloutDay);
        }
    }
}

