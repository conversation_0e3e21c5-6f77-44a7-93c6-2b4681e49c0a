package com.jmt.util;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Random;

public class ProfitUserNoUtil {
    // 日期格式
    private static final String DATE_FORMAT = "yyyyMMdd";
    // 随机数边界
    private static final int MAX_RANDOM_NUMBER = 1000000;
    // 随机数位数
    private static final String RANDOM_NUMBER_LENGTH = "%06d";
    //前缀
    private static final String PROFIT_USER_NO_PREFIX = "PA";
    /**
     * 传入前缀
     * @return
     */
    public static String generate(String prefix) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DATE_FORMAT));
        String random = String.format(RANDOM_NUMBER_LENGTH, new Random().nextInt(MAX_RANDOM_NUMBER));
        return prefix + timestamp + random;
    }
}
