package com.jmt.util;

import com.google.gson.*;
import com.google.gson.internal.LinkedTreeMap;
import com.google.gson.reflect.TypeToken;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonToken;
import com.google.gson.stream.JsonWriter;

import java.io.IOException;
import java.lang.reflect.Type;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class GsonUtil {

	private static final Gson gson = new GsonBuilder()
			.registerTypeAdapter(new TypeToken<Map<String, Object>>() {}.getType(), new MapTypeAdapter())
			//支持转换 java.util.Date, java.sql.Date, java.sql.Timestamp
			.setDateFormat("yyyy-MM-dd HH:mm:ss")
			//额外支持jdk8 time api
			.registerTypeAdapter(LocalDate.class, new LocalDateAdapter())
			.registerTypeAdapter(LocalDateTime.class, new LocalDateTimeAdapter())
			.serializeNulls()
			.create();

	/**
	 * 对象转json
	 *
	 */
	@Deprecated
	public static String toJson(Object obj, boolean format) {
//		GsonBuilder gsonBuilder = new GsonBuilder();
		/*
		 * 设置默认时间格式
		 */
//		gsonBuilder.setDateFormat(DATEFORMAT_default);
//		gsonBuilder.serializeNulls();
		/*
		 * 添加格式化设置
		 */
//		if (format) {
//			gsonBuilder.setPrettyPrinting();
//		}

//		Gson gson = gsonBuilder.create();

		return gson.toJson(obj);
	}

	public static String toJson(Object obj) {
		return gson.toJson(obj);
	}

	/**
	 * json字符串转list或者map
	 *
	 */
	public static <T> T fromJson(String json, TypeToken<T> typeToken) {
		return gson.fromJson(json, typeToken.getType());

	}

	public static Map<String, Object> fromJsonMap(String json) {
		if (json == null || json.isEmpty()) {
			return new HashMap<>();
		}
		return fromJson(json, new TypeToken<Map<String, Object>>() {
		});
	}

	public static List<Map<String, Object>> fromJsonList(String json) {
		if (json == null || json.isEmpty()) {
			return new ArrayList<>();
		}
		return fromJson(json, new TypeToken<List<Map<String, Object>>>() {
		});
	}

	public static List<String> fromJsonListString(String json) {
		if (json == null || json.isEmpty()) {
			return new ArrayList<>();
		}
		return fromJson(json, new TypeToken<List<String>>() {
		});
	}

	public static List<Integer> fromJsonListInteger(String json) {
		if (json == null || json.isEmpty()) {
			return new ArrayList<>();
		}
		return fromJson(json, new TypeToken<List<Integer>>() {
		});
	}

	private static class MapTypeAdapter extends TypeAdapter<Object> {

		@Override
		public Object read(JsonReader in) throws IOException {
			JsonToken token = in.peek();
			switch (token) {
			case BEGIN_ARRAY:
				List<Object> list = new ArrayList<>();
				in.beginArray();
				while (in.hasNext()) {
					list.add(read(in));
				}
				in.endArray();
				return list;

			case BEGIN_OBJECT:
				Map<String, Object> map = new LinkedTreeMap<>();
				in.beginObject();
				while (in.hasNext()) {
					map.put(in.nextName(), read(in));
				}
				in.endObject();
				return map;

			case STRING:
				return in.nextString();

			case NUMBER:
				/*
				 * 改写数字的处理逻辑，将数字值分为整型与浮点型。
				 */
				double dbNum = in.nextDouble();

				// 数字超过long的最大值，返回浮点类型
				if (dbNum > Long.MAX_VALUE) {
					return dbNum;
				}

				// 判断数字是否为整数值
				long lngNum = (long) dbNum;
				if (dbNum == lngNum) {
					return lngNum;
				} else {
					return dbNum;
				}

			case BOOLEAN:
				return in.nextBoolean();

			case NULL:
				in.nextNull();
				return null;

			default:
				throw new IllegalStateException();
			}
		}

		@Override
		public void write(JsonWriter out, Object value) {
			// 序列化无需实现
		}
	}

	private static class LocalDateAdapter implements JsonSerializer<LocalDate> {

		@Override
		public JsonElement serialize(LocalDate src, Type typeOfSrc, JsonSerializationContext context) {
			if (src == null) {
				return JsonNull.INSTANCE;
			}
			return new JsonPrimitive(src.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
		}
	}

	private static class LocalDateTimeAdapter implements JsonSerializer<LocalDateTime> {

		@Override
		public JsonElement serialize(LocalDateTime src, Type typeOfSrc, JsonSerializationContext context) {
			if (src == null) {
				return JsonNull.INSTANCE;
			}
			return new JsonPrimitive(src.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
		}
	}

	private static final JsonParser jsonParser = new JsonParser();

	/**
	 * 从JSON字符串中提取指定字段并转换为对象
	 * @param json JSON字符串
	 * @param fieldName 字段名
	 * @param typeToken 目标类型
	 * @return 转换后的对象
	 */
	public static <T> T extractFieldFromJson(String json, String fieldName, TypeToken<T> typeToken) {
		try {
			JsonElement jsonElement = jsonParser.parse(json);
			if (jsonElement.isJsonObject()) {
				JsonObject jsonObject = jsonElement.getAsJsonObject();
				JsonElement fieldElement = jsonObject.get(fieldName);
				if (fieldElement != null && !fieldElement.isJsonNull()) {
					return gson.fromJson(fieldElement, typeToken.getType());
				}
			}
			return null;
		} catch (JsonSyntaxException e) {
			throw new RuntimeException("JSON解析失败: " + json, e);
		}
	}

	/**
	 * 从JSON字符串中提取data字段并转换为对象
	 * @param json JSON字符串
	 * @param typeToken 目标类型
	 * @return 转换后的对象
	 */
	public static <T> T extractDataFieldFromJson(String json, TypeToken<T> typeToken) {
		return extractFieldFromJson(json, "data", typeToken);
	}

}
