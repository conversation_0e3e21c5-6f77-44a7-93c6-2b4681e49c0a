package com.jmt.util;

import org.apache.commons.lang.RandomStringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 收益项编码生成工具类
 * 功能：拼接「收益项代码 + 交易日期(yyyyMMddHHmm) + 6位随机数」
 */
public class SysCodeGenerateUtil {

    private static final String DATE_FORMAT = "yyyyMMddHHmm";
    private static final String DATE_FORMAT_PART = "yyyyMMdd";

    // 随机数位数
    private static final int RANDOM_NUMBER_SIX = 6;

    private static final int RANDOM_NUMBER_FIVE = 5;

    /**
     * 生成收益项编码
     * @param profitCode
     * @return
     */
    public static String generateProfitCode(String profitCode) {
        //  校验参数合法性
        validateParams(profitCode);
        String formattedDate = new SimpleDateFormat(DATE_FORMAT).format(new Date());
        String randomNum = RandomStringUtils.randomNumeric(RANDOM_NUMBER_SIX);
        return profitCode + formattedDate + randomNum;
    }

    /**
     * 生成用户编码
     * @param profitCode
     * @return
     */
    public static String generateUserCode(String profitCode) {
        //  校验参数合法性
        validateParams(profitCode);
        String formattedDate = new SimpleDateFormat(DATE_FORMAT_PART).format(new Date());
        String randomNum = RandomStringUtils.randomNumeric(RANDOM_NUMBER_FIVE);
        return profitCode +"-"+ formattedDate +"-"+ randomNum;
    }

    /**
     * 生成工单编号
     */
    public static String generateWorkOrderCode(String profitCode) {
        //  校验参数合法性
        validateParams(profitCode);
        String formattedDate = new SimpleDateFormat(DATE_FORMAT).format(new Date());
        String randomNum = RandomStringUtils.randomNumeric(RANDOM_NUMBER_FIVE);
        return profitCode + formattedDate + randomNum;
    }

    /**
     * 参数合法性校验
     *
     * @param profitCode      收益项代码
     * @throws IllegalArgumentException 当参数不合法时抛出
     */
    private static void validateParams(String profitCode) {
        // 校验收益项代码：非空、非空白字符串
        if (profitCode == null || profitCode.trim().isEmpty()) {
            throw new IllegalArgumentException("收益项代码不能为空或空白字符串");
        }

    }
}
