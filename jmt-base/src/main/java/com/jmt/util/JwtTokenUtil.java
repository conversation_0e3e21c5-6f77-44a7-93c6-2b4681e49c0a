package com.jmt.util;

import cn.hutool.core.convert.Convert;
import com.jmt.constant.JmtConstant;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import java.io.Serializable;
import java.util.*;

public class JwtTokenUtil implements Serializable {
    private static final long serialVersionUID = 1L;

    private static final Logger logger = LoggerFactory.getLogger(JwtTokenUtil.class);

    private static final String SECRET_KEY = "AE79CF896367D7235C170B569C799F33";

    public static final long TIME_OUT = 30 *24 * 60 * 60 * 1000L;

    /**
     * 用户登录成功后生成Jwt 使用Hs256算法 私匙使用用户密码
     * @param claims 登录成功的user对象
     */
    public static String createToken(Map<String, Object> claims,String loginName) {
        // 指定签名的时候使用的签名算法，也就是header那部分，jjwt已经将这部分内容封装好了。
        SignatureAlgorithm signatureAlgorithm = SignatureAlgorithm.HS256;
        // 生成JWT的时间
        long nowMillis = System.currentTimeMillis();
        Date now = new Date(nowMillis);
        JwtBuilder builder = Jwts.builder()
                .setClaims(claims)
                .setId(UUID.randomUUID().toString())
                .setIssuedAt(now)
                .setSubject(loginName)
                .signWith(signatureAlgorithm, SECRET_KEY);
        long expMillis = nowMillis + TIME_OUT;
        Date exp = new Date(expMillis);
        // 设置过期时间
        builder.setExpiration(exp);
        return DESEncoder.iEncrypt(builder.compact());
    }

    /**
     * Token的解密
     *
     * @param token  加密后的token
     */
    private static Map<String, Object> parseToken(String token) {
        // 得到DefaultJwtParser
        return Jwts.parser()
                // 设置签名的秘钥
                .setSigningKey(SECRET_KEY)
                // 设置需要解析的jwt
                .parseClaimsJws(DESEncoder.iDecrypt(token)).getBody();
    }

    public static Map<String, Object> getJwtUser(HttpServletRequest req) {
        String token = req.getHeader(JmtConstant.AUTHORIZATION);
        if (token != null) {
            token = token.replace(JmtConstant.BEARER, "").trim();
            return parseToken(token);
        }
        logger.error("token解析失败");
        return null;
    }

    public static boolean validateToken(String token, String loginName) {
        Map<String, Object> tokenMap = parseToken(token);
        String tokenLoginName = Convert.toStr(tokenMap.get(JmtConstant.LOGIN_NAME),"");
        return tokenLoginName.equals(loginName);
    }
}
