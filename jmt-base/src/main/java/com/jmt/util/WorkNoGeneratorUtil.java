package com.jmt.util;

import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;

public class WorkNoGeneratorUtil {
    // 工单前缀
    private static final String WORK_ORDER_PREFIX = "EQ";
    // 日期格式
    private static final String DATE_FORMAT = "yyyyMMdd";
    // 随机数位数
    private static final int RANDOM_NUMBER_LENGTH = 6;

    /**
     * 生成工单编号
     * @return 生成的工单编号
     */
    public static String generate() {
        return generate(WORK_ORDER_PREFIX);
    }

    /**
     * 生成工单编号（可自定义前缀）
     * @param prefix 工单前缀
     * @return 生成的工单编号
     */
    public static String generate(String prefix) {
        if (StringUtils.isBlank(prefix)) {
            prefix = WORK_ORDER_PREFIX;
        }

        String datePart = new SimpleDateFormat(DATE_FORMAT).format(new Date());
        String randomPart = RandomStringUtils.randomNumeric(RANDOM_NUMBER_LENGTH);

        return prefix + datePart + randomPart;
    }

    /**
     * 生成工单编号（可自定义前缀、日期格式和随机数位数）
     * @param prefix 工单前缀
     * @param dateFormat 日期格式
     * @param randomLength 随机数位数
     * @return 生成的工单编号
     */
    public static String generate(String prefix, String dateFormat, int randomLength) {
        if (StringUtils.isBlank(prefix)) {
            prefix = WORK_ORDER_PREFIX;
        }
        if (StringUtils.isBlank(dateFormat)) {
            dateFormat = DATE_FORMAT;
        }
        if (randomLength <= 0) {
            randomLength = RANDOM_NUMBER_LENGTH;
        }

        String datePart = new SimpleDateFormat(dateFormat).format(new Date());
        String randomPart = RandomStringUtils.randomNumeric(randomLength);

        return prefix + datePart + randomPart;
    }
}
