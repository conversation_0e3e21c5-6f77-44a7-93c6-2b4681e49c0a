package com.jmt.util;

import cn.hutool.core.bean.BeanUtil;
import com.jmt.model.auth.LoginUaaUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 获取当前登录用户信息工具类
 * <AUTHOR>
 */
public class LoginUserUtil {

    private final static Logger logger = LoggerFactory.getLogger(LoginUserUtil.class);

    public static LoginUaaUser get() {
        ServletRequestAttributes servletRequest = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletRequest request = servletRequest.getRequest();
        Map<String, Object> jwtUser = JwtTokenUtil.getJwtUser(request);
        if (jwtUser == null) {
            return null;
        }
        LoginUaaUser loginUaaUser = BeanUtil.toBean(jwtUser,LoginUaaUser.class);
        logger.info("获取当前登录用户信息:"+GsonUtil.toJson(loginUaaUser));
        return loginUaaUser;
    }
    public static LoginUaaUser get(HttpServletRequest request) {
        Map<String, Object> jwtUser = JwtTokenUtil.getJwtUser(request);
        if (jwtUser == null) {
            return null;
        }
        LoginUaaUser loginUaaUser = BeanUtil.toBean(jwtUser,LoginUaaUser.class);
        logger.info("获取当前登录用户信息:"+GsonUtil.toJson(loginUaaUser));
        return loginUaaUser;
    }
}
