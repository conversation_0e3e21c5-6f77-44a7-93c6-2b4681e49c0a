package com.jmt.util;

import cn.hutool.core.convert.Convert;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public class TreeUtil {

	public static List<Map<String, Object>> findChildrens(String parentId, List<Map<String, Object>> dataList) {
		List<Map<String, Object>> list = new ArrayList<>();
		Optional.ofNullable(dataList).orElse(new ArrayList<>())
				.stream()
				.filter(data -> parentId.equals(Convert.toStr(data.get("parentId"))))
				.forEach(data -> {
					List<Map<String, Object>> childrens = findChildrens(Convert.toStr(data.get("id")), dataList);
					list.addAll(childrens);
					list.add(data);
				});
		return list;
	}

	public static List<Map<String, Object>> findParents(String currId,String rootId, List<Map<String, Object>> dataList) {
		List<Map<String, Object>> list = new ArrayList<>();
		Optional.ofNullable(dataList).orElse(new ArrayList<>())
				.stream()
				.filter(data -> currId.equals(Convert.toStr(data.get("id"))))
				.forEach(data -> {
					if (!rootId.equals(Convert.toStr(data.get("id")))) {
						List<Map<String, Object>> parents = findParents(Convert.toStr(data.get("parentId")),rootId,dataList);
						list.addAll(parents);
					}
					list.add(data);
				});
		return list;
	}
}