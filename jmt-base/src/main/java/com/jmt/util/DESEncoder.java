package com.jmt.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;

/**
 * 
 * DES算法
 * 
 */

public class DESEncoder {
	final static Logger logger = LoggerFactory.getLogger(DESEncoder.class);
	/**
	 *  DES算法密钥
	 */
	private final static byte[] key = "8b763110eefd9a52d9fef1eba8609f88".getBytes();

	public static String iEncrypt(String content) {
		byte[] data;
		data = DESEncoder.encrypt(content.getBytes(StandardCharsets.UTF_8), key);
		return bytesToHexString(data);
	}

	public static String iDecrypt(String content) {
		byte[] data = str2Bcd(content);
		return new String(DESEncoder.decrypt(data, key), StandardCharsets.UTF_8);
	}

	/**
	 * 加密函数
	 * @param data 加密数据
	 * @param key 密钥
	 * @return 返回加密后的数据
	 */

	public static byte[] encrypt(byte[] data, byte[] key) {
		try {
			// DES算法要求有一个可信任的随机数源
			SecureRandom sr = new SecureRandom();
			// 从原始密钥数据创建DESKeySpec对象
			DESKeySpec dks = new DESKeySpec(key);
			// 创建一个密匙工厂，然后用它把DESKeySpec转换成
			// 一个SecretKey对象
			SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
			SecretKey secretKey = keyFactory.generateSecret(dks);
			// using DES in ECB mode
			Cipher cipher = Cipher.getInstance("DES/ECB/PKCS5Padding");
			// 用密匙初始化Cipher对象
			cipher.init(Cipher.ENCRYPT_MODE, secretKey, sr);
			// 执行加密操作
			return cipher.doFinal(data);
		} catch (Exception e) {
			logger.error("DES算法，加密出错:",e);
		}
		return null;
	}

	/**
	 * 解密函数
	 * @param data 解密数据
	 * @param key 密钥
	 * @return 返回解密后的数据
	 */
	public static byte[] decrypt(byte[] data, byte[] key) {
		try {
			// DES算法要求有一个可信任的随机数源
			SecureRandom sr = new SecureRandom();
			sr.setSeed(key);
			// byte rawKeyData[] = /* 用某种方法获取原始密匙数据 */;
			// 从原始密匙数据创建一个DESKeySpec对象
			DESKeySpec dks = new DESKeySpec(key);
			// 创建一个密匙工厂，然后用它把DESKeySpec对象转换成
			// 一个SecretKey对象
			SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
			SecretKey secretKey = keyFactory.generateSecret(dks);
			// using DES in ECB mode
			Cipher cipher = Cipher.getInstance("DES/ECB/PKCS5Padding");
			// 用密匙初始化Cipher对象
			cipher.init(Cipher.DECRYPT_MODE, secretKey, sr);
			// 正式执行解密操作
			return cipher.doFinal(data);
		} catch (Exception e) {
			logger.error("DES算法，解密出错:",e);
		}
		return null;
	}
	public static byte[] str2Bcd(String asc) {
		int len = asc.length();
		int mod = len % 2;
		if (mod != 0) {
			asc = "0" + asc;
			len = asc.length();
		}
		byte[] abt;
		if (len >= 2) {
			len = len / 2;
		}
		byte[] bbt = new byte[len];
		abt = asc.getBytes();
		int j, k;
		for (int p = 0; p < asc.length() / 2; p++) {
			if ((abt[2 * p] >= '0') && (abt[2 * p] <= '9')) {
				j = abt[2 * p] - '0';
			} else if ((abt[2 * p] >= 'a') && (abt[2 * p] <= 'z')) {
				j = abt[2 * p] - 'a' + 0x0a;
			} else {
				j = abt[2 * p] - 'A' + 0x0a;
			}
			if ((abt[2 * p + 1] >= '0') && (abt[2 * p + 1] <= '9')) {
				k = abt[2 * p + 1] - '0';
			} else if ((abt[2 * p + 1] >= 'a') && (abt[2 * p + 1] <= 'z')) {
				k = abt[2 * p + 1] - 'a' + 0x0a;
			} else {
				k = abt[2 * p + 1] - 'A' + 0x0a;
			}
			int a = (j << 4) + k;
			byte b = (byte) a;
			bbt[p] = b;
		}
		return bbt;
	}

	public static String bytesToHexString(byte[] bytes) {
		if (bytes == null) {
			return "";
		}
		StringBuilder buff = new StringBuilder();
		for (byte aByte : bytes) {
			if ((aByte & 0xff) < 16) {
				buff.append('0');
			}
			buff.append(Integer.toHexString(aByte & 0xff));
		}
		return buff.toString();
	}

	public static byte[] generateKey() {
		try {
			// DES算法要求有一个可信任的随机数源
			SecureRandom sr = new SecureRandom();
			// 生成一个DES算法的KeyGenerator对象
			KeyGenerator kg = KeyGenerator.getInstance("DES");
			kg.init(sr);
			// 生成密钥
			SecretKey secretKey = kg.generateKey();
			// 获取密钥数据
			return secretKey.getEncoded();

		} catch (Exception e) {
			logger.error("DES算法，生成密钥出错:",e);
		}
		return null;
	}
}