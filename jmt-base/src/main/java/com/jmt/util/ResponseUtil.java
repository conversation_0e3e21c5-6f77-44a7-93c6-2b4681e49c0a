package com.jmt.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.jmt.base.ResponseEntry;
import com.jmt.constant.JmtConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class ResponseUtil {

	private final static Logger logger = LoggerFactory.getLogger(ResponseUtil.class);

	private static <T> ResponseEntry parseResponse(String response, Class<T> clazz) {
		try {
			logger.info("接口响应数据:" + response);
			ResponseEntry responseEntry = JSONUtil.toBean(response, ResponseEntry.class);
			Object data = responseEntry.getData();
			if (responseEntry.getCode()==200 && ObjectUtil.isNotEmpty(data)) {
				String dataStr = JSONUtil.toJsonStr(data);
				if (JSONUtil.isJsonArray(dataStr)) {
					JSONArray dataArr = JSONUtil.parseArray(dataStr);
					responseEntry.setDataList(JSONUtil.toList(dataArr,clazz));
				} else if (JSONUtil.isJson(dataStr)){
					responseEntry.setData(JSONUtil.toBean(dataStr, clazz));
				}
			}
			return responseEntry;
		} catch (Exception e) {
			logger.error("接口响应数据:" + response,e);
			throw new RuntimeException("接口返回数据无法解析");
		}
	}
	public static Integer getCode(String response) {
		try {
			logger.info("接口响应数据:" + response);
			ResponseEntry responseEntry = JSONUtil.toBean(response, ResponseEntry.class);
			return responseEntry.getCode();
		} catch (Exception e) {
			logger.error("接口响应数据:" + response,e);
			throw new RuntimeException("接口返回数据无法解析");
		}
	}
	public static String getMsg(String response) {
		try {
			logger.info("接口响应数据:" + response);
			ResponseEntry responseEntry = JSONUtil.toBean(response, ResponseEntry.class);
			return responseEntry.getMsg();
		} catch (Exception e) {
			logger.error("接口响应数据:" + response,e);
			throw new RuntimeException("接口返回数据无法解析");
		}
	}

	public static <T> T getData(String response, Class<T> clazz) {
		if (StrUtil.isEmpty(response)) {
			return null;
		}
		ResponseEntry<T> responseEntry = parseResponse(response,clazz);
		if (responseEntry.getCode().equals(JmtConstant.SERVICE_RESPONSE_SUCCESS_CODE)) {
			return responseEntry.getData();
		} else {
			return null;
		}
	}

	public static <T> List<T> getDataList(String response, Class<T> clazz) {
		if (StrUtil.isEmpty(response)) {
			return new ArrayList<>();
		}
		ResponseEntry<T> responseEntry = parseResponse(response,clazz);
		if (responseEntry.getCode().equals(JmtConstant.SERVICE_RESPONSE_SUCCESS_CODE)) {
			List<T> dataList = responseEntry.getDataList();
			if (dataList != null) {
				return dataList;
			} else {
				return new ArrayList<>();
			}
		} else {
			return new ArrayList<>();
		}
	}
}
