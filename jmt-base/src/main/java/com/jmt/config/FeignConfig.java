package com.jmt.config;

import com.jmt.interceptor.TokenRequestInterceptor;
import feign.Logger;
import feign.Request;
import feign.RequestInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/***
 *  全局配置： 当使用@Configuration 会将配置作用所有的服务提供方
 *  局部配置： 1. 通过配置类：如果只想针对某一个服务进行配置， 就不要加@Configuration
 *           2. 通过配置文件
 * <AUTHOR>
 */
@Configuration
public class FeignConfig {

    @Bean
    public Logger.Level feignLoggerLevel(){
        return Logger.Level.FULL;
    }

    /**
     * 超时时间配置
    */
    @Bean
    public Request.Options options() {
        return new Request.Options(5000, TimeUnit.MILLISECONDS,15000,TimeUnit.MILLISECONDS,false);
    }

    @Bean
    public RequestInterceptor tokenRequestInterceptor() {
        return new TokenRequestInterceptor();
    }
}
