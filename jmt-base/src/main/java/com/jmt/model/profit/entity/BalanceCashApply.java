package com.jmt.model.profit.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 余额提现申请表
 */
@Data
public class BalanceCashApply {
    /**
     * 主键
     */
    private Long id;

    /**
     * 申请人，uaa_user的主键uaaId
     */
    private Long applyUser;
    /**
     * 申请人名称
     */
    private String  applyUserName;
    /**
     * 申请平台
     */
    private String  clientId;

    /**
     * 申请编号
     */
    private String applyNo;

    /**
     * 提现金额
     */
    private BigDecimal balance;
    /**
     * 申请人，uaa_user的主键uaaId
     */
    private Long bankId;
    /**
     * 开户银行
     */
    private String bankName;
    /**
     * 卡号
     */
    private String cardNo;
    /**
     * 开户姓名
     */
    private String accountName;

    /**
     * 审核状态
0-待审核
1-审核通过
2-审核不通过
3-已提现
     */
    private Integer auditStatus;

    /**
     * 备注
     */
    private String reason;

    /**
     * 审核人，uaa_user的主键uaaId
     */
    private Long auditUser;

    private String auditUserName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDelete;
}
