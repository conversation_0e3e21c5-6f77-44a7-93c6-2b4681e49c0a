package com.jmt.model.profit.dto;

import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;
@Data
public class BalanceCashApplyDto {

    /**
     * 支付密码
     */
    @NotNull(message = "支付密码不能为空")
    private String payPassword;
    /**
     * 提现金额
     */
    @NotNull(message = "提现金额不能为空")
    @DecimalMin(value = "0.01", message = "提现金额需大于等于0.01")
    @Digits(integer = 10, fraction = 2, message = "提现金额格式错误，整数部分最多10位，小数部分最多2位")
    private BigDecimal balance;

    /**
     * uaa_user_bankcard的主键ID
     */
    @NotNull(message = "银行卡主键ID不能为空")
    @Min(value = 1, message = "银行卡主键ID需大于0")
    private Long bankId;
}
