package com.jmt.model.profit.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LklRefundDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String merchantNo; // 商户号
    private String termNo; // 终端号
    private String outTradeNo; //商户交易流水号
    private String refundAmount; //退款金额 单位分，整数数字型字符
    private String refundReason; //退款原因
    private String originOutTradeNo; //原商户交易流水号
    private String originLogNo; //原对账单流水号
    private String originTradeNo;//原拉卡拉交易流水号
    private String ipAddress; //请求方IP地址
    private String gpsLocation; //维度,经度 商户终端的地理位置，存在必填 格式：纬度,经度，+表示北纬、东经，-表示南纬、 西经，精度最长支持小数点后9位。


}
