package com.jmt.model.profit.vo;

import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
* 余额收支记录表
*/
@Data
public class BalanceRecordVo implements Serializable {

    /**
    * 主键
    */
    private Long id;
    /**
    * 余额归属人，uaa_user的主键uaaId
    */
    private Long uaaId;
    /**
     * 变动余额
     */
    private BigDecimal balance = new BigDecimal("0.00");

    /**
     * 剩余余额
     */
    private BigDecimal remainBalance = new BigDecimal("0.00");
    /**
    * 记录类型
0-收入
1-支出
    */
    private Integer recordType;
    /**
    * 余额来源
recordType=0时填写
0-充值
1-积分兑现
    */
    private Integer balanceSource;
    /**
    * 填写充值订单号或积分兑现申请单号
    */
    private String sourceId;
    /**
    * 备注
    */
    private String remark;
    /**
    * 创建时间
    */
    private Date createTime;
    /**
    * 更新时间
    */
    private Date updateTime;
    /**
    * 是否删除 0-否 1-是
    */
    private Integer isDelete;



}
