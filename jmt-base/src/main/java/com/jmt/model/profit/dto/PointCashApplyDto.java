package com.jmt.model.profit.dto;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
public class PointCashApplyDto {

    /**
     * 支付密码
     */
    @NotNull(message = "支付密码不能为空")
    private String payPassword;

    /**
     * 申请积分数
     */
    @NotNull(message = "申请积分数不能为空")
    @Min(value = 100, message = "申请积分数需大于100")
    private Integer points;

    /**
     * 是否提供发票 0-否 代缴代扣 1-是 6%专用服务发票
     */
    @NotNull(message = "是否提供发票")
    private Integer invoiceProvide;

    /**
     * 发票文件
     */
    private String invoiceFile;
}
