package com.jmt.model.profit.entity;

import lombok.Data;

import java.util.Date;

/**
 * 收益分润表
 */
@Data
public class ProfitShareDivide {
    /**
     * 主键
     */
    private Long id;

    /**
     * 来自所有收益订单的orderNo
     */
    private String orderNo;

    /**
     * 运营商编号
     */
    private String operatorNo;

    /**
     * 设备编号
     */
    private String eqNo;

    /**
     * 收益分润项目Id
     */
    private Long projectId;

    /**
     * 收益分润项目名称
     */
    private String projectName;

    /**
     * 收益总积分
     */
    private Integer profitTotalPoints;
    /**
     * 分到的积分
     */
    private Integer sharePoints;
    /**
     * 获得分润积分的账号
     */
    private Long uaaId;

    /**
     * 分润状态
     0-未入账
     1-已入账
     */
    private Integer shareStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDelete;
}
