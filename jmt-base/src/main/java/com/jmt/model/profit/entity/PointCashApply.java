package com.jmt.model.profit.entity;

import lombok.Data;

import java.util.Date;
@Data
public class PointCashApply {
    /**
     * 主键
     */
    private Long id;

    /**
     * 申请平台
     */
    private String  clientId;

    /**
     * 申请人，uaa_user的主键uaald
     */
    private Long applyUser;

    /**
     * 申请人名称
     */
    private String applyUserName;

    /**
     * 申请编号
     */
    private String applyNo;

    /**
     * 申请积分数
     */
    private Integer points;

    /**
     * 是否提供发票0-否1-是
     */
    private Integer invoiceProvide;

    /**
     * 发票文件
     */
    private String invoiceFile;

    /**
     * 审核状态0-待审核1-审核通过2-审核不通过3-已兑现
     */
    private Integer auditStatus;

    /**
     * 备注
     */
    private String reason;
    /**
     * 审核人，uaa_user的主键uaaId
     */
    private Long auditUser;

    private String auditUserName;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除0-否1-是
     */
    private Integer isDelete;
}
