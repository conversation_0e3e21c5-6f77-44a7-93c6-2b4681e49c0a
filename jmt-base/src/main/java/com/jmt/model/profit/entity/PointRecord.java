package com.jmt.model.profit.entity;

import java.io.Serializable;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
* 积分收支记录表
* @TableName profit_points_record
*/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PointRecord implements Serializable {

    /**
    * 主键
    */
    private Long id;
    /**
    * 积分归属人，uaa_user的主键uaaId
    */
    private Long uaaId;
    /**
    * 变动积分数
    */
    private Integer points = 0;

    /**
     * 剩余积分数
     */
    private Integer remainPoints = 0;

    /**
    * 记录类型
0-收入
1-支出
    */

    private Integer recordType;
    /**
    * recordType=0时填写
0-分润
1-任务
    */
    private Integer pointSource;
    /**
    * 填写分润id或任务id
    */
    private String sourceId;
    /**
    * 备注
    */
    private String remark;
    /**
    * 创建时间
    */
    private Date createTime;
    /**
    * 更新时间
    */
    private Date updateTime;
    /**
    * 是否删除 0-否 1-是
    */
    private Integer isDelete;



}
