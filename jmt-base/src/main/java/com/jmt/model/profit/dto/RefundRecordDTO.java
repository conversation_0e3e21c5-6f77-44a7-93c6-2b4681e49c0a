package com.jmt.model.profit.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
@Data
public class RefundRecordDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long id;

    private String seqNo;

    private String orderNo;

    private Long rechargeUser;

    private Integer rechargeType;

    private BigDecimal rechargeAmount;

    private Integer rechargeStatus;

    private String reason;
}

