package com.jmt.model.profit.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class LklPayNotifyDTO implements Serializable {
    private Long uaaId;

    @JsonProperty("out_trade_no")
    private String orderNo;

    @JsonProperty("trade_no")
    private String tradeNo;

    @JsonProperty("account_type")
    private String accountType;

    @JsonProperty("total_amount")
    private String totalAmount;

    @JsonProperty("trade_status")
    private String tradeStatus;

    // 其他需要的字段
    @JsonProperty("log_no")
    private String logNo;

    @JsonProperty("acc_trade_no")
    private String accTradeNo;

    @JsonProperty("trade_time")
    private String tradeTime;

    @JsonProperty("trade_state")
    private String tradeState;
    @JsonProperty("payer_amount")
    private String payerAmount;
    @JsonProperty("acc_settle_amount")
    private String accSettleAmount;
    @JsonProperty("user_id1")
    private String userId1;
    @JsonProperty("user_id2")
    private String userId2;
    @JsonProperty("notify_url")
    private String notifyUrl;
    @JsonProperty("card_type")
    private String cardType;
}
