package com.jmt.model.profit.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LklCounterOrderDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String merchantNo; // 商户号

    private String outOrderNo; // 商户订单号

    private String channelId; // 渠道号

}
