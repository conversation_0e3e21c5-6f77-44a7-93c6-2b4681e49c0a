package com.jmt.model.profit.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LklAlipayDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    private String merchantNo; //商户号

    private String termNo; //终端号

    private String outTradeNo; //商户交易流水号

    private String accountType; //钱包类型
    private String transType; //接入方式

    private String totalAmount; // 金额 单位：分

    private String notifyUrl; //商户通知地址

    private String remark; //备注

    private String ipAddress; //请求方地址
    private String baseStation; //基站信息

    private String gpsLocation; //维度,经度 商户终端的地理位置，存在必填 格式：纬度,经度，+表示北纬、东经，-表示南纬、 西经，精度最长支持小数点后9位。

    private AlipayParams alipayParams;



}
