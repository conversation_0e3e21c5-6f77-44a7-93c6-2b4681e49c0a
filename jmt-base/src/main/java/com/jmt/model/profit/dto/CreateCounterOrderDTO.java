package com.jmt.model.profit.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreateCounterOrderDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String orderNo;

    private String merchantCode;

    private Long amount;

    private String orderTime;

    private String orderInfo;


    private String notifyUrl;


    private String payType; // ALIPAY, WECHAT等

    private Long  goodsAmt; // 商品单价
    private Integer goodsNum; // 商品数量
    private String goodsPricingUnit;// 计价单位: 1-箱 2-件 3-瓶 4-个
    private String goodsName;
    private String platformType;// 交易电商平台类型 1-境内平台 2-境外平台
    private String platformName;// 交易电商平台名称
    private String goodsType;


    private String shopName; // 可选 网点名称
    private String counterRemark; // 可选 收银台备注
    private String busiTypeParam; // 可选
}
