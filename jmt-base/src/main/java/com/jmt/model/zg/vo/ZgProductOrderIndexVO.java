package com.jmt.model.zg.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ZgProductOrderIndexVO {

    private Long id;

    private String orderNo;

    private String orderName;

    private Long buyUserId;
    //买家
    private String loginName;

    /**
     * 支付金额 单位元
     */
    private BigDecimal payAmount;
    /**
     * 订单状态
     0-未支付
     1-支付成功
     2-支付失败
     3-取消支付
     */
    private Integer orderStatus;
    /**
     * 配送员编号
     */
    private String driverNo;

    private Date createTime;

    private Long addressId;
    //配送地址
    private String province;
    private String city;
    private String detailAddress;

}
