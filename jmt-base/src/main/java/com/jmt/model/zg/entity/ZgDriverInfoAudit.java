package com.jmt.model.zg.entity;

import lombok.Data;

import java.util.Date;

/**
 * 筷圣直供配送员信息审核记录表
 * @TableName zg_driver_info_audit
 */
@Data
public class ZgDriverInfoAudit {
    /**
     * 主键
     */
    private Long id;

    /**
     * 审核人，uaa_user的主键uaaId
     */
    private Long auditUser;

    /**
     * 配送员编号
     */
    private String driverNo;

    /**
     * 审核状态<br/>
     * 0-待审核<br/>1-同意<br/>2-驳回<br/>3-冻结
     */
    private Integer auditStatus;

    /**
     * 备注
     */
    private String reason;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDelete;


}