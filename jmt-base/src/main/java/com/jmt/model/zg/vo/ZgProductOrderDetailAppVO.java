package com.jmt.model.zg.vo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ZgProductOrderDetailAppVO {
    private Long id;

    private String orderNo;
    private String supplierNo;

    private String productNo;

    private String productName;
    private BigDecimal originalPrice;

    private BigDecimal currentPrice;

    private Integer quantity;

    private Long categoryId;

    private String categoryName;

    private String productMainPicture;

    private String productBrand;

    private String productSize;

    private String productBarCode;

    private Integer productNature;

}
