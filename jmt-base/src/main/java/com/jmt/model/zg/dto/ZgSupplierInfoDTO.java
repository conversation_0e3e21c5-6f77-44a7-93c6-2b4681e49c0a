package com.jmt.model.zg.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ZgSupplierInfoDTO {
    private String supplierName;
    private String creditCode;
    private String legalPerson;
    private String bankName;
    private String bankCardNo;

    private Integer categoryId;
    private Integer nature;
    private String linkman;
    private String telPhone;

    private String country;
    private String province;
    private String city;
    private String area;
    private String address;
    private Integer deliveryModel;
    private BigDecimal minDeliveryAmount;

    private String supplierPermit;
    private String supplierLicense;

}
