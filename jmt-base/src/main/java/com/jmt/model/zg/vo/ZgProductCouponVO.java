package com.jmt.model.zg.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
@Data
public class ZgProductCouponVO { /**
 *
 */
private Long id;
    /**
     * 优惠券编号
     */
    private String couponNo;
    /**
     * 优惠券名称
     */
    private String couponName;
    /**
     * 商品品类zg_product_category的id
     */
    private Long categoryId;

    private String categoryName;
    /**
     * 有效期
     */
    private Date validityPeriod;
    /**
     * 抵扣金额
     */
    private BigDecimal couponAmount;
    /**
     * 描述
     */
    private String description;

    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
}
