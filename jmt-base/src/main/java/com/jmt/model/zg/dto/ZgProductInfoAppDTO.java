package com.jmt.model.zg.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ZgProductInfoAppDTO {
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 商品条码
     */
    private String productBarCode;
    /**
     * 商品品牌
     */
    private String productBrand;
    /**
     * 商品货号
     */
    private String productArticleNo;
    /**
     * 商品规格
     */
    private String productSize;
    /**
     * 单位
     */
    private String productUnit;
    /**
     * 商品性质
     0-其他
     1-一级
     2-二级
     3-三级
     4-统货
     5-临期
     6-库存

     */
    private Integer productNature;
    /**
     * 服务点数（%）
     */
    private Double servicePoint;
    /**
     * 促销基金点数（%）
     */
    private Double fundPoint;
    /**
     * 原价
     */
    private BigDecimal originalPrice;
    /**
     * 现价
     */
    private BigDecimal currentPrice;
    /**
     * 商品主图片
     */
    private String productMainPicture;
    /**
     * 商品附图片
     */
    private String productSubPicture;
    /**
     * 商品详细图
     */
    private String productDetailPicture;
    /**
     * 许可证图片
     */
    private String licensePicture;
    /**
     * 质检报告
     */
    private String qualityReport;

    private String busNo;

    private String shopNo;


    private Long categoryId;

    private Integer stock;

}
