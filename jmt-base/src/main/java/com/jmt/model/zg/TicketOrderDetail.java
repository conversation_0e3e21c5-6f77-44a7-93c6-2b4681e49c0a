package com.jmt.model.zg;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
@Data
public class TicketOrderDetail {
    private String orderNo; // 订单编号（关联字段）
    private String orderName; // 订单名称
    private Long buyUserId; // 购买用户ID
    private Integer payType; // 支付方式
    private BigDecimal payAmount; // 总支付金额
    private Integer orderStatus; // 订单状态
    private Date createTime; // 订单创建时间

    // 订单明细表字段
    private String supplierNo; // 供应商编号
    private String ticketNo; // 囤货券编号
    private String ticketName; // 囤货券名称
    private BigDecimal originalPrice; // 原价
    private BigDecimal currentPrice; // 现价
    private BigDecimal prepayAmount; // 预付金额
    private BigDecimal postpayAmount; // 尾款金额
    private Integer quantity; // 购买数量
    private Date detailCreateTime; // 明细创建时间
}
