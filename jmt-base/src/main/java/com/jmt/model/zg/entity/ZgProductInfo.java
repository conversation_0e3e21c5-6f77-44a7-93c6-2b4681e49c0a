package com.jmt.model.zg.entity;

import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
* 筷圣直供商品信息表
* @TableName zg_product_info
*/
@Data
public class ZgProductInfo implements Serializable {

    /**
    * 
    */
    private Long id;
    /**
    * 供应商编号
    */
    private String supplierNo;
    /**
    * 供应商名称
    */
    private String supplierName;
    /**
    * 商品编号
    */
    private String productNo;
    /**
    * 商品名称
    */
    private String productName;
    /**
    * 商品品类 zg_product_category的id
    */
    private Long categoryId;
    /**
    * 许可证图片
    */
    private String licensePicture;
    /**
    * 质检报告
    */
    private String qualityReport;
    /**
    * 原价
    */
    private BigDecimal originalPrice;
    /**
    * 现价
    */
    private BigDecimal currentPrice;
    /**
    * 商品主图片
    */
    private String productMainPicture;
    /**
    * 商品附图片
    */
    private String productSubPicture;
    /**
    * 商品详细图
    */
    private String productDetailPicture;
    /**
    * 商品条码
    */
    private String productBarCode;
    /**
    * 商品品牌
    */
    private String productBrand;
    /**
    * 商品规格
    */
    private String productSize;
    /**
    * 商品货号
    */
    private String productArticleNo;
    /**
    * 单位
    */
    private String productUnit;
    /**
    * 商品性质
0-其他
1-一级
2-二级
3-三级
4-统货
5-临期
6-库存

    */
    private Integer productNature;
    /**
    * 服务点数（%）
    */
    private Double servicePoint;
    /**
    * 促销基金点数（%）
    */
    private Double fundPoint;
    /**
    * 库存数量
    */
    private Integer stock;
    /**
    * 限购数量
    */
    private Integer buyLimit;
    /**
    * 0-待审核
1-审核通过
2-驳回
    */
    private Integer auditStatus;
    /**
    * 备注说明
    */
    private String remark;
    /**
    * 审核意见
    */
    private String reason;
    /**
    * 更新时间
    */
    private Date createTime;
    /**
    * 更新时间
    */
    private Date updateTime;
    /**
    * 是否删除  0-否 1-是
    */
    private Integer isDelete;


}
