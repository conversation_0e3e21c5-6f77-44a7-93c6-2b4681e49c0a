package com.jmt.model.zg.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ZgProductOrderDetailVO {
    private Long id;
    /**
     * 订单编号
     */
    private String orderNo;
    private String orderName;
    private Long buyUserId;
    private Integer payType;
    private String driverNo;
    private Integer orderStatus;
    private Long addressId;

    private String province;
    private String city;
    private String address;
    private BigDecimal payAmount;

    private List<ProductOrderDetailVO> detailVOList;

}
