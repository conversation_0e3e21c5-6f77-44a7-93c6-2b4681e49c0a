package com.jmt.model.zg;

import lombok.Data;

import java.util.Date;

/**
 * 筷圣直供用户表
 * @TableName zg_user
 */
@Data
public class ZgUser {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 统一账号ID
     */
    private Long uaaId;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 头像URL
     */
    private String headImg;

    /**
     * 性别<br/>
     * 0-男<br/>
     * 1-女
     */
    private Integer sex;

    /**
     * 手机号码
     */
    private String telPhone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 国家
     */
    private String country;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 身份类型<br/>
     * 0-游客<br/>
     * 1-供应商<br/>
     * 2-配送员
     */
    private Integer roleType;

    /**
     * 是否首次登录<br/>
     * 0-否<br/>
     * 1-是
     */
    private Integer isFirstLogin;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除<br/>
     * 0-否<br/>
     * 1-是
     */
    private Integer isDelete;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ZgUser zgUser = (ZgUser) o;
        return java.util.Objects.equals(id, zgUser.id) &&
                java.util.Objects.equals(uaaId, zgUser.uaaId) &&
                java.util.Objects.equals(userName, zgUser.userName) &&
                java.util.Objects.equals(nickName, zgUser.nickName) &&
                java.util.Objects.equals(headImg, zgUser.headImg) &&
                java.util.Objects.equals(sex, zgUser.sex) &&
                java.util.Objects.equals(telPhone, zgUser.telPhone) &&
                java.util.Objects.equals(email, zgUser.email) &&
                java.util.Objects.equals(country, zgUser.country) &&
                java.util.Objects.equals(province, zgUser.province) &&
                java.util.Objects.equals(city, zgUser.city) &&
                java.util.Objects.equals(address, zgUser.address) &&
                java.util.Objects.equals(roleType, zgUser.roleType) &&
                java.util.Objects.equals(isFirstLogin, zgUser.isFirstLogin) &&
                java.util.Objects.equals(createTime, zgUser.createTime) &&
                java.util.Objects.equals(updateTime, zgUser.updateTime) &&
                java.util.Objects.equals(isDelete, zgUser.isDelete);
    }

    @Override
    public int hashCode() {
        return java.util.Objects.hash(
                id, uaaId, userName, nickName, headImg, sex,
                telPhone, email, country, province, city,
                address, roleType, isFirstLogin, createTime,
                updateTime, isDelete
        );
    }

    @Override
    public String toString() {
        return "ZgUser{" +
                "id=" + id +
                ", uaaId=" + uaaId +
                ", userName='" + userName + '\'' +
                ", nickName='" + nickName + '\'' +
                ", headImg='" + headImg + '\'' +
                ", sex=" + sex +
                ", telPhone='" + telPhone + '\'' +
                ", email='" + email + '\'' +
                ", country='" + country + '\'' +
                ", province='" + province + '\'' +
                ", city='" + city + '\'' +
                ", address='" + address + '\'' +
                ", roleType=" + roleType +
                ", isFirstLogin=" + isFirstLogin +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", isDelete=" + isDelete +
                '}';
    }
}