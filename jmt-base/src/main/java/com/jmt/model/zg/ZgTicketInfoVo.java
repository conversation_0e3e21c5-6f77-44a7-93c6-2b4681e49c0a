package com.jmt.model.zg;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 筷圣直供囤货券信息表
 * @TableName zg_ticket_info
 */
@Data
public class ZgTicketInfoVo {

    /**
     * 供应商编号
     */
    private String supplierNo;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 囤货券编号
     */
    private String ticketNo;

    /**
     * 囤货券名称
     */
    private String ticketName;

    /**
     * 原价
     */
    private BigDecimal originalPrice;

    /**
     * 现价
     */
    private BigDecimal currentPrice;

    /**
     * 预付金额
     */
    private BigDecimal prepayAmount;

    /**
     * 尾款金额
     */
    private BigDecimal postpayAmount;

    /**
     * 有效期开始时间
     */
    private Date effectiveStartTime;

    /**
     * 有效期结束时间
     */
    private Date effectiveEndTime;

    /**
     * 产品图片
     */
    private String productPicture;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品规格
     */
    private String productSize;

    /**
     * 产品数量
     */
    private Integer productNum;

    /**
     * 库存数量
     */
    private Integer stock;

    /**
     * 限购数量
     */
    private Integer buyLimit;

    /**
     * 审核状态<br/>
     * 0-待审核<br/>1-同意<br/>2-驳回
     */
    private Integer auditStatus;

    /**
     * 备注说明
     */
    private String remark;

    /**
     * 审核意见
     */
    private String reason;

}