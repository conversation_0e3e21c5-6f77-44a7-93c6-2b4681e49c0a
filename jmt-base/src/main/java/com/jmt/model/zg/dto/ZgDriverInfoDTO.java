package com.jmt.model.zg.dto;

import lombok.Data;

@Data
public class ZgDriverInfoDTO {


    /**
     * 配送员姓名
     */
    private String driverName;

    /**
     * 身份证号
     */
    private String idNo;

    /**
     * 手机号
     */
    private String telPhone;

    /**
     * 区县
     */
    private String area;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 维度
     */
    private String latitude;

    /**
     * 配送范围
     */
    private String areaScope;

    /**
     * 车辆正面照片
     */
    private String carPhotoFront;

    private String carPhotoBack;

    /**
     * 车牌号
     */
    private String carNumber;

    /**
     * 车辆最大载重，单位KG
     */
    private Integer maxLoad;

    /**
     * 星级 1-5 标识1-5个星
     */
    private Integer driverStar;

    /**
     * 备注说明
     */
    private String remark;


    /**
     * 是否已实名认证 0-否 1-是
     */
    private Integer isRealAuth;
}
