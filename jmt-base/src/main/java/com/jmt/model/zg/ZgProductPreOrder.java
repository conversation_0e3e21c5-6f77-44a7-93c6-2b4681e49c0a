package com.jmt.model.zg;

import lombok.Data;

import java.util.Date;

/**
 * 筷圣直供商品预订订单表
 * @TableName zg_product_pre_order
 */
@Data
public class ZgProductPreOrder {
    /**
     * 主键
     */
    private Long id;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 订单名称
     */
    private String orderName;

    /**
     * 供应商编号
     */
    private String supplierNo;

    /**
     * 囤货券编号
     */
    private String ticketNo;

    /**
     * 囤货券名称
     */
    private String ticketName;

    /**
     * 商品总量
     */
    private Integer productTotal;

    /**
     * 总金额
     */
    private Integer amountTotal;

    /**
     * 订单总数
     */
    private Integer orderCount;

    /**
     * 订单状态<br/>
     * 0-待确认<br/>1-已确认<br/>2-已提货
     */
    private Integer orderStatus;

    /**
     * 拒绝原因
     */
    private String reason;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除（0-否，1-是）
     */
    private Integer isDelete;


    @Override
    public boolean equals(Object that) {
        if (this == that) return true;
        if (that == null || getClass() != that.getClass()) return false;
        ZgProductPreOrder other = (ZgProductPreOrder) that;
        return (getId() == null ? other.getId() == null : getId().equals(other.getId()))
                && (getOrderNo() == null ? other.getOrderNo() == null : getOrderNo().equals(other.getOrderNo()))
                && (getOrderName() == null ? other.getOrderName() == null : getOrderName().equals(other.getOrderName()))
                && (getSupplierNo() == null ? other.getSupplierNo() == null : getSupplierNo().equals(other.getSupplierNo()))
                && (getTicketNo() == null ? other.getTicketNo() == null : getTicketNo().equals(other.getTicketNo()))
                && (getTicketName() == null ? other.getTicketName() == null : getTicketName().equals(other.getTicketName()))
                && (getProductTotal() == null ? other.getProductTotal() == null : getProductTotal().equals(other.getProductTotal()))
                && (getAmountTotal() == null ? other.getAmountTotal() == null : getAmountTotal().equals(other.getAmountTotal()))
                && (getOrderCount() == null ? other.getOrderCount() == null : getOrderCount().equals(other.getOrderCount()))
                && (getOrderStatus() == null ? other.getOrderStatus() == null : getOrderStatus().equals(other.getOrderStatus()))
                && (getReason() == null ? other.getReason() == null : getReason().equals(other.getReason()))
                && (getCreateTime() == null ? other.getCreateTime() == null : getCreateTime().equals(other.getCreateTime()))
                && (getUpdateTime() == null ? other.getUpdateTime() == null : getUpdateTime().equals(other.getUpdateTime()))
                && (getIsDelete() == null ? other.getIsDelete() == null : getIsDelete().equals(other.getIsDelete()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getOrderNo() == null) ? 0 : getOrderNo().hashCode());
        result = prime * result + ((getOrderName() == null) ? 0 : getOrderName().hashCode());
        result = prime * result + ((getSupplierNo() == null) ? 0 : getSupplierNo().hashCode());
        result = prime * result + ((getTicketNo() == null) ? 0 : getTicketNo().hashCode());
        result = prime * result + ((getTicketName() == null) ? 0 : getTicketName().hashCode());
        result = prime * result + ((getProductTotal() == null) ? 0 : getProductTotal().hashCode());
        result = prime * result + ((getAmountTotal() == null) ? 0 : getAmountTotal().hashCode());
        result = prime * result + ((getOrderCount() == null) ? 0 : getOrderCount().hashCode());
        result = prime * result + ((getOrderStatus() == null) ? 0 : getOrderStatus().hashCode());
        result = prime * result + ((getReason() == null) ? 0 : getReason().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getIsDelete() == null) ? 0 : getIsDelete().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName()).append(" [");
        sb.append("Hash=").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", orderNo=").append(orderNo);
        sb.append(", orderName=").append(orderName);
        sb.append(", supplierNo=").append(supplierNo);
        sb.append(", ticketNo=").append(ticketNo);
        sb.append(", ticketName=").append(ticketName);
        sb.append(", productTotal=").append(productTotal);
        sb.append(", amountTotal=").append(amountTotal);
        sb.append(", orderCount=").append(orderCount);
        sb.append(", orderStatus=").append(orderStatus);
        sb.append(", reason=").append(reason);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", isDelete=").append(isDelete);
        sb.append("]");
        return sb.toString();
    }
}