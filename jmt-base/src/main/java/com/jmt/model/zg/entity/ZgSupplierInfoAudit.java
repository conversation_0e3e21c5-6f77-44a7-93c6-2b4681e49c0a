package com.jmt.model.zg.entity;

import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
* 筷圣直供供应商信息审核记录表
* @TableName zg_supplier_info_audit
*/
@Data
public class ZgSupplierInfoAudit implements Serializable {

    /**
    * 主键
    */
    private Long id;
    /**
    * 审核人，uaa_user的主键uaaId
    */
    private Long auditUser;
    /**
    * 供应商编号
    */
    private String supplierNo;
    /**
    * 审核状态
0-待审核
1-同意
2-驳回
3-冻结
    */
    private Integer auditStatus;
    /**
    * 备注
    */
    private String reason;
    /**
    * 创建时间
    */
    private Date createTime;
    /**
    * 更新时间
    */
    private Date updateTime;
    /**
    * 是否删除 0-否 1-是
    */
    private Integer isDelete;



}
