package com.jmt.model.zg.entity;

import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
* 筷圣直供我的券包表
* @TableName zg_coupon_pakage
*/
@Data
public class ZgCouponPackage implements Serializable {

    /**
    * 
    */
    private Long id;
    /**
    * 券的所有人统一账号uaaId
    */
    private Long buyUserId;
    /**
    * 券编号
    */
    private String couponNo;
    /**
    * 券的状态
0-未使用 
1-已使用

    */
    private Integer tStatus;
    /**
    * 更新时间
    */
    private Date createTime;
    /**
    * 更新时间
    */
    private Date updateTime;
    /**
    * 是否删除  0-否 1-是
    */
    private Integer isDelete;


}
