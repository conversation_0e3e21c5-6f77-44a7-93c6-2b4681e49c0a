package com.jmt.model.zg;

import lombok.Data;

import java.util.Date;

/**
 * 筷圣直供商品预订订单明细表
 * @TableName zg_product_pre_order_detail
 */
@Data
public class ZgProductPreOrderDetail {
    /**
     * 主键
     */
    private Long id;

    /**
     * 预订订单编号（关联 zg_product_pre_order 的 orderNo）
     */
    private String productPreOrderNo;

    /**
     * 商品订单编号（关联 zg_product_order 的 orderNo）
     */
    private String productOrderNo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除（0-否，1-是）
     */
    private Integer isDelete;


    @Override
    public boolean equals(Object that) {
        if (this == that) return true;
        if (that == null || getClass() != that.getClass()) return false;
        ZgProductPreOrderDetail other = (ZgProductPreOrderDetail) that;
        return (getId() == null ? other.getId() == null : getId().equals(other.getId()))
                && (getProductPreOrderNo() == null ? other.getProductPreOrderNo() == null : getProductPreOrderNo().equals(other.getProductPreOrderNo()))
                && (getProductOrderNo() == null ? other.getProductOrderNo() == null : getProductOrderNo().equals(other.getProductOrderNo()))
                && (getCreateTime() == null ? other.getCreateTime() == null : getCreateTime().equals(other.getCreateTime()))
                && (getUpdateTime() == null ? other.getUpdateTime() == null : getUpdateTime().equals(other.getUpdateTime()))
                && (getIsDelete() == null ? other.getIsDelete() == null : getIsDelete().equals(other.getIsDelete()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getProductPreOrderNo() == null) ? 0 : getProductPreOrderNo().hashCode());
        result = prime * result + ((getProductOrderNo() == null) ? 0 : getProductOrderNo().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getIsDelete() == null) ? 0 : getIsDelete().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName()).append(" [");
        sb.append("Hash=").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", productPreOrderNo=").append(productPreOrderNo);
        sb.append(", productOrderNo=").append(productOrderNo);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", isDelete=").append(isDelete);
        sb.append("]");
        return sb.toString();
    }
}