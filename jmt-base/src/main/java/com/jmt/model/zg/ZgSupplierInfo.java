package com.jmt.model.zg;

import lombok.Data;

import java.util.Date;

/**
 * 筷圣直供供应商信息表
 * @TableName zg_supplier_info
 */
@Data
public class ZgSupplierInfo {
    /**
     * 主键
     */
    private Long id;

    /**
     * zg_user的id
     */
    private Long userId;

    /**
     * 供应商编号
     */
    private String supplierNo;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 联系人
     */
    private String linkman;

    /**
     * 联系人手机号
     */
    private String TelPhone;

    /**
     * 国家
     */
    private String country;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 维度
     */
    private String latitude;

    /**
     * 供应商照片
     */
    private String supplierPhoto;

    /**
     * 供应商营业执照
     */
    private String supplierLicense;

    /**
     * 经营类别
     */
    private Integer categoryId;

    /**
     * 星级（1-5，标识1-5个星）
     */
    private Integer supplierStar;

    /**
     * 备注说明
     */
    private String remark;

    /**
     * 审核状态<br/>
     * 0-待审核<br/>1-同意<br/>2-驳回<br/>3-冻结
     */
    private Integer auditStatus;

    /**
     * 审核意见
     */
    private String reason;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除（0-否，1-是）
     */
    private Integer isDelete;


    @Override
    public boolean equals(Object that) {
        if (this == that) return true;
        if (that == null || getClass() != that.getClass()) return false;
        ZgSupplierInfo other = (ZgSupplierInfo) that;
        return (getId() == null ? other.getId() == null : getId().equals(other.getId()))
                && (getUserId() == null ? other.getUserId() == null : getUserId().equals(other.getUserId()))
                && (getSupplierNo() == null ? other.getSupplierNo() == null : getSupplierNo().equals(other.getSupplierNo()))
                && (getSupplierName() == null ? other.getSupplierName() == null : getSupplierName().equals(other.getSupplierName()))
                && (getLinkman() == null ? other.getLinkman() == null : getLinkman().equals(other.getLinkman()))
                && (getTelPhone() == null ? other.getTelPhone() == null : getTelPhone().equals(other.getTelPhone()))
                && (getCountry() == null ? other.getCountry() == null : getCountry().equals(other.getCountry()))
                && (getProvince() == null ? other.getProvince() == null : getProvince().equals(other.getProvince()))
                && (getCity() == null ? other.getCity() == null : getCity().equals(other.getCity()))
                && (getAddress() == null ? other.getAddress() == null : getAddress().equals(other.getAddress()))
                && (getLongitude() == null ? other.getLongitude() == null : getLongitude().equals(other.getLongitude()))
                && (getLatitude() == null ? other.getLatitude() == null : getLatitude().equals(other.getLatitude()))
                && (getSupplierPhoto() == null ? other.getSupplierPhoto() == null : getSupplierPhoto().equals(other.getSupplierPhoto()))
                && (getSupplierLicense() == null ? other.getSupplierLicense() == null : getSupplierLicense().equals(other.getSupplierLicense()))
                && (getCategoryId() == null ? other.getCategoryId() == null : getCategoryId().equals(other.getCategoryId()))
                && (getSupplierStar() == null ? other.getSupplierStar() == null : getSupplierStar().equals(other.getSupplierStar()))
                && (getRemark() == null ? other.getRemark() == null : getRemark().equals(other.getRemark()))
                && (getAuditStatus() == null ? other.getAuditStatus() == null : getAuditStatus().equals(other.getAuditStatus()))
                && (getReason() == null ? other.getReason() == null : getReason().equals(other.getReason()))
                && (getCreateTime() == null ? other.getCreateTime() == null : getCreateTime().equals(other.getCreateTime()))
                && (getUpdateTime() == null ? other.getUpdateTime() == null : getUpdateTime().equals(other.getUpdateTime()))
                && (getIsDelete() == null ? other.getIsDelete() == null : getIsDelete().equals(other.getIsDelete()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getUserId() == null) ? 0 : getUserId().hashCode());
        result = prime * result + ((getSupplierNo() == null) ? 0 : getSupplierNo().hashCode());
        result = prime * result + ((getSupplierName() == null) ? 0 : getSupplierName().hashCode());
        result = prime * result + ((getLinkman() == null) ? 0 : getLinkman().hashCode());
        result = prime * result + ((getTelPhone() == null) ? 0 : getTelPhone().hashCode());
        result = prime * result + ((getCountry() == null) ? 0 : getCountry().hashCode());
        result = prime * result + ((getProvince() == null) ? 0 : getProvince().hashCode());
        result = prime * result + ((getCity() == null) ? 0 : getCity().hashCode());
        result = prime * result + ((getAddress() == null) ? 0 : getAddress().hashCode());
        result = prime * result + ((getLongitude() == null) ? 0 : getLongitude().hashCode());
        result = prime * result + ((getLatitude() == null) ? 0 : getLatitude().hashCode());
        result = prime * result + ((getSupplierPhoto() == null) ? 0 : getSupplierPhoto().hashCode());
        result = prime * result + ((getSupplierLicense() == null) ? 0 : getSupplierLicense().hashCode());
        result = prime * result + ((getCategoryId() == null) ? 0 : getCategoryId().hashCode());
        result = prime * result + ((getSupplierStar() == null) ? 0 : getSupplierStar().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getAuditStatus() == null) ? 0 : getAuditStatus().hashCode());
        result = prime * result + ((getReason() == null) ? 0 : getReason().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getIsDelete() == null) ? 0 : getIsDelete().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName()).append(" [");
        sb.append("Hash=").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", userId=").append(userId);
        sb.append(", supplierNo=").append(supplierNo);
        sb.append(", supplierName=").append(supplierName);
        sb.append(", linkman=").append(linkman);
        sb.append(", TelPhone=").append(TelPhone);
        sb.append(", country=").append(country);
        sb.append(", province=").append(province);
        sb.append(", city=").append(city);
        sb.append(", address=").append(address);
        sb.append(", longitude=").append(longitude);
        sb.append(", latitude=").append(latitude);
        sb.append(", supplierPhoto=").append(supplierPhoto);
        sb.append(", supplierLicense=").append(supplierLicense);
        sb.append(", categoryId=").append(categoryId);
        sb.append(", supplierStar=").append(supplierStar);
        sb.append(", remark=").append(remark);
        sb.append(", auditStatus=").append(auditStatus);
        sb.append(", reason=").append(reason);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", isDelete=").append(isDelete);
        sb.append("]");
        return sb.toString();
    }
}