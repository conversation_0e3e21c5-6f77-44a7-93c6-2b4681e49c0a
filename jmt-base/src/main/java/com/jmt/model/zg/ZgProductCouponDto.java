package com.jmt.model.zg;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 筷圣直供商品抵扣券
 */
@Data
public class ZgProductCouponDto {

    /**
     * 主键
     */
    private Long id;

    /**
     * 抵扣券编码
     */
    private String couponNo;

    /**
     * 抵扣券名称
     */
    private String couponName;

    /**
     * 商品品类ID
     */
    private Long categoryId;

    /**
     * 有效期
     */
    private Date validityPeriod;

    /**
     * 抵扣金额
     */
    private BigDecimal couponAmount;

    /**
     * 描述
     */
    private String description;

}
