package com.jmt.model.zg;

import lombok.Data;

import java.util.Date;

/**
 * 筷圣直供囤货券包表
 * @TableName zg_ticket_pakage
 */
@Data
public class ZgTicketPackage {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 券的所有人统一账号ID
     */
    private Long buyUserId;

    /**
     * 囤货券实例序列号（唯一标识）
     */
    private String ticketSeqNo;

    /**
     * 供应商编号
     */
    private String supplierNo;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 囤货券编号
     */
    private String ticketNo;

    /**
     * 囤货券名称
     */
    private String ticketName;

    /**
     * 原价（单位：分）
     */
    private Integer originalPrice;

    /**
     * 现价（单位：分）
     */
    private Integer currentPrice;

    /**
     * 预付金额（单位：分）
     */
    private Integer prepayAmount;

    /**
     * 尾款金额（单位：分）
     */
    private Integer postpayAmount;

    /**
     * 有效期开始时间
     */
    private Date effectiveStartTime;

    /**
     * 有效期结束时间
     */
    private Date effectiveEndTime;

    /**
     * 商品图片URL
     */
    private String productPicture;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品规格
     */
    private String productSize;

    /**
     * 商品数量
     */
    private Integer productNum;

    /**
     * 券的状态<br/>
     * 0-未使用<br/>
     * 1-已使用<br/>
     * 2-已退款<br/>
     * 3-已核销
     */
    private Integer tStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除（0-否，1-是）
     */
    private Integer isDelete;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ZgTicketPackage that = (ZgTicketPackage) o;
        return java.util.Objects.equals(id, that.id) &&
                java.util.Objects.equals(buyUserId, that.buyUserId) &&
                java.util.Objects.equals(ticketSeqNo, that.ticketSeqNo) &&
                java.util.Objects.equals(supplierNo, that.supplierNo) &&
                java.util.Objects.equals(supplierName, that.supplierName) &&
                java.util.Objects.equals(ticketNo, that.ticketNo) &&
                java.util.Objects.equals(ticketName, that.ticketName) &&
                java.util.Objects.equals(originalPrice, that.originalPrice) &&
                java.util.Objects.equals(currentPrice, that.currentPrice) &&
                java.util.Objects.equals(prepayAmount, that.prepayAmount) &&
                java.util.Objects.equals(postpayAmount, that.postpayAmount) &&
                java.util.Objects.equals(effectiveStartTime, that.effectiveStartTime) &&
                java.util.Objects.equals(effectiveEndTime, that.effectiveEndTime) &&
                java.util.Objects.equals(productPicture, that.productPicture) &&
                java.util.Objects.equals(productName, that.productName) &&
                java.util.Objects.equals(productSize, that.productSize) &&
                java.util.Objects.equals(productNum, that.productNum) &&
                java.util.Objects.equals(tStatus, that.tStatus) &&
                java.util.Objects.equals(createTime, that.createTime) &&
                java.util.Objects.equals(updateTime, that.updateTime) &&
                java.util.Objects.equals(isDelete, that.isDelete);
    }

    @Override
    public int hashCode() {
        return java.util.Objects.hash(
                id, buyUserId, ticketSeqNo, supplierNo, supplierName,
                ticketNo, ticketName, originalPrice, currentPrice,
                prepayAmount, postpayAmount, effectiveStartTime,
                effectiveEndTime, productPicture, productName,
                productSize, productNum, tStatus, createTime,
                updateTime, isDelete
        );
    }

    @Override
    public String toString() {
        return "ZgTicketPackage{" +
                "id=" + id +
                ", buyUserId=" + buyUserId +
                ", ticketSeqNo='" + ticketSeqNo + '\'' +
                ", supplierNo='" + supplierNo + '\'' +
                ", supplierName='" + supplierName + '\'' +
                ", ticketNo='" + ticketNo + '\'' +
                ", ticketName='" + ticketName + '\'' +
                ", originalPrice=" + originalPrice +
                ", currentPrice=" + currentPrice +
                ", prepayAmount=" + prepayAmount +
                ", postpayAmount=" + postpayAmount +
                ", effectiveStartTime=" + effectiveStartTime +
                ", effectiveEndTime=" + effectiveEndTime +
                ", productPicture='" + productPicture + '\'' +
                ", productName='" + productName + '\'' +
                ", productSize='" + productSize + '\'' +
                ", productNum=" + productNum +
                ", ticketStatus=" + tStatus +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", isDelete=" + isDelete +
                '}';
    }
}