package com.jmt.model.zg;

import lombok.Data;

import java.util.Date;

/**
 * 筷圣直供购物车表
 * @TableName zg_ticket_cart
 */
@Data
public class ZgTicketCart {
    /**
     * 主键
     */
    private Long id;

    /**
     * 供应商编号（关联 zg_supplier_info 的 supplierNo）
     */
    private String supplierNo;

    /**
     * 囤货券编号（关联 zg_ticket_info 的 ticketNo）
     */
    private String ticketNo;

    /**
     * 囤货券名称
     */
    private String ticketName;

    /**
     * 统一账号uaaId
     */
    private Long buyUserId;

    /**
     * 原价
     */
    private Integer originalPrice;

    /**
     * 现价
     */
    private Integer currentPrice;

    /**
     * 预付金额
     */
    private Integer prepayAmount;

    /**
     * 尾款金额
     */
    private Integer postpayAmount;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除（0-否，1-是）
     */
    private Integer isDelete;


    @Override
    public boolean equals(Object that) {
        if (this == that) return true;
        if (that == null || getClass() != that.getClass()) return false;
        ZgTicketCart other = (ZgTicketCart) that;
        return (getId() == null ? other.getId() == null : getId().equals(other.getId()))
                && (getSupplierNo() == null ? other.getSupplierNo() == null : getSupplierNo().equals(other.getSupplierNo()))
                && (getTicketNo() == null ? other.getTicketNo() == null : getTicketNo().equals(other.getTicketNo()))
                && (getTicketName() == null ? other.getTicketName() == null : getTicketName().equals(other.getTicketName()))
                && (getBuyUserId() == null ? other.getBuyUserId() == null : getBuyUserId().equals(other.getBuyUserId()))
                && (getOriginalPrice() == null ? other.getOriginalPrice() == null : getOriginalPrice().equals(other.getOriginalPrice()))
                && (getCurrentPrice() == null ? other.getCurrentPrice() == null : getCurrentPrice().equals(other.getCurrentPrice()))
                && (getPrepayAmount() == null ? other.getPrepayAmount() == null : getPrepayAmount().equals(other.getPrepayAmount()))
                && (getPostpayAmount() == null ? other.getPostpayAmount() == null : getPostpayAmount().equals(other.getPostpayAmount()))
                && (getQuantity() == null ? other.getQuantity() == null : getQuantity().equals(other.getQuantity()))
                && (getCreateTime() == null ? other.getCreateTime() == null : getCreateTime().equals(other.getCreateTime()))
                && (getUpdateTime() == null ? other.getUpdateTime() == null : getUpdateTime().equals(other.getUpdateTime()))
                && (getIsDelete() == null ? other.getIsDelete() == null : getIsDelete().equals(other.getIsDelete()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSupplierNo() == null) ? 0 : getSupplierNo().hashCode());
        result = prime * result + ((getTicketNo() == null) ? 0 : getTicketNo().hashCode());
        result = prime * result + ((getTicketName() == null) ? 0 : getTicketName().hashCode());
        result = prime * result + ((getBuyUserId() == null) ? 0 : getBuyUserId().hashCode());
        result = prime * result + ((getOriginalPrice() == null) ? 0 : getOriginalPrice().hashCode());
        result = prime * result + ((getCurrentPrice() == null) ? 0 : getCurrentPrice().hashCode());
        result = prime * result + ((getPrepayAmount() == null) ? 0 : getPrepayAmount().hashCode());
        result = prime * result + ((getPostpayAmount() == null) ? 0 : getPostpayAmount().hashCode());
        result = prime * result + ((getQuantity() == null) ? 0 : getQuantity().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getIsDelete() == null) ? 0 : getIsDelete().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName()).append(" [");
        sb.append("Hash=").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", supplierNo=").append(supplierNo);
        sb.append(", ticketNo=").append(ticketNo);
        sb.append(", ticketName=").append(ticketName);
        sb.append(", buyUserId=").append(buyUserId);
        sb.append(", originalPrice=").append(originalPrice);
        sb.append(", currentPrice=").append(currentPrice);
        sb.append(", prepayAmount=").append(prepayAmount);
        sb.append(", postpayAmount=").append(postpayAmount);
        sb.append(", quantity=").append(quantity);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", isDelete=").append(isDelete);
        sb.append("]");
        return sb.toString();
    }
}