package com.jmt.model.zg.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ZgCouponVO {
    /**
     * 优惠券ID
     */
    private Long id;

    /**
     * 优惠券编号
     */
    private String couponNo;

    /**
     * 优惠券名称
     */
    private String couponName;

    /**
     * 商品品类ID
     */
    private Long categoryId;

    /**
     * 品类名称
     */
    private String categoryName;

    /**
     * 有效期
     */
    private Date validityPeriod;

    /**
     * 抵扣金额
     */
    private BigDecimal couponAmount;

    /**
     * 描述
     */
    private String description;

    /**
     * 是否已领取
     * 0-未领取，1-已领取
     */
    private Integer isReceived;

    /**
     * 领取状态描述
     */
    private String receiveStatusText;

    /**
     * 是否有效（未过期）
     */
    private Boolean isValid;

    /**
     * 剩余天数
     */
    private Long remainingDays;
}
