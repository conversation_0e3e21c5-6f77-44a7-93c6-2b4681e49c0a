package com.jmt.model.zg;

import lombok.Data;

import java.util.Date;

/**
 * 筷圣直供供应商信息审核记录表
 * @TableName zg_supplier_info_audit
 */
@Data
public class ZgSupplierInfoAudit {
    /**
     * 主键
     */
    private Long id;

    /**
     * 审核人，uaa_user的主键uaaId
     */
    private Long auditUser;

    /**
     * 供应商编号
     */
    private String supplierNo;

    /**
     * 审核状态<br/>
     * 0-待审核<br/>1-同意<br/>2-驳回<br/>3-冻结
     */
    private String auditStatus;

    /**
     * 备注
     */
    private String reason;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除（0-否，1-是）
     */
    private Integer isDelete;


    @Override
    public boolean equals(Object that) {
        if (this == that) return true;
        if (that == null || getClass() != that.getClass()) return false;
        ZgSupplierInfoAudit other = (ZgSupplierInfoAudit) that;
        return (getId() == null ? other.getId() == null : getId().equals(other.getId()))
                && (getAuditUser() == null ? other.getAuditUser() == null : getAuditUser().equals(other.getAuditUser()))
                && (getSupplierNo() == null ? other.getSupplierNo() == null : getSupplierNo().equals(other.getSupplierNo()))
                && (getAuditStatus() == null ? other.getAuditStatus() == null : getAuditStatus().equals(other.getAuditStatus()))
                && (getReason() == null ? other.getReason() == null : getReason().equals(other.getReason()))
                && (getCreateTime() == null ? other.getCreateTime() == null : getCreateTime().equals(other.getCreateTime()))
                && (getUpdateTime() == null ? other.getUpdateTime() == null : getUpdateTime().equals(other.getUpdateTime()))
                && (getIsDelete() == null ? other.getIsDelete() == null : getIsDelete().equals(other.getIsDelete()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getAuditUser() == null) ? 0 : getAuditUser().hashCode());
        result = prime * result + ((getSupplierNo() == null) ? 0 : getSupplierNo().hashCode());
        result = prime * result + ((getAuditStatus() == null) ? 0 : getAuditStatus().hashCode());
        result = prime * result + ((getReason() == null) ? 0 : getReason().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getIsDelete() == null) ? 0 : getIsDelete().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName()).append(" [");
        sb.append("Hash=").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", auditUser=").append(auditUser);
        sb.append(", supplierNo=").append(supplierNo);
        sb.append(", auditStatus=").append(auditStatus);
        sb.append(", reason=").append(reason);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", isDelete=").append(isDelete);
        sb.append("]");
        return sb.toString();
    }
}