package com.jmt.model.zg;

import lombok.Data;

import java.util.Date;

/**
 * 筷圣直供商品订单表
 * @TableName zg_product_order
 */
@Data
public class ZgProductOrder {
    /**
     * 主键
     */
    private Long id;

    /**
     * 下单人的统一账号uaaId
     */
    private Long buyUserId;

    /**
     * 配送地址id（关联 uaa_user_address 的id）
     */
    private Long addressId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 订单名称
     */
    private String orderName;

    /**
     * 供应商编号
     */
    private String supplierNo;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 囤货券实例序列号（唯一标识）
     */
    private String ticketSeqNo;

    /**
     * 囤货券编号
     */
    private String ticketNo;

    /**
     * 囤货券名称
     */
    private String ticketName;

    /**
     * 原价
     */
    private Integer originalPrice;

    /**
     * 现价
     */
    private Integer currentPrice;

    /**
     * 预付金额
     */
    private Integer prepayAmount;

    /**
     * 尾款金额
     */
    private Integer postpayAmount;

    /**
     * 有效期开始时间
     */
    private Date effectiveStartTime;

    /**
     * 有效期结束时间
     */
    private Date effectiveEndTime;

    /**
     * 商品图片
     */
    private String productPicture;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品规格
     */
    private String productSize;

    /**
     * 商品数量
     */
    private Integer productNum;

    /**
     * 支付方式<br/>
     * 0-积分支付<br/>1-余额支付<br/>2-微信支付<br/>3-支付宝
     */
    private Integer payType;

    /**
     * 订单状态<br/>
     * 0-未支付<br/>1-已支付<br/>2-待发货<br/>3-已发货<br/>4-已收货
     */
    private Integer orderStatus;

    /**
     * 核销二维码
     */
    private String usedQrCode;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除（0-否，1-是）
     */
    private Integer isDelete;


    @Override
    public boolean equals(Object that) {
        if (this == that) return true;
        if (that == null || getClass() != that.getClass()) return false;
        ZgProductOrder other = (ZgProductOrder) that;
        return (getId() == null ? other.getId() == null : getId().equals(other.getId()))
                && (getBuyUserId() == null ? other.getBuyUserId() == null : getBuyUserId().equals(other.getBuyUserId()))
                && (getAddressId() == null ? other.getAddressId() == null : getAddressId().equals(other.getAddressId()))
                && (getOrderNo() == null ? other.getOrderNo() == null : getOrderNo().equals(other.getOrderNo()))
                && (getOrderName() == null ? other.getOrderName() == null : getOrderName().equals(other.getOrderName()))
                && (getSupplierNo() == null ? other.getSupplierNo() == null : getSupplierNo().equals(other.getSupplierNo()))
                && (getSupplierName() == null ? other.getSupplierName() == null : getSupplierName().equals(other.getSupplierName()))
                && (getTicketSeqNo() == null ? other.getTicketSeqNo() == null : getTicketSeqNo().equals(other.getTicketSeqNo()))
                && (getTicketNo() == null ? other.getTicketNo() == null : getTicketNo().equals(other.getTicketNo()))
                && (getTicketName() == null ? other.getTicketName() == null : getTicketName().equals(other.getTicketName()))
                && (getOriginalPrice() == null ? other.getOriginalPrice() == null : getOriginalPrice().equals(other.getOriginalPrice()))
                && (getCurrentPrice() == null ? other.getCurrentPrice() == null : getCurrentPrice().equals(other.getCurrentPrice()))
                && (getPrepayAmount() == null ? other.getPrepayAmount() == null : getPrepayAmount().equals(other.getPrepayAmount()))
                && (getPostpayAmount() == null ? other.getPostpayAmount() == null : getPostpayAmount().equals(other.getPostpayAmount()))
                && (getEffectiveStartTime() == null ? other.getEffectiveStartTime() == null : getEffectiveStartTime().equals(other.getEffectiveStartTime()))
                && (getEffectiveEndTime() == null ? other.getEffectiveEndTime() == null : getEffectiveEndTime().equals(other.getEffectiveEndTime()))
                && (getProductPicture() == null ? other.getProductPicture() == null : getProductPicture().equals(other.getProductPicture()))
                && (getProductName() == null ? other.getProductName() == null : getProductName().equals(other.getProductName()))
                && (getProductSize() == null ? other.getProductSize() == null : getProductSize().equals(other.getProductSize()))
                && (getProductNum() == null ? other.getProductNum() == null : getProductNum().equals(other.getProductNum()))
                && (getPayType() == null ? other.getPayType() == null : getPayType().equals(other.getPayType()))
                && (getOrderStatus() == null ? other.getOrderStatus() == null : getOrderStatus().equals(other.getOrderStatus()))
                && (getUsedQrCode() == null ? other.getUsedQrCode() == null : getUsedQrCode().equals(other.getUsedQrCode()))
                && (getCreateTime() == null ? other.getCreateTime() == null : getCreateTime().equals(other.getCreateTime()))
                && (getUpdateTime() == null ? other.getUpdateTime() == null : getUpdateTime().equals(other.getUpdateTime()))
                && (getIsDelete() == null ? other.getIsDelete() == null : getIsDelete().equals(other.getIsDelete()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + (getId() == null ? 0 : getId().hashCode());
        result = prime * result + (getBuyUserId() == null ? 0 : getBuyUserId().hashCode());
        result = prime * result + (getAddressId() == null ? 0 : getAddressId().hashCode());
        result = prime * result + (getOrderNo() == null ? 0 : getOrderNo().hashCode());
        result = prime * result + (getOrderName() == null ? 0 : getOrderName().hashCode());
        result = prime * result + (getSupplierNo() == null ? 0 : getSupplierNo().hashCode());
        result = prime * result + (getSupplierName() == null ? 0 : getSupplierName().hashCode());
        result = prime * result + (getTicketSeqNo() == null ? 0 : getTicketSeqNo().hashCode());
        result = prime * result + (getTicketNo() == null ? 0 : getTicketNo().hashCode());
        result = prime * result + (getTicketName() == null ? 0 : getTicketName().hashCode());
        result = prime * result + (getOriginalPrice() == null ? 0 : getOriginalPrice().hashCode());
        result = prime * result + (getCurrentPrice() == null ? 0 : getCurrentPrice().hashCode());
        result = prime * result + (getPrepayAmount() == null ? 0 : getPrepayAmount().hashCode());
        result = prime * result + (getPostpayAmount() == null ? 0 : getPostpayAmount().hashCode());
        result = prime * result + (getEffectiveStartTime() == null ? 0 : getEffectiveStartTime().hashCode());
        result = prime * result + (getEffectiveEndTime() == null ? 0 : getEffectiveEndTime().hashCode());
        result = prime * result + (getProductPicture() == null ? 0 : getProductPicture().hashCode());
        result = prime * result + (getProductName() == null ? 0 : getProductName().hashCode());
        result = prime * result + (getProductSize() == null ? 0 : getProductSize().hashCode());
        result = prime * result + (getProductNum() == null ? 0 : getProductNum().hashCode());
        result = prime * result + (getPayType() == null ? 0 : getPayType().hashCode());
        result = prime * result + (getOrderStatus() == null ? 0 : getOrderStatus().hashCode());
        result = prime * result + (getUsedQrCode() == null ? 0 : getUsedQrCode().hashCode());
        result = prime * result + (getCreateTime() == null ? 0 : getCreateTime().hashCode());
        result = prime * result + (getUpdateTime() == null ? 0 : getUpdateTime().hashCode());
        result = prime * result + (getIsDelete() == null ? 0 : getIsDelete().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName()).append(" [");
        sb.append("Hash=").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", buyUserId=").append(buyUserId);
        sb.append(", addressId=").append(addressId);
        sb.append(", orderNo=").append(orderNo);
        sb.append(", orderName=").append(orderName);
        sb.append(", supplierNo=").append(supplierNo);
        sb.append(", supplierName=").append(supplierName);
        sb.append(", ticketSeqNo=").append(ticketSeqNo);
        sb.append(", ticketNo=").append(ticketNo);
        sb.append(", ticketName=").append(ticketName);
        sb.append(", originalPrice=").append(originalPrice);
        sb.append(", currentPrice=").append(currentPrice);
        sb.append(", prepayAmount=").append(prepayAmount);
        sb.append(", postpayAmount=").append(postpayAmount);
        sb.append(", effectiveStartTime=").append(effectiveStartTime);
        sb.append(", effectiveEndTime=").append(effectiveEndTime);
        sb.append(", productPicture=").append(productPicture);
        sb.append(", productName=").append(productName);
        sb.append(", productSize=").append(productSize);
        sb.append(", productNum=").append(productNum);
        sb.append(", payType=").append(payType);
        sb.append(", orderStatus=").append(orderStatus);
        sb.append(", usedQrCode=").append(usedQrCode);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", isDelete=").append(isDelete);
        sb.append("]");
        return sb.toString();
    }
}