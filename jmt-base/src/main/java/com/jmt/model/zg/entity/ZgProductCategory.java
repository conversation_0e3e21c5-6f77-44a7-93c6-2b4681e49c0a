package com.jmt.model.zg.entity;

import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
* 筷圣直供商品品类
* @TableName zg_product_category
*/
@Data
public class ZgProductCategory implements Serializable {

    /**
    * 主键
    */
    private Long id;
    /**
    * 商品品类编码
    */
    private String categoryNo;
    /**
    * 商品品类名称
    */
    private String categoryName;
    /**
    * 创建时间
    */
    private Date createTime;
    /**
    * 更新时间
    */
    private Date updateTime;
    /**
    * 是否删除 0-否 1-是
    */
    private Integer isDelete;

    private Long parentId;

    private String categoryPicture;


}
