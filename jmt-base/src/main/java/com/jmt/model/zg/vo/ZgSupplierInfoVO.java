package com.jmt.model.zg.vo;

import lombok.Data;


@Data
public class ZgSupplierInfoVO {
    private Long id;
    private Long uuaId;

    private String supplierNo;

    private String supplierName;

    /**
     * 联系人
     */
    private String linkman;
    /**
     * 企业信用代码
     */
    private String creditCode;
    /**
     * 供应商性质
     1-厂家
     2-进口
     3-代理商
     4-贴牌商
     5-贸易商
     */
    private Integer nature;
    /**
     * 联系人手机号
     */
    private String telPhone;


    /**
     * 主营品类zg_product_category的id
     */
    private Integer categoryId;

    private String categoryName;

    /**
     * 配送模式
     1-配送到店
     2-仓对仓送
     3-到店提货
     4-工厂发货
     5-产地发货
     */
    private Integer deliveryModel;

    /**
     * 0-待审核
     1-同意
     2-驳回
     3-冻结
     */
    private Integer auditStatus;

}
