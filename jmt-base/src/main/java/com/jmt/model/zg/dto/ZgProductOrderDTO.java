package com.jmt.model.zg.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
@Data
public class ZgProductOrderDTO {
    /**
     *
     */
    private Long id;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 订单名称
     */
    private String orderName;
    /**
     * 下单用户的统一账号uaaId
     */
    private Long buyUserId;
    /**
     * 支付方式
     0-积分支付
     1-余额支付
     2-微信支付
     3-支付宝
     */
    private Integer payType;
    /**
     * 支付金额 单位元
     */
    private BigDecimal payAmount;
    /**
     * 订单状态
     0-未支付
     1-支付成功
     2-支付失败
     3-取消支付
     */
    private Integer orderStatus;
    /**
     * 配送员编号
     */
    private String driverNo;
    private Long addressId;

    private String province;
    private String city;
    private String area;
    private String address;
    /**
     * 更新时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否删除  0-否 1-是
     */
    private Integer isDelete;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;
}
