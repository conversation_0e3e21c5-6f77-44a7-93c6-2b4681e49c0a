package com.jmt.model.zg.vo;

import lombok.Data;

@Data
public class ZgDriverInfoVO {
    private Long id;

    /**
     * 配送员编号
     */
    private String driverNo;

    /**
     * 配送员姓名
     */
    private String driverName;

    /**
     * 身份证号
     */
    private String idNo;

    /**
     * 手机号
     */
    private String telPhone;

    /**
     * 区县
     */
    private String area;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 详细地址
     */
    private String address;


    /**
     * 配送范围
     */
    private String areaScope;

    /**
     * 车辆照片
     */
    private String carPhotoFront;

    private String carPhotoBack;

    /**
     * 车牌号
     */
    private String carNumber;

    /**
     * 车辆最大载重，单位KG
     */
    private Integer maxLoad;


    /**
     * 0-待审核
     1-同意
     2-驳回
     3-冻结
     */
    private Integer auditStatus;



}