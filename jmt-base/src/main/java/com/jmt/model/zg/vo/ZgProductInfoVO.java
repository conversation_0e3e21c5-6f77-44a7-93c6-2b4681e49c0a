package com.jmt.model.zg.vo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ZgProductInfoVO {
    private Long id;
    private String productNo;
    private String supplierName;
    private String productName;
    private String productMainPicture;
    private Long categoryId;
    private String categoryName;
    private String productSize;
    private BigDecimal originalPrice;
    private BigDecimal currentPrice;
    private Integer stock;
    private Integer buyLimit;
    private Integer auditStatus;

}
