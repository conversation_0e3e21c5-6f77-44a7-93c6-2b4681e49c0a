package com.jmt.model.zg;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商品信息表
 * @TableName zg_product
 */
@Data
public class ZgProduct {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 关联商家ID
     */
    private Long merchantId;

    /**
     * 商品许可证图片
     */
    private String merchantLicenseImg;

    /**
     * 质检报告图片
     */
    private String merchantQualityImg;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品条码
     */
    private String productBarcode;

    /**
     * 商品品牌
     */
    private String productBrand;

    /**
     * 商品编码
     */
    private String productCode;

    /**
     * 商品规格/重量
     */
    private String productWeight;

    /**
     * 包装类型(袋/箱/桶...)
     */
    private String packageType;

    /**
     * 商品等级(一级/二级...)
     */
    private String productLevel;

    /**
     * 税率(%)
     */
    private BigDecimal taxRate;

    /**
     * 促销基金类型
     */
    private String promotionType;

    /**
     * 市场价
     */
    private BigDecimal marketPrice;

    /**
     * 销售价
     */
    private BigDecimal salePrice;

    /**
     * 附图
     */
    private String attachmentImg;

    /**
     * 详情图
     */
    private String detailImg;

    /**
     * 状态(待审核/审核通过/驳回)
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ZgProduct other = (ZgProduct) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getMerchantId() == null ? other.getMerchantId() == null : this.getMerchantId().equals(other.getMerchantId()))
            && (this.getMerchantLicenseImg() == null ? other.getMerchantLicenseImg() == null : this.getMerchantLicenseImg().equals(other.getMerchantLicenseImg()))
            && (this.getMerchantQualityImg() == null ? other.getMerchantQualityImg() == null : this.getMerchantQualityImg().equals(other.getMerchantQualityImg()))
            && (this.getProductName() == null ? other.getProductName() == null : this.getProductName().equals(other.getProductName()))
            && (this.getProductBarcode() == null ? other.getProductBarcode() == null : this.getProductBarcode().equals(other.getProductBarcode()))
            && (this.getProductBrand() == null ? other.getProductBrand() == null : this.getProductBrand().equals(other.getProductBrand()))
            && (this.getProductCode() == null ? other.getProductCode() == null : this.getProductCode().equals(other.getProductCode()))
            && (this.getProductWeight() == null ? other.getProductWeight() == null : this.getProductWeight().equals(other.getProductWeight()))
            && (this.getPackageType() == null ? other.getPackageType() == null : this.getPackageType().equals(other.getPackageType()))
            && (this.getProductLevel() == null ? other.getProductLevel() == null : this.getProductLevel().equals(other.getProductLevel()))
            && (this.getTaxRate() == null ? other.getTaxRate() == null : this.getTaxRate().equals(other.getTaxRate()))
            && (this.getPromotionType() == null ? other.getPromotionType() == null : this.getPromotionType().equals(other.getPromotionType()))
            && (this.getMarketPrice() == null ? other.getMarketPrice() == null : this.getMarketPrice().equals(other.getMarketPrice()))
            && (this.getSalePrice() == null ? other.getSalePrice() == null : this.getSalePrice().equals(other.getSalePrice()))
            && (this.getAttachmentImg() == null ? other.getAttachmentImg() == null : this.getAttachmentImg().equals(other.getAttachmentImg()))
            && (this.getDetailImg() == null ? other.getDetailImg() == null : this.getDetailImg().equals(other.getDetailImg()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getMerchantId() == null) ? 0 : getMerchantId().hashCode());
        result = prime * result + ((getMerchantLicenseImg() == null) ? 0 : getMerchantLicenseImg().hashCode());
        result = prime * result + ((getMerchantQualityImg() == null) ? 0 : getMerchantQualityImg().hashCode());
        result = prime * result + ((getProductName() == null) ? 0 : getProductName().hashCode());
        result = prime * result + ((getProductBarcode() == null) ? 0 : getProductBarcode().hashCode());
        result = prime * result + ((getProductBrand() == null) ? 0 : getProductBrand().hashCode());
        result = prime * result + ((getProductCode() == null) ? 0 : getProductCode().hashCode());
        result = prime * result + ((getProductWeight() == null) ? 0 : getProductWeight().hashCode());
        result = prime * result + ((getPackageType() == null) ? 0 : getPackageType().hashCode());
        result = prime * result + ((getProductLevel() == null) ? 0 : getProductLevel().hashCode());
        result = prime * result + ((getTaxRate() == null) ? 0 : getTaxRate().hashCode());
        result = prime * result + ((getPromotionType() == null) ? 0 : getPromotionType().hashCode());
        result = prime * result + ((getMarketPrice() == null) ? 0 : getMarketPrice().hashCode());
        result = prime * result + ((getSalePrice() == null) ? 0 : getSalePrice().hashCode());
        result = prime * result + ((getAttachmentImg() == null) ? 0 : getAttachmentImg().hashCode());
        result = prime * result + ((getDetailImg() == null) ? 0 : getDetailImg().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", merchantId=").append(merchantId);
        sb.append(", merchantLicenseImg=").append(merchantLicenseImg);
        sb.append(", merchantQualityImg=").append(merchantQualityImg);
        sb.append(", productName=").append(productName);
        sb.append(", productBarcode=").append(productBarcode);
        sb.append(", productBrand=").append(productBrand);
        sb.append(", productCode=").append(productCode);
        sb.append(", productWeight=").append(productWeight);
        sb.append(", packageType=").append(packageType);
        sb.append(", productLevel=").append(productLevel);
        sb.append(", taxRate=").append(taxRate);
        sb.append(", promotionType=").append(promotionType);
        sb.append(", marketPrice=").append(marketPrice);
        sb.append(", salePrice=").append(salePrice);
        sb.append(", attachmentImg=").append(attachmentImg);
        sb.append(", detailImg=").append(detailImg);
        sb.append(", status=").append(status);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }
}