package com.jmt.model.zg;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 筷圣直供囤货券信息表
 * @TableName zg_ticket_info
 */
@Data
public class ZgTicketInfo {
    /**
     * 主键
     */
    private Long id;

    /**
     * 供应商编号
     */
    private String supplierNo;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 囤货券编号
     */
    private String ticketNo;

    /**
     * 囤货券名称
     */
    private String ticketName;

    /**
     * 原价
     */
    private BigDecimal originalPrice;

    /**
     * 现价
     */
    private BigDecimal currentPrice;

    /**
     * 预付金额
     */
    private BigDecimal prepayAmount;

    /**
     * 尾款金额
     */
    private BigDecimal postpayAmount;

    /**
     * 有效期开始时间
     */
    private Date effectiveStartTime;

    /**
     * 有效期结束时间
     */
    private Date effectiveEndTime;

    /**
     * 产品图片
     */
    private String productPicture;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品规格
     */
    private String productSize;

    /**
     * 产品数量
     */
    private Integer productNum;

    /**
     * 库存数量
     */
    private Integer stock;

    /**
     * 限购数量
     */
    private Integer buyLimit;

    /**
     * 审核状态<br/>
     * 0-待审核<br/>1-同意<br/>2-驳回
     */
    private Integer auditStatus;

    /**
     * 备注说明
     */
    private String remark;

    /**
     * 审核意见
     */
    private String reason;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除（0-否，1-是）
     */
    private Integer isDelete;


    @Override
    public boolean equals(Object that) {
        if (this == that) return true;
        if (that == null || getClass() != that.getClass()) return false;
        ZgTicketInfo other = (ZgTicketInfo) that;
        return (getId() == null ? other.getId() == null : getId().equals(other.getId()))
                && (getSupplierNo() == null ? other.getSupplierNo() == null : getSupplierNo().equals(other.getSupplierNo()))
                && (getSupplierName() == null ? other.getSupplierName() == null : getSupplierName().equals(other.getSupplierName()))
                && (getTicketNo() == null ? other.getTicketNo() == null : getTicketNo().equals(other.getTicketNo()))
                && (getTicketName() == null ? other.getTicketName() == null : getTicketName().equals(other.getTicketName()))
                && (getOriginalPrice() == null ? other.getOriginalPrice() == null : getOriginalPrice().equals(other.getOriginalPrice()))
                && (getCurrentPrice() == null ? other.getCurrentPrice() == null : getCurrentPrice().equals(other.getCurrentPrice()))
                && (getPrepayAmount() == null ? other.getPrepayAmount() == null : getPrepayAmount().equals(other.getPrepayAmount()))
                && (getPostpayAmount() == null ? other.getPostpayAmount() == null : getPostpayAmount().equals(other.getPostpayAmount()))
                && (getEffectiveStartTime() == null ? other.getEffectiveStartTime() == null : getEffectiveStartTime().equals(other.getEffectiveStartTime()))
                && (getEffectiveEndTime() == null ? other.getEffectiveEndTime() == null : getEffectiveEndTime().equals(other.getEffectiveEndTime()))
                && (getProductPicture() == null ? other.getProductPicture() == null : getProductPicture().equals(other.getProductPicture()))
                && (getProductName() == null ? other.getProductName() == null : getProductName().equals(other.getProductName()))
                && (getProductSize() == null ? other.getProductSize() == null : getProductSize().equals(other.getProductSize()))
                && (getProductNum() == null ? other.getProductNum() == null : getProductNum().equals(other.getProductNum()))
                && (getStock() == null ? other.getStock() == null : getStock().equals(other.getStock()))
                && (getBuyLimit() == null ? other.getBuyLimit() == null : getBuyLimit().equals(other.getBuyLimit()))
                && (getAuditStatus() == null ? other.getAuditStatus() == null : getAuditStatus().equals(other.getAuditStatus()))
                && (getRemark() == null ? other.getRemark() == null : getRemark().equals(other.getRemark()))
                && (getReason() == null ? other.getReason() == null : getReason().equals(other.getReason()))
                && (getCreateTime() == null ? other.getCreateTime() == null : getCreateTime().equals(other.getCreateTime()))
                && (getUpdateTime() == null ? other.getUpdateTime() == null : getUpdateTime().equals(other.getUpdateTime()))
                && (getIsDelete() == null ? other.getIsDelete() == null : getIsDelete().equals(other.getIsDelete()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSupplierNo() == null) ? 0 : getSupplierNo().hashCode());
        result = prime * result + ((getSupplierName() == null) ? 0 : getSupplierName().hashCode());
        result = prime * result + ((getTicketNo() == null) ? 0 : getTicketNo().hashCode());
        result = prime * result + ((getTicketName() == null) ? 0 : getTicketName().hashCode());
        result = prime * result + ((getOriginalPrice() == null) ? 0 : getOriginalPrice().hashCode());
        result = prime * result + ((getCurrentPrice() == null) ? 0 : getCurrentPrice().hashCode());
        result = prime * result + ((getPrepayAmount() == null) ? 0 : getPrepayAmount().hashCode());
        result = prime * result + ((getPostpayAmount() == null) ? 0 : getPostpayAmount().hashCode());
        result = prime * result + ((getEffectiveStartTime() == null) ? 0 : getEffectiveStartTime().hashCode());
        result = prime * result + ((getEffectiveEndTime() == null) ? 0 : getEffectiveEndTime().hashCode());
        result = prime * result + ((getProductPicture() == null) ? 0 : getProductPicture().hashCode());
        result = prime * result + ((getProductName() == null) ? 0 : getProductName().hashCode());
        result = prime * result + ((getProductSize() == null) ? 0 : getProductSize().hashCode());
        result = prime * result + ((getProductNum() == null) ? 0 : getProductNum().hashCode());
        result = prime * result + ((getStock() == null) ? 0 : getStock().hashCode());
        result = prime * result + ((getBuyLimit() == null) ? 0 : getBuyLimit().hashCode());
        result = prime * result + ((getAuditStatus() == null) ? 0 : getAuditStatus().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getReason() == null) ? 0 : getReason().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getIsDelete() == null) ? 0 : getIsDelete().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName()).append(" [");
        sb.append("Hash=").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", supplierNo=").append(supplierNo);
        sb.append(", supplierName=").append(supplierName);
        sb.append(", ticketNo=").append(ticketNo);
        sb.append(", ticketName=").append(ticketName);
        sb.append(", originalPrice=").append(originalPrice);
        sb.append(", currentPrice=").append(currentPrice);
        sb.append(", prepayAmount=").append(prepayAmount);
        sb.append(", postpayAmount=").append(postpayAmount);
        sb.append(", effectiveStartTime=").append(effectiveStartTime);
        sb.append(", effectiveEndTime=").append(effectiveEndTime);
        sb.append(", productPicture=").append(productPicture);
        sb.append(", productName=").append(productName);
        sb.append(", productSize=").append(productSize);
        sb.append(", productNum=").append(productNum);
        sb.append(", stock=").append(stock);
        sb.append(", buyLimit=").append(buyLimit);
        sb.append(", auditStatus=").append(auditStatus);
        sb.append(", remark=").append(remark);
        sb.append(", reason=").append(reason);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", isDelete=").append(isDelete);
        sb.append("]");
        return sb.toString();
    }

    // 手动添加的getter方法（与Lombok生成的重复，可删除）
    // public BigDecimal getCurrentPrice() {
    //     return this.currentprice;
    // }
}