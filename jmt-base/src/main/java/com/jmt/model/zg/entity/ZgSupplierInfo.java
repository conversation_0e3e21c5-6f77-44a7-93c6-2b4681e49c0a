package com.jmt.model.zg.entity;

import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
* 筷圣直供供应商信息表
* @TableName zg_supplier_info
*/
@Data
public class ZgSupplierInfo implements Serializable {

    /**
    * 
    */
    private Long id;
    /**
    * 商家或门店的uaaId
    */
    private Long uuaId;
    /**
    * 供应商编号
    */
    private String supplierNo;
    /**
    * 供应商名称
    */
    private String supplierName;
    /**
    * 法定代表
    */
    private String legalPerson;
    /**
    * 开户银行
    */
    private String bankName;
    /**
    * 银行卡号
    */
    private String bankCardNo;
    /**
    * 联系人
    */
    private String linkman;
    /**
    * 企业信用代码
    */
    private String creditCode;
    /**
    * 供应商性质
1-厂家
2-进口
3-代理商
4-贴牌商
5-贸易商
    */
    private Integer nature;
    /**
    * 联系人手机号
    */
    private String telPhone;
    /**
    * 国家
    */
    private String country;
    /**
    * 省份
    */
    private String province;
    /**
    * 城市
    */
    private String city;

    private String area;
    /**
    * 详细地址
    */
    private String address;
    /**
    * 经度
    */
    private String longitude;
    /**
    * 维度
    */
    private String latitude;
    /**
    * 供应商许可证
    */
    private String supplierPermit;
    /**
    * 供应商营业执照
    */
    private String supplierLicense;
    /**
    * 主营品类zg_product_category的id
    */
    private Integer categoryId;
    /**
    * 星级 1-5 标识1-5个星
    */
    private Integer supplierStar;
    /**
    * 配送模式
1-配送到店
2-仓对仓送
3-到店提货
4-工厂发货
5-产地发货
    */
    private Integer deliveryModel;
    /**
    * 起送金额,当配送模式为1-配送到店时必填
    */
    private BigDecimal minDeliveryAmount;
    /**
    * 备注说明
    */
    private String remark;
    /**
    * 0-待审核
1-同意
2-驳回
3-冻结
    */
    private Integer auditStatus;
    /**
    * 审核意见
    */
    private String reason;
    /**
    * 更新时间
    */
    private Date createTime;
    /**
    * 更新时间
    */
    private Date updateTime;
    /**
    * 是否删除  0-否 1-是
    */
    private Integer isDelete;



}
