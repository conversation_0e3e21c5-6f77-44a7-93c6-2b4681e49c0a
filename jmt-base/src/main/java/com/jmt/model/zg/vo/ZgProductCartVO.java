package com.jmt.model.zg.vo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ZgProductCartVO {
    //购物车id
    private String id;
    private String productNo;
    private String productName;
    private BigDecimal originalPrice;

    private BigDecimal currentPrice;
    private Integer quantity;

    /**
     * 商品条码
     */
    private String productBarCode;
    /**
     * 商品规格
     */
    private String productSize;
    /**
     * 商品主图片
     */
    private String productMainPicture;
    /**
     * 商品性质
     0-其他
     1-一级
     2-二级
     3-三级
     4-统货
     5-临期
     6-库存

     */
    private Integer productNature;

    private Long categoryId;
    private String categoryName;
}
