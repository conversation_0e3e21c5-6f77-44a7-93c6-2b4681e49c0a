package com.jmt.model.zg;

import com.jmt.model.zg.vo.ZgProductCategoryChildVo;
import lombok.Data;


import java.util.List;

/**
 * 筷圣直供商品品类
 * @TableName zg_category
 */
@Data
public class ZgProductCategoryVo {
    /**
     * 主键
     */
    private Long id;

    /**
     * 行业类别编码
     */
    private String categoryNo;

    /**
     * 行业类别名称
     */
    private String categoryName;

    private String categoryPicture;
    private Long parentId;
    private List<ZgProductCategoryChildVo> subCategories;
}
