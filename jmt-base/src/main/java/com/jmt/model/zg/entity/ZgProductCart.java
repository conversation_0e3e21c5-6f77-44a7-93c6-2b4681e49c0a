package com.jmt.model.zg.entity;

import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
* 筷圣直供购物车表
* @TableName zg_product_cart
*/
@Data
public class ZgProductCart implements Serializable {

    /**
    * 
    */
    private Long id;
    /**
    * 供应商编号 zg_supplier_info的supplierNo
    */
    private String supplierNo;
    /**
    * 商品编号zg_product_info的productNo
    */
    private String productNo;
    /**
    * 商品名称
    */
    private String productName;
    /**
    * 统一账号uaaId
    */
    private Long buyUserId;
    /**
    * 原价
    */
    private BigDecimal originalPrice;
    /**
    * 现价
    */
    private BigDecimal currentPrice;
    /**
    * 数量
    */
    private Integer quantity;
    /**
    * 更新时间
    */
    private Date createTime;
    /**
    * 更新时间
    */
    private Date updateTime;
    /**
    * 是否删除  0-否 1-是
    */
    private Integer isDelete;


}
