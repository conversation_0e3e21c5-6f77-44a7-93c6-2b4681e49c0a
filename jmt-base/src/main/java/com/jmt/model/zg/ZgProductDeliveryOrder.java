package com.jmt.model.zg;

import lombok.Data;

import java.util.Date;

/**
 * 筷圣直供商品配送订单表
 * @TableName zg_product_delivery_order
 */
@Data
public class ZgProductDeliveryOrder {
    /**
     *
     */
    private Long id;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 订单名称
     */
    private String orderName;

    /**
     * 供应商编号
     */
    private String driverNo;

    /**
     * 商品总量
     */
    private Integer productTotal;

    /**
     * 订单总数
     */
    private Integer orderCount;

    /**
     * 取货地数量（卖家数量）
     */
    private Integer sellerCount;

    /**
     * 送货地数量（买家数量）
     */
    private Integer buyerCount;

    /**
     * 订单状态
     * 0-待确认
     * 1-已确认
     * 2-拒绝
     * 3-完成
     */
    private Integer orderStatus;

    /**
     * 拒绝原因
     */
    private String reason;

    /**
     * 提货二维码
     */
    private String takeQrCode;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除  0-否 1-是
     */
    private Integer isDelete;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ZgProductDeliveryOrder other = (ZgProductDeliveryOrder) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getOrderNo() == null ? other.getOrderNo() == null : this.getOrderNo().equals(other.getOrderNo()))
                && (this.getOrderName() == null ? other.getOrderName() == null : this.getOrderName().equals(other.getOrderName()))
                && (this.getDriverNo() == null ? other.getDriverNo() == null : this.getDriverNo().equals(other.getDriverNo()))
                && (this.getProductTotal() == null ? other.getProductTotal() == null : this.getProductTotal().equals(other.getProductTotal()))
                && (this.getOrderCount() == null ? other.getOrderCount() == null : this.getOrderCount().equals(other.getOrderCount()))
                && (this.getSellerCount() == null ? other.getSellerCount() == null : this.getSellerCount().equals(other.getSellerCount()))
                && (this.getBuyerCount() == null ? other.getBuyerCount() == null : this.getBuyerCount().equals(other.getBuyerCount()))
                && (this.getOrderStatus() == null ? other.getOrderStatus() == null : this.getOrderStatus().equals(other.getOrderStatus()))
                && (this.getReason() == null ? other.getReason() == null : this.getReason().equals(other.getReason()))
                && (this.getTakeQrCode() == null ? other.getTakeQrCode() == null : this.getTakeQrCode().equals(other.getTakeQrCode()))
                && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
                && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
                && (this.getIsDelete() == null ? other.getIsDelete() == null : this.getIsDelete().equals(other.getIsDelete()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getOrderNo() == null) ? 0 : getOrderNo().hashCode());
        result = prime * result + ((getOrderName() == null) ? 0 : getOrderName().hashCode());
        result = prime * result + ((getDriverNo() == null) ? 0 : getDriverNo().hashCode());
        result = prime * result + ((getProductTotal() == null) ? 0 : getProductTotal().hashCode());
        result = prime * result + ((getOrderCount() == null) ? 0 : getOrderCount().hashCode());
        result = prime * result + ((getSellerCount() == null) ? 0 : getSellerCount().hashCode());
        result = prime * result + ((getBuyerCount() == null) ? 0 : getBuyerCount().hashCode());
        result = prime * result + ((getOrderStatus() == null) ? 0 : getOrderStatus().hashCode());
        result = prime * result + ((getReason() == null) ? 0 : getReason().hashCode());
        result = prime * result + ((getTakeQrCode() == null) ? 0 : getTakeQrCode().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getIsDelete() == null) ? 0 : getIsDelete().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", orderNo=").append(orderNo);
        sb.append(", orderName=").append(orderName);
        sb.append(", driverNo=").append(driverNo);
        sb.append(", productTotal=").append(productTotal);
        sb.append(", orderCount=").append(orderCount);
        sb.append(", sellerCount=").append(sellerCount);
        sb.append(", buyerCount=").append(buyerCount);
        sb.append(", orderStatus=").append(orderStatus);
        sb.append(", reason=").append(reason);
        sb.append(", takeQrCode=").append(takeQrCode);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", isDelete=").append(isDelete);
        sb.append("]");
        return sb.toString();
    }
}