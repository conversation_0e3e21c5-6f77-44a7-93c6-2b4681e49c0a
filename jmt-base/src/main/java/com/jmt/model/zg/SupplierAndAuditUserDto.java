package com.jmt.model.zg;

import lombok.Data;

@Data
public class SupplierAndAuditUserDto {
    private Long userId;
    private Long auditUser;
    /**
     * 供应商编号
     */
    private String supplierno;

    /**
     * 供应商名称
     */
    private String suppliername;

    /**
     * 联系人
     */
    private String linkman;

    /**
     * 联系人手机号
     */
    private String telphone;

    /**
     * 国家
     */
    private String country;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 维度
     */
    private String latitude;

    /**
     * 供应商照片
     */
    private String supplierphoto;

    /**
     * 供应商营业执照
     */
    private String supplierlicense;

    /**
     * 经营类别
     */
    private Integer categoryid;

    /**
     * 星级 1-5 标识1-5个星
     */
    private Integer supplierstar;

    /**
     * 备注说明
     */
    private String remark;


}
