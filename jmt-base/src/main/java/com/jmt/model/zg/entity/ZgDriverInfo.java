package com.jmt.model.zg.entity;

import lombok.Data;

import java.util.Date;

/**
 * 筷圣直供配送员信息表
 * @TableName zg_driver_info
 */
@Data
public class ZgDriverInfo {
    /**
     *
     */
    private Long id;

    /**
     * zg_user的id
     */
    private Long userId;

    /**
     * 配送员编号
     */
    private String driverNo;

    /**
     * 配送员姓名
     */
    private String driverName;

    /**
     * 身份证号
     */
    private String idNo;

    /**
     * 手机号
     */
    private String telPhone;

    /**
     * 区县
     */
    private String area;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 维度
     */
    private String latitude;

    /**
     * 配送范围
     */
    private String areaScope;

    /**
     * 车辆正面照片
     */
    private String carPhotoFront;

    private String carPhotoBack;

    /**
     * 车牌号
     */
    private String carNumber;

    /**
     * 车辆最大载重，单位KG
     */
    private Integer maxLoad;

    /**
     * 星级 1-5 标识1-5个星
     */
    private Integer driverStar;

    /**
     * 备注说明
     */
    private String remark;

    /**
     * 0-待审核
     1-同意
     2-驳回
     3-冻结
     */
    private Integer auditStatus;

    /**
     * 审核意见
     */
    private String reason;

    /**
     * 是否已实名认证 0-否 1-是
     */
    private Integer isRealAuth;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除  0-否 1-是
     */
    private Integer isDelete;

}