package com.jmt.model.page;

import lombok.Data;

import java.io.Serializable;

import lombok.Data;
import javax.validation.constraints.*;
/**
 * 分页+查询条件请求类
 * <AUTHOR>
 */
@Data
public class PageQuery<T> {
    @Min(value = 1, message = "页码不能小于1")
    private Integer pageNo = 1;

    @Min(value = 1, message = "每页数量不能小于1")
    @Max(value = 100, message = "每页数量不能超过100")
    private Integer pageSize = 10;

    private T queryData;
}