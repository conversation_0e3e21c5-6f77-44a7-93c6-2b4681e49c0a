package com.jmt.model.page;

import com.github.pagehelper.Page;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
/**
 * 分页请求返回实体类
 * <AUTHOR>
 */
@Data
public class PageResult<T> implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = 1L;
    private Long total = 0L;        //总记录数
    private List<T> list = new ArrayList<>();    //结果集
    private Integer pageNo;    // 第几页
    private Integer pageSize;    // 每页记录数
    private Integer pages = 0;        // 总页数
    private Integer size = 0;        // 当前页的数量 <= pageSize，该属性来自ArrayList的size属性

    /**
     * 包装Page对象，因为直接返回Page对象，在JSON处理以及其他情况下会被当成List来处理，
     * 而出现一些问题。
     * @param list  page结果
     */
    public PageResult(List<T> list) {
        if (list instanceof Page) {
            Page<T> page = (Page<T>) list;
            this.pageNo = page.getPageNum();
            this.pageSize = page.getPageSize();
            this.total = page.getTotal();
            this.pages = page.getPages();
            this.list = page;
            this.size = page.size();
        } else {
            this.list = list;
        }
    }

    public PageResult(Page<?> page, List<T> list) {
        this.pageNo = page.getPageNum();
        this.pageSize = page.getPageSize();
        this.total = page.getTotal();
        this.pages = page.getPages();
        this.list = list;
        this.size = list.size();
    }
    public PageResult() {
    }
}

