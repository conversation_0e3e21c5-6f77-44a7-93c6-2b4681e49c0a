package com.jmt.model.jhh.vo;

import com.jmt.model.uaa.UaaUserAddress;
import com.jmt.model.uaa.UaaUserBankcard;
import lombok.Data;

@Data
public class JhhUserInfoVo {
    //收益人id
    private Long id;
    //uaaid
    private Long uaaId;
    //用户名称
    private String userName;
    //用户编号
    private String userNo;

    private String telPhone;
    //头像
    private String headImg;
    //合约编号
    private String contractNo;
    //职位名称
    private String dutyName;

    private Integer userType;

    /**
     * 国家
     */
    private String country;
    /**
     * 省份
     */
    private String province;
    /**
     * 城市
     */
    private String city;
    /**
     * 区县
     */
    private String area;

    //经营地址
    private String address;

    //二维码
    private String qrCode;

    //默认收货地址
    private UaaUserAddress defaultAddress = new UaaUserAddress();

    //默认收货地址
    private UaaUserBankcard defaultBankcard = new UaaUserBankcard();
}
