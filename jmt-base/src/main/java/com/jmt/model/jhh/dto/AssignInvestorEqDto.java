package com.jmt.model.jhh.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class AssignInvestorEqDto {
    @NotBlank(message = "设备收益人编号不能为空")
    private String investorNo; // 设备收益人编号（对应 jhh_investor_info 的 investorNo）

    @NotEmpty(message = "设备编号列表不能为空")
    private List<String> eqNos; // 待分配的设备编号列表（对应 eq_info 的 eqNo）

}
