package com.jmt.model.jhh.vo;

import com.jmt.model.jhh.dto.ShareConfig;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class OperatorVo {

    private Long id;

    private Long uaaId;

    private String operatorName;

    private String operatorNo;

    private String headImg;

    private String contractNo;

    private String dutyName;

    private String telPhone;

    private Integer isProfitLimit;

    private Integer limitAmount;

    private String contractPicUrl;

    private String licensePicUrl;

    private Integer auditStatus;

    private String reason;

    private Integer isFirstLogin;

    /**
     * 国家
     */
    private String country;
    /**
     * 省份
     */
    private String province;
    /**
     * 城市
     */
    private String city;
    /**
     * 区县
     */
    private String area;

    //经营地址
    private String address;

    private Date startTime;

    private Date endTime;

    private Date createTime;

    private Date updateTime;

    private Integer isDelete;

    private OperatorStatInfo operatorStatInfo = new OperatorStatInfo();

    private List<ShareConfig> shareConfigs = new ArrayList<>();
}
