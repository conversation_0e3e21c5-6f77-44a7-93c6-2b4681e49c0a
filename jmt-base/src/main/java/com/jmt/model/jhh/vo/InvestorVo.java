package com.jmt.model.jhh.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class InvestorVo {

    /**
     * 经营地址
     */
    private String address;
    /**
     * 区县
     */
    private String area;
    /**
     * 状态
     * 0-待审核
     * 1-同意
     * 2-驳回
     * 3-冻结
     * 4-正常
     */
    private Integer auditStatus;
    /**
     * 城市
     */
    private String city;
    /**
     * 合同编号
     */
    private String contractNo;
    /**
     * 合同扫描件图片URL
     */
    private String contractPicUrl;
    /**
     * 国家
     */
    private String country;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 企业信用编码
     */
    private String creditCode;
    /**
     * 负责人姓名
     */
    private String dutyName;
    /**
     * 合同结束时间
     */
    private Date endTime;
    /**
     * 分配设备数量
     */
    private Integer eqNum;
    /**
     * 头像Url
     */
    private String headImg;
    /**
     * 主键
     */
    private Long id;
    /**
     * 身份证反面照
     */
    private String idBack;
    /**
     * 身份证正面照
     */
    private String idFront;
    /**
     * 身份证号
     */
    private String idNo;
    /**
     * 投资金额
     */
    private BigDecimal investAmount;
    /**
     * 设备收益人名称
     */
    private String investorName;
    /**
     * 设备收益人编号
     */
    private String investorNo;
    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDelete;
    /**
     * 是否首次登录 0-否 1-是
     */
    private Integer isFirstLogin;
    /**
     * 分润是否封顶 0-否 1-是
     */
    private Integer isProfitLimit;
    /**
     * 营业执照图片URL
     */
    private String licensePicUrl;
    /**
     * 当分润封顶时必填
     */
    private BigDecimal limitAmount;
    /**
     * 收益人性质 0-个人 1-品牌方（企业）
     */
    private Integer nature;
    /**
     * 运营商编号
     */
    private String operatorNo;
    /**
     * 省份
     */
    private String province;
    /**
     * 审批意见
     */
    private String reason;
    /**
     * 合作开始时间
     */
    private Date startTime;
    /**
     * 手机号
     */
    private String telPhone;
    /**
     * 统一账号uaaId
     */
    private Long uaaId;
    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 审核人uaaId
     */
    private Long auditUser;

    /**
     * 审核人名称
     */
    private String auditUserName;

    /**
     * 审核时间
     */
    private Date auditTime;

    InvestorProfitVo investorProfit;
}
