package com.jmt.model.jhh.vo;

import lombok.Data;

@Data
public class SalesmanVo {

    private Long id;

    private Long uaaId;

    private String salesmanName;

    private String salesmanNo;

    private String idNo;

    private String headImg;

    private Integer salesmanType;

    private String operatorNo;

    private String dutyName;

    private String telPhone;

    private Integer isFirstLogin;

    private Integer fStatus;

    private Integer isDelete;

    private Integer busANum = 0;

    private Integer eqANum = 0;

    private Integer eqBNum = 0;

    private Integer eqCNum = 0;

    /**
     * 国家
     */
    private String country;
    /**
     * 省份
     */
    private String province;
    /**
     * 城市
     */
    private String city;
    /**
     * 区县
     */
    private String area;

    //经营地址
    private String address;
}
