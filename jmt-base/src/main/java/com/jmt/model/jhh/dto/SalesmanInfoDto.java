package com.jmt.model.jhh.dto;

import lombok.Data;


@Data
public class SalesmanInfoDto {

    private Long id;

    private String salesmanName;

    private String salesmanNo;

    private String password;

    private String headImg;

    private Integer salesmanType;

    private String idNo;

    private String dutyName;

    private String telPhone;

    /**
     * 国家
     */
    private String country;
    /**
     * 省份
     */
    private String province;
    /**
     * 城市
     */
    private String city;
    /**
     * 区县
     */
    private String area;

    //经营地址
    private String address;
}
