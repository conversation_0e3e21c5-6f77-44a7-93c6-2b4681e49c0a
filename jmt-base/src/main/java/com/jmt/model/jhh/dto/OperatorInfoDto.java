package com.jmt.model.jhh.dto;

import lombok.Data;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class OperatorInfoDto {

    private Long id;

    private String operatorName;

    private String operatorNo;

    private String headImg;

    private String contractNo;

    private String dutyName;

    private String telPhone;

    private Integer isProfitLimit;

    private Integer limitAmount;

    private String contractPicUrl;

    private String licensePicUrl;

    private String reason;

    /**
     * 国家
     */
    private String country;
    /**
     * 省份
     */
    private String province;
    /**
     * 城市
     */
    private String city;
    /**
     * 区县
     */
    private String area;

    //经营地址
    private String address;

    private Date startTime;

    private Date endTime;

    private List<ShareConfig> shareConfigs = new ArrayList<>();
}
