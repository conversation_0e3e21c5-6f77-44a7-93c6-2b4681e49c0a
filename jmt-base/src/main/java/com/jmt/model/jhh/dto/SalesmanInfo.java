package com.jmt.model.jhh.dto;

import lombok.Data;

import java.util.Date;

@Data
public class SalesmanInfo {

    private Long id;

    private Long uaaId;

    private String salesmanName;

    private String salesmanNo;

    private String idNo;

    private String headImg;

    private Integer salesmanType;

    private String operatorNo;

    private String dutyName;

    private String telPhone;

    private Integer isFirstLogin;

    private Integer fStatus;

    private Date createTime;

    private Date updateTime;

    private Integer isDelete;

    /**
     * 国家
     */
    private String country;
    /**
     * 省份
     */
    private String province;
    /**
     * 城市
     */
    private String city;
    /**
     * 区县
     */
    private String area;

    //经营地址
    private String address;
}
