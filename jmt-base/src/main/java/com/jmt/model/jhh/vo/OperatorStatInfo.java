package com.jmt.model.jhh.vo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class OperatorStatInfo {
    /**
     * 托管账号数量
     */
    private Integer proxyNum = 0;
    /**
     * 业务员数量
     */
    private Integer salesManNum = 0;
    /**
     * 收益人数量
     */
    private Integer investorNum = 0;
    /**
     * 品牌孵化数量
     */
    private Integer brandMemberNum = 0;
    /**
     * 品牌联盟数量
     */
    private Integer brandLeagueNum = 0;
    /**
     * 待审核商家数量
     */
    private Integer applyBusNum = 0;
    /**
     * 审核通过商家数量
     */
    private Integer approvedBusNum = 0;
    /**
     * 设备总预定量
     */
    private Integer orderEqNum = 0;
    /**
     * 已部署设备量
     */
    private Integer deployedEqNum = 0;
    /**
     * 设备在线数量
     */
    private Integer onlineEqNum = 0;
    /**
     * 设备离线数量
     */
    private Integer offlineEqNum = 0;
    /**
     * 设备送发货数量
     */
    private Integer sendEqTotalNum = 0;
    /**
     * 设备库存数量
     */
    private Integer stockEqNum = 0;
    /**
     * 提现笔数
     */
    private Integer applyCashNum = 0;
    /**
     * 可提现余额
     */
    private BigDecimal balance = new BigDecimal("0.00");
    /**
     * 剩余积分
     */
    private Integer totalPoints = 0;
    /**
     * 预收益积分
     */
    private Integer preProfitPoints = 0;

}
