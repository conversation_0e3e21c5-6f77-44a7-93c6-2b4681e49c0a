package com.jmt.model.admin.user;

import lombok.Data;

import java.util.Date;

/**
 * 系统用户角色关系表
 * @TableName sys_user_role
 */
@Data
public class SysUserRole {
    /**
     * 主键
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 角色ID
     */
    private Long roleId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDelete;




}