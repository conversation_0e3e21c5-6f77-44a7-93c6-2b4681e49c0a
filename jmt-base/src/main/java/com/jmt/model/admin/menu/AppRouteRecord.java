package com.jmt.model.admin.menu;

import lombok.Data;

import java.util.List;

/**
 * 应用路由记录模型
 * 用于定义应用的路由配置信息，包含路由的基本属性、元信息及子路由，
 * 通常用于前端路由系统（如Vue Router、React Router）的配置映射
 */
@Data
public class AppRouteRecord {

    /**
     * 路由唯一标识ID
     * 用于区分不同的路由配置，在权限控制、路由管理中作为唯一键
     */
    private Long id;

    /**
     * 路由名称
     * 通常与组件名称对应，用于路由的标识和引用（如路由跳转时的名称匹配）
     */
    private String name;

    /**
     * 路由路径
     * 前端访问该路由的URL路径（如"/dashboard"），用于路由匹配
     */
    private String path;

    /**
     * 路由元信息
     * 存储路由的附加信息（如标题、图标、权限等），由RouteMeta类定义
     */
    private RouteMeta meta;

    /**
     * 路由对应组件路径
     * 指向该路由所渲染的组件文件路径（如"views/Dashboard/index.vue"）
     */
    private String component;

    /**
     * 子路由列表
     * 嵌套路由配置，形成路由树结构（如菜单的子菜单对应的路由）
     */
    private List<AppRouteRecord> children;
}