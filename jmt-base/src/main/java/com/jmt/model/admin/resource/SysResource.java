package com.jmt.model.admin.resource;

import lombok.Data;

import java.util.Date;

/**
 * 系统资源表
 * @TableName sys_resource
 */
@Data
public class SysResource {
    /**
     * 主键
     */
    private Long id;

    /**
     * 资源编码
     */
    private String resKey;

    /**
     * 资源名称
     */
    private String resName;

    /**
     * 资源接口URL
     */
    private String resUrl;
    /**
     * 服务名称
     */
    private String serverName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDelete;


}