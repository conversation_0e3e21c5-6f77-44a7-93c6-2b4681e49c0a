package com.jmt.model.admin.permission;

import lombok.Data;

import java.util.Date;

/**
 * 系统权限资源关系表
 * 用于建立权限与资源（如API接口）的多对多关联，记录哪些权限可以访问哪些资源
 * @TableName sys_permission_resource
 */
@Data
public class SysPermissionResource {
    /**
     * 主键ID
     * 权限与资源关联记录的唯一标识，自增主键
     */
    private Long id;

    /**
     * 权限ID
     * 关联sys_permission表的主键，标识当前关联的权限
     */
    private Long permissId;

    /**
     * 资源ID
     * 关联sys_resource表的主键，标识当前权限可访问的资源（如API接口）
     * （修正原"角色ID"注释错误，应为资源ID）
     */
    private Long resourceId;

    /**
     * 创建时间
     * 关联关系的创建时间，默认自动填充当前时间
     */
    private Date createTime;

    /**
     * 更新时间
     * 关联关系的最后更新时间，每次修改时自动更新为当前时间
     */
    private Date updateTime;

    /**
     * 是否删除
     * 逻辑删除标识：0-未删除（正常关联），1-已删除（解除权限与资源的关联）
     */
    private Integer isDelete;
}