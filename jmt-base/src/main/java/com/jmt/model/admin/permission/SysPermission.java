package com.jmt.model.admin.permission;

import lombok.Data;

import java.util.Date;

/**
 * 系统权限表
 * 存储系统中所有权限元数据，支持通过父权限ID构建层级关系，覆盖模块、菜单、按钮等全粒度权限
 * @TableName sys_permission
 */
@Data
public class SysPermission {
    /**
     * 主键ID
     * 权限记录唯一标识，自增主键
     */
    private Long id;

    /**
     * 权限编码
     * 权限唯一标识，采用"模块:资源:操作"格式（如sys:user:add），用于权限校验逻辑
     */
    private String permissKey;

    /**
     * 权限名称
     * 权限展示名称，用于前端界面显示（如"用户新增权限"）
     */
    private String permissName;
    private String routerName;
    private String routerPath;
    private String routerComponent;
    private String routerIcon;

    private Integer listSort;

    /**
     * 父权限ID
     * 用于构建权限树形结构：
     * - 顶级权限父ID为0
     * - 子权限通过父ID关联上级权限，形成层级关系
     */
    private Long parentId;

    /**
     * 权限类型
     * 区分权限粒度与作用范围：
     * 1-模块（如"系统管理"）
     * 2-菜单（如"用户管理"）
     * 3-选项卡（如"用户列表"）
     * 4-页面按钮（如"新增"按钮）
     * 5-表格按钮（如"编辑"按钮）
     */
    private Integer permissType;

    /**
     * 创建时间
     * 权限记录创建时间，默认自动填充当前时间
     */
    private Date createTime;

    /**
     * 更新时间
     * 权限记录最后更新时间，每次修改自动更新为当前时间
     */
    private Date updateTime;

    /**
     * 是否删除
     * 逻辑删除标识：
     * 0-未删除（正常生效）
     * 1-已删除（废弃权限）
     */
    private Integer isDelete;
}