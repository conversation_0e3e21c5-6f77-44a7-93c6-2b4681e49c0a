package com.jmt.model.admin.resource;

import lombok.Data;

import java.util.Date;

/**
 * 经营类别
 * @TableName sys_business_category
 */
@Data
public class SysBusinessCategory {
    /**
     * 主键
     */
    private Long id;

    /**
     * 行业类别编码
     */
    private String categoryNo;

    /**
     * 行业类别名称
     */
    private String categoryName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDelete;


}