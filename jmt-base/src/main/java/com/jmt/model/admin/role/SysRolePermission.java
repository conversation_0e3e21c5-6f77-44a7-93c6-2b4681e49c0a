package com.jmt.model.admin.role;

import lombok.Data;

import java.util.Date;

/**
 * 系统角色权限关系表
 * @TableName sys_role_permission
 */
@Data
public class SysRolePermission {
    /**
     * 主键
     */
    private Long id;

    /**
     * 权限ID
     */
    private Long permissId;

    /**
     * 角色ID
     */
    private Long roleId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDelete;


}