package com.jmt.model.admin.menu;

import lombok.Data;
import java.util.List;

/**
 * 路由元信息模型
 * 存储路由的附加配置信息，包括显示设置、权限控制、页面行为等非核心路由属性，
 * 用于控制路由在前端的展示形式和访问权限
 */
@Data
public class RouteMeta {
    /**
     * 路由标题
     * 用于在菜单、标签页等位置显示的文本名称（通常支持国际化）
     */
    private String title;

    /**
     * 路由图标
     * 菜单中显示的图标标识（如FontAwesome图标类名）
     */
    private String icon;

    /**
     * 是否显示徽章
     * 用于在菜单旁显示小红点等提示标识，默认不显示（false）
     */
    private Boolean showBadge = false;

    /**
     * 文本徽章内容
     * 当showBadge为true时，显示的具体文本（如"New"、"3"）
     */
    private String showTextBadge;

    /**
     * 是否隐藏路由
     * 控制路由是否在菜单中显示（true为隐藏，false为显示），不影响路由跳转
     */
    private Boolean isHide = false;

    /**
     * 标签页是否隐藏
     * 控制路由对应的页面在标签页中是否显示（true为不显示标签）
     */
    private Boolean isHideTab = false;

    /**
     * 外部链接地址
     * 若路由需要跳转到外部链接，填写该地址（如"https://example.com"）
     */
    private String link;

    /**
     * 是否为iframe嵌入
     * true表示该路由通过iframe嵌入外部页面，此时link字段为嵌入地址
     */
    private Boolean isIframe = false;

    /**
     * 是否缓存页面
     * true表示路由对应的组件会被缓存（如使用keep-alive），避免重复渲染
     */
    private Boolean keepAlive = false;

    /**
     * 操作权限列表
     * 该路由包含的具体操作权限项（如"新增"、"删除"），由AuthItem类定义
     */
    private List<AuthItem> authList;

    /**
     * 是否为一级菜单
     * 用于区分路由在菜单中的层级，true表示为顶级菜单
     */
    private Boolean isFirstLevel = false;

    /**
     * 角色权限列表
     * 允许访问该路由的角色标识集合（如["admin", "editor"]）
     */
    private List<String> roles;

    /**
     * 是否固定标签页
     * true表示该路由对应的标签页不可关闭
     */
    private Boolean fixedTab = false;

    /**
     * 激活路径
     * 用于菜单高亮匹配，当路由路径与该值匹配时，对应的菜单高亮显示
     */
    private String activePath;

    /**
     * 是否为全屏页面
     * true表示该路由页面以全屏模式展示（不显示侧边栏、导航栏等）
     */
    private Boolean isFullPage = false;

    /**
     * 内部类：操作权限项
     * 定义路由下具体的操作权限，用于权限粒度控制
     */
    @Data
    public static class AuthItem {
        /**
         * 权限标题
         * 操作权限的显示名称（如"新增用户"）
         */
        private String title;

        /**
         * 权限标识
         * 权限的唯一键（如"user:add"），用于后端权限校验和前端按钮权限控制
         */
        private String authMark;
    }
}