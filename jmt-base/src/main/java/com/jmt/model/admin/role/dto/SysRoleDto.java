package com.jmt.model.admin.role.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 系统角色表
 * @TableName sys_role
 */
@Data
public class SysRoleDto {
    /**
     * 主键
     */
    private Long id;

    /**
     * 角色编码
     */
    private String roleKey;

    /**
     * 角色名称
     */
    private String roleName;

    private List<Long> permissionIds = new ArrayList<>();
}