package com.jmt.model.admin.role;

import lombok.Data;

import java.util.Date;

/**
 * 系统角色表
 * @TableName sys_role
 */
@Data
public class SysRole {
    /**
     * 主键
     */
    private Long id;

    /**
     * 角色编码
     */
    private String roleKey;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDelete;


}