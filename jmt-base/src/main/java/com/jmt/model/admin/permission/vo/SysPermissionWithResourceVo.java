package com.jmt.model.admin.permission.vo;

import com.jmt.model.admin.resource.vo.SysResourceVo;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class SysPermissionWithResourceVo {
    private Long id;
    private String permissKey;
    private String permissName;
    private String routerName;
    private String routerPath;
    private String routerComponent;
    private String routerIcon;
    private Integer listSort;
    private Long parentId;
    private Integer permissType;
    List<SysResourceVo> resources = new ArrayList<>();
}