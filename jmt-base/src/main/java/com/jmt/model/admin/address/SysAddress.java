package com.jmt.model.admin.address;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 地址信息实体类，对应数据库 sys_address 表
 */
@Data
public class SysAddress {
    /**
     * 主键，自动递增
     */
    private Long id;

    /**
     * 地域类型01-省,02-市,03-区/县
     */
    private String placeType;

    /**
     * 地域代码
     */
    private String placeCode;

    /**
     * 地域名称
     */
    private String placeName;

    /**
     * 上级地域代码,省级无上一级地域代码,市的上级代码为所属省
     */
    private String upPlaceName;

    /**
     * 邮编
     */
    private String zipCode;

    /**
     * 是否删除
     */
    private String isDelete;

    /**
     * 是否展示
     */
    private String isDisplay;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 版本号
     */
    private String version;
}