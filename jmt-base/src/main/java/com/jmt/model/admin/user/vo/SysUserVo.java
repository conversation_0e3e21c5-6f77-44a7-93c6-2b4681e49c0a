package com.jmt.model.admin.user.vo;

import com.jmt.model.admin.role.vo.SysRoleVo;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class SysUserVo {

    private Long id;

    private  String userName;

    private  String loginName;

    private  String telPhone;

    private String password;

    private Long uaaId;

    private Integer isFirstLogin;

    private Date createTime;

    private Date updateTime;

    private Integer isDelete;

    private List<SysRoleVo> userRoles = new ArrayList<>();
}
