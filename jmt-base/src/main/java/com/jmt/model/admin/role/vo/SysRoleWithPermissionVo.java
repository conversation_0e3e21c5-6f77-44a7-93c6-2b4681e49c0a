package com.jmt.model.admin.role.vo;

import com.jmt.model.admin.permission.vo.SysPermissionVo;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class SysRoleWithPermissionVo {
    /**
     * 主键
     */
    private Long id;

    /**
     * 角色编码
     */
    private String roleKey;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDelete;

    private List<SysPermissionVo> rolePermissions = new ArrayList<>();
}