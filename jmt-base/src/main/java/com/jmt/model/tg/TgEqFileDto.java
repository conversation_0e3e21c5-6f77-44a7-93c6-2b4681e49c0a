package com.jmt.model.tg;

import lombok.Data;

import java.util.Date;

@Data
public class TgEqFileDto {
    /**
     * 主键
     */
    private Long id;
    /**
     * 广告用户编号
     */
    private String tgUserNo;
    /**
     * 素材上传人的统一账号的uaaId
     */
    private String upUser;
    /**
     * 素材名称
     */
    private String fileName;
    /**
     * 素材真实名称
     */
    private String fileRealName;
    /**
     * 素材地址，阿里云存储URL
     */
    private String fileUrl;
    /**
     * 素材大小，单位为B
     */
    private String fileSize;
    /**
     * 素材文件md5摘要
     */
    private String fileMd5;
    /**
     * 素材类型
     */
    private String fileType;
    /**
     * 素材视频时长，单位为秒
     */
    private String videoDuration;
    /**
     * 播放状态
     * 0-待播放
     * 1-播放中
     * 2-已播放
     */
    private String playStatus;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否删除  0-否 1-是
     */
    private String isDelete;
}
