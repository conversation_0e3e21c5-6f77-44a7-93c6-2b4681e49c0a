package com.jmt.model.tg;

import lombok.Data;

@Data
public class TgBusinessOrderDto {
    /**
     * 主键
     */
    private Long id;
    /**
     * 系统根据订单规则生成
     */
    private String orderNo;
    /**
     * 订单名称
     */
    private String orderName;
    /**
     * 商家编号
     */
    private String busNo;
    /**
     * 投放经办人账号，统一账号uaaId
     */
    private String handleUser;
    /**
     * 片位，播放序号
     */
    private String playPosition;
    /**
     * 商家频道素材ID，tg_app_file的id
     */
    private String fileId;
    /**
     * 播放状态
     * 0-待播放
     * 1-播放中
     * 2-已播放
     */
    private String playStatus;
    /**
     * 审核状态：
     * 0-待审核
     * 1-审核通过
     * 2-审核不通过
     */
    private String auditStatus;
    /**
     * 审核意见，审核不通必填
     */
    private String reason;
    /**
     * 更新时间
     */
    private String createTime;
    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * 是否删除  0-否 1-是
     */
    private String isDelete;

}
