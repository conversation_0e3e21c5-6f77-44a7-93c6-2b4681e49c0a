package com.jmt.model.tg;

import lombok.Data;

import java.util.Date;

/**
 * 广告用户表
 */

@Data
public class TgUserDto {
    /**
     * 主键
     */
    private Long id;
    /**
     * 统一账号
     */
    private String uaaId;
    /**
     * 广告用户编号
     */
    private String tgUserNo;
    /**
     * 广告用户名称
     */
    private String tgUserName;
    /**
     * 头像
     */
    private String headImg;
    /**
     * 昵称
     */
    private String nickname;
    /**
     * 性别 0-男 1-女
     */
    private String sex;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 手机
     */
    private String telPhone;
    /**
     * 国家
     */
    private String country;
    /**
     * 省份
     */
    private String province;
    /**
     * 城市
     */
    private String city;
    /**
     * 区县
     */
    private String area;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 身份类型
     * 0-普通
     * 1-孵化用户
     * 2-VIP
     * 3-时段代理商
     */
    private Integer roleType;
    /**
     * 是否首次登录 0-否 1-是
     */
    private Integer isFirstLogin;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否删除0-否 1-是
     */
    private Integer isDelete;





}
