package com.jmt.model.tg;

import lombok.Data;

@Data
public class TgEqOrderDetailDto {
    /**
     * 主键
     */
    private Long id;
    /**
     * 订单编号，tg_eq_order的orderNo
     */
    private String orderNo;
    /**
     * 广告用户编号
     */
    private String tgUserNo;
    /**
     * 设备广告素材id，tg_eq_file的id
     */
    private String fileId;
    /**
     * 广告素材地址URL
     */
    private String fileUrl;
    /**
     * 播放方式
     * 1-定时投播
     * 2-即时插播
     */
    private String playType;
    /**
     * 投放商家编号，jmt-cm.cm_business的busNo
     */
    private String busNo;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * 是否删除  0-否 1-是
     */
    private String isDelete;

}
