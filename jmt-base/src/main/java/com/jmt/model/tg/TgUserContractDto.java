package com.jmt.model.tg;

import lombok.Data;

import java.util.Date;

/**
 * 广告用户合同
 */
@Data
public class TgUserContractDto {
    /**
     * 主键
     */
    private Long id;
    /**
     * 合同编号
     */
    private String contractNo;
    /**
     * 合同编码
     */
    private String contractName;
    /**
     * 广告用户编号
     */
    private String tgUserNo;
    /**
     * 广告用户名称
     */
    private String tgUserName;
    /**
     * 合同经办人的统一账号的uaaId
     */
    private String handleUser;
    /**
     * 合同金额
     */
    private String amount;
    /**
     * 片源数
     */
    private String sourceNum;
    /**
     * 合同附件
     */
    private String contractFile;
    /**
     * 性别 0-男 1-女
     */
    private String content;
    /**
     * 合同开始时间
     */
    private Date contractStartTime;
    /**
     * 合同结束时间
     */
    private Date contractEndTime;
    /**
     * 更新时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否删除  0-否 1-是
     */
    private Integer isDelete;
}
