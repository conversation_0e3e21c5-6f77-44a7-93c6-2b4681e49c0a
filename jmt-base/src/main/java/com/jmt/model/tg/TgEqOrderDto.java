package com.jmt.model.tg;

import lombok.Data;

@Data
public class TgEqOrderDto {
    /**
     * 主键
     */
    private Long id;
    /**
     * 系统根据订单规则生成
     */
    private String orderNo;
    /**
     * 订单名称
     */
    private String orderName;
    /**
     * 广告用户编号
     */
    private String tgUserNo;
    /**
     * 投放经办人账号，统一账号uaaId
     */
    private String handleUser;
    /**
     * 投放方式
     * 1-定时投播
     * 2-即时插播
     */
    private String playType;
    /**
     * 投放开始时间，当playType=1时必填
     */
    private String playStartTime;
    /**
     * 投放结束时间，当playType=1时必填
     */
    private String playEndTime;
    /**
     * 设备广告素材ID，tg_eq_file的id
     */
    private String fileId;
    /**
     * 投放商家数量
     */
    private String playbusNum;
    /**
     * 投放商家设备数量
     */
    private String playEqNum;
    /**
     * 投放天数
     */
    private String playDays;
    /**
     * 支付片源数
     */
    private String sourceNum;
    /**
     * 支付方式
     * 0-积分支付
     * 1-余额支付
     * 2-微信支付
     * 3-支付宝
     */
    private String payType;
    /**
     * 支付金额
     */
    private String payAmount;
    /**
     * 支付状态
     * 0-未支付
     * 1-支付成功
     * 2-支付失败
     * 3-取消支付
     */
    private String payStauts;
    /**
     * 播放状态
     * 0-待播放
     * 1-播放中
     * 2-已播放
     */
    private String playStatus;
    /**
     * 审核状态：
     * 0-待审核
     * 1-审核通过
     * 2-审核不通过
     */
    private String auditStatus;
    /**
     * 审核意见，审核不通必填
     */
    private String reason;
    /**
     * 更新时间
     */
    private String createTime;
    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * 是否删除  0-否 1-是
     */
    private String isDelete;
}
