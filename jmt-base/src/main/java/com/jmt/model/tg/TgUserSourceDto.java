package com.jmt.model.tg;

import lombok.Data;

import java.util.Date;

@Data
public class TgUserSourceDto {
    /**
     * 主键
     */
    private Long id;
    /**
     * 系统根据订单规则生成
     */
    private String orderNo;
    /**
     * 系统根据订单规则生成
     */
    private String orderName;
    /**
     * 合同编号
     */
    private String contractNo;
    /**
     * 广告用户编号
     */
    private String tgUserNo;
    /**
     * 充值经办人的统一账号的uaaId
     */
    private String handleUser;
    /**
     * 充值片源数
     */
    private String sourceNum;
    /**
     * 审核状态：
     * 0-待审核
     * 1-同意
     * 2-驳回
     * 3-完成
     */
    private String auditStatus;
    /**
     * 审核意见，驳回必填
     */
    private String reason;
    /**
     * 更新时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否删除  0-否 1-是
     */
    private String isDelete;

}
