package com.jmt.model.uaa;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
* 统一认证账号表
* @TableName uaa_user
*/
@Data
public class UaaUser implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
    * 主键ID
    */
    @NotNull(message="[主键ID]不能为空")
    private Long uaaId;
    /**
    * 昵称
    */
    @Size(max= 64,message="编码长度不能超过64")
    @Length(max= 64,message="编码长度不能超过64")
    private String nickname;
    /**
    * 登录账号名
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String loginName;
    /**
    * 登录密码
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String password;
    /**
    * 是否激活 0-激活 1-未激活
    */
    private Integer activated;
    /**
    * 邮箱
    */
    @Size(max= 100,message="编码长度不能超过100")
    @Length(max= 100,message="编码长度不能超过100")
    private String email;
    /**
    * 性别 0-男 1-女
    */
    private Integer sex;
    /**
    * 头像
    */
    @Size(max= 256,message="编码长度不能超过256")
    @Length(max= 256,message="编码长度不能超过256")
    private String photo;
    /**
    * 手机号
    */
    @Size(max= 64,message="编码长度不能超过64")
    @Length(max= 64,message="编码长度不能超过64")
    private String telPhone;
    /**
    * 详细地址
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String address;
    /**
    * 国家
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String country;
    /**
    * 省份
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String province;
    /**
    * 城市
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String city;

    private String area;
    /**
    * 是否实名认证 0-未认证 1-已认证
    */
    private Integer isRealAuth;
    /**
    * 微信的unionId
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String unionId;
    /**
    * 创建时间
    */
    private Date createTime;
    /**
    * 更新时间
    */
    private Date updateTime;
    /**
    * 是否删除 0-否 1-是
    */
    private Integer isDelete;

    /**
     * 是否超级管理员 0-否 1-是
     */
    private Integer isRoot = 0;

}
