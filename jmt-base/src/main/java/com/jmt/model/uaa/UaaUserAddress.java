package com.jmt.model.uaa;


import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.util.Date;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
* 用户账号收货地址表
* @TableName uaa_user_address
*/
@Data
public class UaaUserAddress implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键ID
    */
    @NotNull(message="[主键ID]不能为空")
    private Long id;
    /**
    * uaaId
    */
    @NotNull(message="[uaaId]不能为空")
    private Long uaaId;
    /**
    * 联系人
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String linkName;
    /**
    * 手机号
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String telPhone;
    /**
    * 国家
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String country;
    /**
    * 省份
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String province;
    /**
    * 城市
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String city;
    /**
    * 详细地址
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String detailAddress;
    /**
    * 创建时间
    */
    private Date createTime;
    /**
    * 更新时间
    */
    private Date updateTime;
    /**
    * 是否删除 0-否 1-是
    */
    private Integer isDelete;

    /**
     * 是否默认 是否默认地址 0-否 1- 是；通过账号只能一个默认地址
     */
    private Integer isDefault;

}
