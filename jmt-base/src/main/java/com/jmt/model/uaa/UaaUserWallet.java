package com.jmt.model.uaa;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Date;

/**
* 用户账号钱包表
* @TableName uaa_user_wallet
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UaaUserWallet implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键ID
    */
    @NotNull(message="[主键ID]不能为空")
    private Long id;
    /**
    * uaaId
    */
    @NotNull(message="[uaaId]不能为空")
    private Long uaaId;
    /**
    * 余额
    */
    private BigDecimal balance;
    /**
    * 积分
    */
    private Integer points;
    /**
    * 创建时间
    */
    private Date createTime;
    /**
    * 更新时间
    */
    private Date updateTime;
    /**
    * 是否删除 0-否 1-是
    */
    private Integer isDelete;

    /**
     * 支付密码
     */
    private String payPassword;


}
