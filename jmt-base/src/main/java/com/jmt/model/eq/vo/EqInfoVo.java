package com.jmt.model.eq.vo;

import lombok.Data;

import java.util.Date;

/**
 * 设备信息表实体类
 * 对应表：eq_info
 */
@Data
public class EqInfoVo {
    /**
     * 主键
     */
    private Long id;
    /**
     * 设备编号
     */
    private String eqNo;
    /**
     * 设备类型，A类/B类/C类
     */
    private String eqType;
    /**
     * 设备状态，0-未激活/1-在线/2-离线
     */
    private Integer eqState;
    /**
     * 商家编号，关联 cm_business_info 的 busNo
     */
    private String busNo;
    /**
     * 门店编号，关联 cm_shop_info 的 shopNo
     */
    private String shopNo;

    private String shopName;
    /**
     * 运营商编号，关联 jhh_operator_info 的 operatorNo
     */
    private String operatorNo;
    /**
     * 设备收益人编号，关联 jhh_investor_info 的 investorNo
     */
    private String investorNo;
    /**
     * 部署业务员编号，关联 jhh_salesman_info 的 salesmanNo
     */
    private String deployNo;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否删除，0-否 1-是
     */
    private Integer isDelete;

    /**
     * 国家
     */
    private String country;
    /**
     * 省份
     */
    private String province;
    /**
     * 城市
     */
    private String city;
    /**
     * 区县
     */
    private String area;

    /**
     * 设备位置
     */
    private String address;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 维度
     */
    private String latitude;
}
