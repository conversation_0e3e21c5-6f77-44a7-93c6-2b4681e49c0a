
package com.jmt.model.auth;

import lombok.Data;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 登录用户信息
 * <AUTHOR>
 */
@Data
public class LoginUaaUser implements UserDetails {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private Long uaaId;

    /**
     * 客户端id
     */
    private String clientId;

    /**
     * 第三方平台认证id
     */
    private String unionId;

    /**
     * 登录账号
     */
    private String loginName;
    /**
     *昵称
     */
    private String nickname;
    /**
     *性别 0-男 1-女
     */
    private Integer sex;
    /**
     *邮箱
     */
    private String email;
    /**
     *手机号
     */
    private String telPhone;
    /**
     *密码
     */
    private String password;
    /**
     *是否激活 0-激活 1-未激活
     */
    private Integer activated;
    /**
     *头像
     */
    private String photo;
    /**
     *国家
     */
    private String country;
    /**
     *省
     */
    private String province;
    /**
     *城市
     */
    private String city;
    /**
     *区县
     */
    private String area;
    /**
     *详细地址
     */
    private String address;

    private String operatorNo;

    private String investorNo;

    private String salesmanNo;

    private String busNo;

    private String shopNo;

    private String tgUserNo;

    private String bdUserNo;

    /**
     *是否实名认证 0-未认证 1-已认证
     */
    private Integer isRealAuth;
    /**
     * 登录身份
     * 筷圣聚合伙移动端（客户端ID:jmt-jhh）：
     * 0-运营商
     * 1-设备受益人
     * 2-业务员
     * 其他客户端均填0
     */
    private Integer userType = 0;
    /**
     * 是否超级管理员 0-否 1-是
     */
    private Integer isRoot = 0;

    //拥有角色KEYs
    private List<String> roleKeys;

    //拥有资源URL
    private  List<String> resUrls;

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        final List<GrantedAuthority> authorities = new ArrayList<>();
        if (roleKeys != null) {
            for (final String role : roleKeys) {
                authorities.add(new SimpleGrantedAuthority(role));
            }
        }
        if (resUrls != null) {
            for (final String url : resUrls) {
                authorities.add(new SimpleGrantedAuthority(url));
            }
        }
        return authorities;
    }

    @Override
    public String getPassword() {
        return this.password;
    }

    @Override
    public String getUsername() {
        return this.getLoginName();
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return this.activated.equals(0);
    }
}
