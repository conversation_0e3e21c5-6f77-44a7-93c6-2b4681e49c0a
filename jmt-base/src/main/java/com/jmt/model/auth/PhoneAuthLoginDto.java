
package com.jmt.model.auth;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * 手机APP登录信息
 * <AUTHOR>
 */
@Data
public class PhoneAuthLoginDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     *客户端ID
     */
    @NotBlank(message="客户端ID不能为空")
    private String clientId;
    /**
     *开放身份ID
     */
    @NotBlank(message="开放身份ID不能为空")
    private String openId;
    /**
     *访问凭证
     */
    @NotBlank(message="访问凭证不能为空")
    private String accessToken;
    /**
     * 登录身份
     * 筷圣聚合伙移动端（客户端ID:jmt-jhh）：
     * 0-运营商
     * 1-设备受益人
     * 2-业务员
     * 其他客户端均填0
     */
    @NotNull(message="登录身份不能为空")
    private Integer userType = 0;

}
