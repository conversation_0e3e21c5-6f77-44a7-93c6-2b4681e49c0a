
package com.jmt.model.auth;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * 手机验证码登录信息
 * <AUTHOR>
 */
@Data
public class SmsCodeDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     *手机号
     */
    @NotNull(message="手机号不能为空")
    private String telPhone;
    /**
     * 0-登录验证
     * 1-修改密码
     */
    @NotNull(message="短信验证码类型不能为空")
    private Integer codeType = 0;

}
