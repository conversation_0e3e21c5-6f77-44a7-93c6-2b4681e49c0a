
package com.jmt.model.auth;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * 微信登录信息
 * <AUTHOR>
 */
@Data
public class WeiXinAuthLoginDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     *客户端ID
     */
    @NotBlank(message="客户端ID不能为空")
    private String clientId;
    /**
     *微信code临时授权码 由wx.login()提供
     */
    @NotBlank(message="微信code临时授权码不能为空")
    private String code;
    /**
     *初始化向量 由微信wx.getPhoneNumber提供
     */
    @NotBlank(message="微信初始化向量不能为空")
    private String iv;
    /**
     * 手机号码加密信息 由微信wx.getPhoneNumber()提供
     */
    @NotBlank(message="微信手机号码加密信息不能为空")
    private String encryptedData;
    /**
     * 登录身份
     * 筷圣聚合伙移动端（客户端ID:jmt-jhh）：
     * 0-运营商
     * 1-设备受益人
     * 2-业务员
     * 其他客户端均填0
     */
    @NotNull(message="登录身份不能为空")
    private Integer userType = 0;

}
