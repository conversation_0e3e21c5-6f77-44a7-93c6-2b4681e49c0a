
package com.jmt.model.auth;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * 手机验证码登录信息
 * <AUTHOR>
 */
@Data
public class SmsCodeAuthLoginDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     *客户端ID
     */
    @NotBlank(message="客户端ID不能为空")
    private String clientId;
    /**
     *手机号
     */
    @NotBlank(message="手机号不能为空")
    private String telPhone;
    /**
     *短信验证码
     */
    @NotBlank(message="手机短信验证码不能为空")
    private String smsCode;
    /**
     * 登录身份
     * 筷圣聚合伙移动端（客户端ID:jmt-jhh）：
     * 0-运营商
     * 1-设备受益人
     * 2-业务员
     * 其他客户端均填0
     */
    @NotNull(message="登录身份不能为空")
    private Integer userType = 0;

}
