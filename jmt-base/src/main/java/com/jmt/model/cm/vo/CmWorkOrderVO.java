package com.jmt.model.cm.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 返回所有工单
 */
@Data
public class CmWorkOrderVO implements Serializable {

    // ========================== 所有工单的公共字段 ==========================
    /**
     * 主键ID（各表通用）
     */
    private Long id;

    /**
     * 工单编号（各表含义不同：申请/部署/撤回/维护工单编号，通过tableSource区分）
     */
    private String workNo;

    /**
     * 商家编号（申请/部署/撤回/维护商家编号，通用）
     */
    private String busNo;

    /**
     * 商家名称（申请/部署/撤回/维护商家名称，通用）
     */
    private String busName;

    private String shopNo;

    private String shopName;

    /**
     * 联系人（通用）
     */
    private String linkman;

    /**
     * 联系人手机号（通用）
     */
    private String telPhone;

    /**
     * 国家（通用）
     */
    private String country;

    /**
     * 省份（通用）
     */
    private String province;

    /**
     * 城市（通用）
     */
    private String city;

    /**
     * 区县（通用）
     */
    private String area;

    /**
     * 详细地址（通用）
     */
    private String address;

    /**
     * 经度（通用）
     */
    private String longitude;

    /**
     * 纬度（通用，注意：原实体类“维度”为笔误，统一修正为“纬度”）
     */
    private String latitude;

    /**
     * 运营商编号（关联jhh_operator_info的operatorNo，通用）
     */
    private String operatorNo;

    /**
     * 推荐业务编号（关联jhh_salesman_info的salesmanNo，通用）
     */
    private String refereeNo;

    /**
     * 工单状态（各表状态值含义需注意差异，见字段注释）
     * - 申请工单（apply）：0-待审核、1-同意、2-驳回
     * - 部署/撤回/维护工单（deploy/recall/service）：0-待审核、1-同意、2-驳回、3-完成
     */
    private Integer workStatus;

    /**
     * 审核意见/驳回原因（通用，驳回时必填）
     */
    private String reason;

    /**
     * 创建时间（通用）
     */
    private Date createTime;

    /**
     * 更新时间（通用）
     */
    private Date updateTime;

    /**
     * 是否删除（0-否、1-是，通用）
     */
    private Integer isDelete;


    // ========================== 各工单的特有字段（非通用，不存在时为null） ==========================

    /**
     * 部署工单特有：关联的申请工单编号（仅CmWorkEqDeploy有，关联CmWorkEqApply的workNo）
     */
    private String applyWorkNo;

    /**
     * 部署/撤回/维护工单特有：部署业务编号（关联jhh_salesman_info的salesmanNo，CmWorkEqDeploy/Recall/Service有）
     */
    private String deployerNo;


    /**
     * 工单来源表标识（用于业务层区分工单类型）
     * - deploy：来自cm_work_eq_deploy（部署工单）
     * - recall：来自cm_work_eq_recall（撤回工单）
     * - service：来自cm_work_eq_service（维护工单）
     */
    private String tableSource;


}
