package com.jmt.model.cm.vo;

import lombok.Data;
import java.util.Date;

@Data
public class CmWorkEqApplyVO {

    private Long id;
    private String workNo;
    private String busNo;
    private String busName;
    private String linkman;
    private String telPhone;
    private String country;
    private String province;
    private String city;
    private String area;
    private String address;
    private String longitude;
    private String latitude;
    private String operatorNo;
    private String refereeNo;
    private Integer eqTotalNum;
    private Integer isProxy;
    private String remark;
    private Integer workStatus;
    private String reason;
    private Date createTime;
    private Date updateTime;

    /**
     * 业态名称
     */
    private String categoryName;

    /**
     * 门店照片
     */
    private String busPhoto;

    /**
     * 运营商名称
     */
    private String operatorName;

    /**
     * 业务员名称
     */
    private String salesmanName;

}
