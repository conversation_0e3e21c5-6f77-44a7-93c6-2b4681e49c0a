package com.jmt.model.cm.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class CmSupplyReqInfoDTO implements Serializable {
    private Long id;

    private Long userId;

    @NotNull(message = "商家编号不能为空")
    @Size(max = 255, message = "商家编号长度不能超过255")
    private String busNo;

    @Size(max = 255, message = "商家名称长度不能超过255")
    private String busName;

    @Size(max = 255, message = "联系人长度不能超过255")
    private String linkman;

    @Size(max = 255, message = "联系人手机号长度不能超过255")
    private String telPhone;

    @Size(max = 255, message = "国家长度不能超过255")
    private String country;

    @Size(max = 255, message = "省份长度不能超过255")
    private String province;

    @Size(max = 255, message = "城市长度不能超过255")
    private String city;

    @Size(max = 255, message = "区县长度不能超过255")
    private String area;

    @Size(max = 255, message = "详细地址长度不能超过255")
    private String address;

    @Size(max = 255, message = "经度长度不能超过255")
    private String longitude;

    @Size(max = 255, message = "纬度长度不能超过255")
    private String latitude;

    @Size(max = 255, message = "标题长度不能超过255")
    private String title;

    @Size(max = 65535, message = "内容长度不能超过65535")
    private String content;

    @Size(max = 255, message = "图片长度不能超过255")
    private String picture;

    @Size(max = 255, message = "视频长度不能超过255")
    private String video;

    private Integer scope;

    @NotNull(message = "信息类型不能为空")
    private Integer infoType;

    private Integer visitCount;

    private Integer contactCount;

    private Date beginTime;

    private Date endTime;

    private Integer auditStatus;

    @Size(max = 255, message = "原因长度不能超过255")
    private String reason;

    /**
     * 支付方式
     0-积分支付
     1-余额支付
     2-微信支付
     3-支付宝
     */
    private Integer payType;
    /**
     * 支付金额
     */
    private BigDecimal payAmount;

    private String password;

    private Date createTime;


    //时间选择
    private Integer timeScope;
}
