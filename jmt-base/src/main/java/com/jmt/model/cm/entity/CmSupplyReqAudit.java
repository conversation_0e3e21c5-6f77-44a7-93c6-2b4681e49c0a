package com.jmt.model.cm.entity;

import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.util.Date;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
* 筷圣餐谋供需信息审核记录
* @TableName cm_supply_req_audit
*/
@Data
public class CmSupplyReqAudit implements Serializable {

    /**
    * 主键
    */
    @NotNull(message="[主键]不能为空")
    private Long id;
    /**
    * 审核人统一账号uaaId
    */
    private Long auditUser;
    /**
    * 信息ID cm_supply_req_info的id
    */
    private Long infoId;
    /**
    * 状态 
0-待审核
1-同意
2-驳回
    */
    private Integer auditStatus;
    /**
    * 审核意见
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String reason;
    /**
    * 创建时间
    */
    private Date createTime;
    /**
    * 更新时间
    */
    private Date updateTime;
    /**
    * 是否删除 0-否 1-是
    */
    private Integer isDelete;



}
