package com.jmt.model.cm.vo;

import lombok.Data;

import java.util.Date;
@Data
public class CmInfoVo {
    private Long id;

    private String title;
    private Date createTime;
    private Integer visitCount;
    private Integer messageCount;
    private Integer dealCount;

    /**
     * 工单来源表标识（用于业务层区分工单类型）
     * - supply：来自供需表
     * - farming：来自农餐对接表
     * - sellout：来自卖光光表
     */
    private String sourceType;
}
