package com.jmt.model.cm.entity;

import lombok.Data;
import java.io.Serializable;
import java.util.Date;

@Data
public class CmBoxInfoPhoto implements Serializable {


    private Long id;
    /**
    * 包厢编号
    */
    private String boxNo;
    /**
    * 门店编号
    */
    private String shopNo;
    /**
    * 照片类型 0-环境照片 1-包厢照片
    */
    private Integer photoType;
    /**
    * 照片url
    */
    private String photoUrl;
    /**
    * 创建时间
    */
    private Date createTime;
    /**
    * 更新时间
    */
    private Date updateTime;
    /**
    * 是否删除 0-否 1-是
    */
    private Integer isDelete;


}
