package com.jmt.model.cm.entity;

import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.util.Date;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
* 筷圣餐谋商户卖光光信息表
* @TableName cm_sellout_info
*/
@Data
public class CmSelloutInfo implements Serializable {
    /**
     *
     */
    @NotNull(message="[]不能为空")
    private Long id;

    private Long userId;
    /**
     * 商家编号
     */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String busNo;
    /**
     * 商家名称
     */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String shopName;
    /**
     * 联系人
     */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String linkman;
    /**
     * 联系人手机号
     */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String telPhone;
    /**
     * 国家
     */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String country;
    /**
     * 省份
     */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String province;
    /**
     * 城市
     */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String city;
    /**
     * 区县
     */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String area;
    /**
     * 详细地址
     */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String address;
    /**
     * 经度
     */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String longitude;
    /**
     * 维度
     */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String latitude;
    /**
     * 标题
     */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String title;
    /**
     * 内容
     */
    @Size(max= -1,message="编码长度不能超过-1")
    @Length(max= -1,message="编码长度不能超过-1")
    private String content;
    /**
     * 图片
     */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String picture;
    /**
     * 视频
     */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String video;
    /**
     * 范围 单位km 如1km范围内
     */
    private Integer scope;

    private Integer selloutType;
    private Integer selloutDay;

    /**
     * 工单状态
     0-待审核
     1-同意
     2-驳回
     3-完成
     */
    private Integer auditStatus;
    /**
     * 驳回必填
     */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String reason;
    /**
     * 更新时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;

    private Date endTime;
    /**
     * 是否删除  0-否 1-是
     */
    private Integer isDelete;

    private Integer visitCount;
    private Integer messageCount;
    private Integer dealCount;


}
