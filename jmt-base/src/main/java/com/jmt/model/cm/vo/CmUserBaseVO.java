package com.jmt.model.cm.vo;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

@Data
public class CmUserBaseVO {
    /**
     *
     */
    @NotNull(message="[]不能为空")
    private Long id;
    /**
     * 统一账号uaaId
     */
    private Long uaaId;

    private Long parentId;
    /**
     * 姓名
     */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String userName;
    /**
     * 头像
     */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String headImg;
    /**
     * 昵称
     */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String nickName;
    /**
     * 性别 0-男 1-女
     */
    private Integer sex;
    /**
     * 手机
     */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String telPhone;
    /**
     * 邮箱
     */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String email;
    /**
     * 国家
     */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String country;
    /**
     * 省份
     */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String province;
    /**
     * 城市
     */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String city;
    /**
     * 区县
     */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String area;
    /**
     * 详细地址
     */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String address;
    /**
     * 身份类型
     0-游客
     1-商家

     */
    private Integer roleType;
    /**
     * 运营商编号
     jhh_operator_info的operatorNo
     */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String operatorNo;
    /**
     * 推荐业务编号
     jhh_salesman_info的salesmanNo
     */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String refereeNo;
    /**
     * 是否首次登录 0-否 1-是
     */
    private Integer isFirstLogin;

    private Integer isSupplier;
    /**
     * 更新时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否删除  0-否 1-是
     */
    private Integer isDelete;

    private String busNo;

    private String busName;

    private String shopNo;

    private String shopName;


}
