package com.jmt.model.cm.vo;

import com.jmt.model.cm.dto.FeatureDTO;
import lombok.Data;

import java.util.List;

@Data
public class CmBoxInfoVO {
    private Long id;

    private String boxNo;

    private String shopName;

    private Integer measure;

    private Integer capacity;

    private Integer busStar;

    private String cuisineType;

    private Integer comsumeAvg;

    private String servicePhone;

    //包厢照片
    private String boxPhoto;

    private Integer scope;

    private List<FeatureDTO> features;

}
