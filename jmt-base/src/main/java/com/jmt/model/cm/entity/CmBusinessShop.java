package com.jmt.model.cm.entity;

import lombok.Data;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
* 筷圣餐谋商家团队成员表
* @TableName cm_business_shop
*/
@Data
public class CmBusinessShop implements Serializable {

    /**
    * 主键
    */
    @NotNull(message="[主键]不能为空")
    private Long id;
    /**
    * cms_user的id
    */
    private Long userId;
    /**
    * 商家编码
    */
    private String busNo;
    /**
    * 门店编码
    */
    private String shopNo;
    /**
    * 门店名称
    */
    private String shopName;
    /**
    * 门店负责人
    */
    private String linkman;
    /**
    * 性别 0-男 1-女
    */
    private Integer sex;
    /**
    * 手机号
    */
    private String telPhone;
    /**
    * 国家
    */
    private String country;
    /**
    * 省份
    */
    private String province;
    /**
    * 城市
    */
    private String city;
    /**
    * 区县
    */
    private String area;
    /**
    * 详细地址
    */
    private String address;
    /**
    * 经度
    */
    private String longitude;
    /**
    * 维度
    */
    private String latiude;
    /**
    * 员工状态 0-在职 1-离职
    */
    private Integer memStatus;
    /**
    * 创建时间
    */
    private Date createTime;
    /**
    * 更新时间
    */
    private Date updateTime;
    /**
    * 是否删除 0-否 1-是
    */
    private Integer isDelete;
    /**
    * 运营商编号
jhh_operator_info的operatorNo
    */
    private String operatorNo;
    /**
    * 推荐业务编号
jhh_salesman_info的salesmanNo
    */
    private String refereeNo;
    /**
    * 部署业务员编号
jhh_salesman_info的salesmanNo
    */
    private String devloperNo;
    /**
    * 门店照片
    */
    private String shopPhoto;

    /**
     * 门店环境照片
     */
    private String shopInteriorPhoto;
    /**
    * 门店营业执照
    */
    private String shopLicense;
    /**
    * 店铺经营类别
    */
    private String categoryId;
    /**
    * 店铺面积
    */
    private Integer measure;
    /**
    * 圆桌数量
    */
    private Integer ydeskNum;
    /**
    * 方桌数量
    */
    private Integer fdeskNum;
    /**
    * 人均消费
    */
    private Integer comsumeAvg;
    /**
    * 营业时间
    */
    private String openTime;
    /**
    * 人流量
    */
    private Integer visitorAvg;
    /**
    * 星级 1-5 标识1-5个星
    */
    private Integer busStar;
    /**
    * 备注说明
    */
    private String remark;
    /**
    * 0-待审核
1-同意
2-驳回
3-冻结
    */
    private Integer auditStatus;
    /**
    * 审核意见
    */
    private String reason;

    private String creditCode;
    private String legalPerson;

}
