package com.jmt.model.cm.vo;

import com.jmt.model.jhh.vo.ShareProjectProfitShopVo;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class CmBusinessShopDetailVO {
    private Long id;

    private Long userId;

    private String busNo;

    private String shopNo;

    private String shopName;

    private String telPhone;
    private String linkman;

    private Date createTime;

    private String refereeNo;

    private String shopPhoto;

    private String shopLicense;

    private String categoryId;

    private String creditCode;

    private String legalPerson;

    private String province;

    private String city;

    private String area;
    private String address;

    private CmWorkEqApplyDetailVO cmWorkEqApplyDetailVO;

    private CmEqDeployDetailVO cmEqDeployDetailVO;

    private List<ShareProjectProfitShopVo> shareProjectProfitShopVoList;

    private MerchantPerformanceVO merchantPerformanceVO;

}
