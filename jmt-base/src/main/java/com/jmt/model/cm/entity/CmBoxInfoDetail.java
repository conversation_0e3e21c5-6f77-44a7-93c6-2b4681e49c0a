package com.jmt.model.cm.entity;


import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
*
* @TableName cm_box_info_detail
*/
@Data
public class CmBoxInfoDetail implements Serializable {

    /**
    * 主键
    */
    private Long id;
    /**
    * 包厢编号
    */
    private String boxNo;
    /**
    * 菜系/包厢环境
    */
    private String dataType;
    /**
    * 招牌菜/好视野
    */
    private String textLabel;
    /**
    * 创建时间
    */
    private Date createTime;
    /**
    * 更新时间
    */
    private Date updateTime;
    /**
    * 是否删除 0-否 1-是
    */
    private Integer isDelete;



}
