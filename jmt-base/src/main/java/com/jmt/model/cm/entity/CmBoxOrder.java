package com.jmt.model.cm.entity;

import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;
@Data
public class CmBoxOrder {
    private Long id;

    private Long infoId;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 订单名称
     */
    private String orderName;

    private Long userId;

    /**
     * 支付方式
     0-积分支付
     1-余额支付
     2-微信支付
     3-支付宝
     */
    private Integer payType;
    /**
     * 支付金额
     */
    private BigDecimal payAmount;
    /**
     * 订单状态
     0-未支付
     1-支付成功
     2-支付失败
     3-取消支付
     */
    private Integer orderStatus;
    /**
     * 驳回必填
     */
    private String reason;
    /**
     * 更新时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否删除  0-否 1-是
     */
    private Integer isDelete;
}
