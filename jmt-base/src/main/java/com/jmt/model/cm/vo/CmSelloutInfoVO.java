package com.jmt.model.cm.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
@Data
public class CmSelloutInfoVO implements Serializable {
    private Long id;
    private Long userId;
    private String title;
    private String content;
    private Integer auditStatus;
    private PubBusInfoVO pubBusInfoVO;
    private String picture;
    private String video;
    private Integer scope;
    private Integer selloutType;
    private Integer selloutDay;
    private Date createTime;
    private Date endTime;
    private Integer visitCount;
    //距离
    private Double distance;
}

