package com.jmt.model.cm.entity;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
* 商家管理查询条件
* @TableName cm_business_shop
*/
@Data
public class CmBusinessMange implements Serializable {
    //运营商编号
    private String operatorNo;
    //区域
    private String address;
    //日期
    private LocalDate createTime;

}
