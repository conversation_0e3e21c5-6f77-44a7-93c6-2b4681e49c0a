package com.jmt.model.cm.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* 筷圣餐谋商家经营类别
* @TableName cm_category
*/
@Data
public class CmCategory implements Serializable {

    /**
    * 主键
    */
    private Long id;
    /**
    * 父节点ID
    */
    private Long parentId;
    /**
    * 行业类别编码
    */
    private String categoryNo;
    /**
    * 行业类别名称
    */
    private String categoryName;
    /**
    * 是否需要设备 0-否 1-是
    */
    private Integer isNeedEq;
    /**
    * 是否需要团队 0-否 1-是
    */
    private Integer isNeedTeam;
    /**
    * 是否参与分润 0-否 1-是
    */
    private Integer isShareProfit;
    /**
    * 创建时间
    */
    private Date createTime;
    /**
    * 更新时间
    */
    private Date updateTime;
    /**
    * 是否删除 0-否 1-是
    */
    private Integer isDelete;

}
