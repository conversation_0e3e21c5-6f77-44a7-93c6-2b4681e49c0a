package com.jmt.model.cm.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CmBoxInfoDTO {
    private Long id;
    private String boxNo;

    private String shopNo;

    private String shopName;

    private Integer measure;

    private Integer capacity;

    private Integer busStar;

    private String cuisineType;

    private Integer comsumeAvg;

    private String servicePhone;

    private String shopPhoto;

    private String country;

    private String province;

    private String city;

    private String area;

    private String address;

    private String longitude;
    private String latiude;
    private Integer scope;

    // 特色菜和包厢环境数据
    private List<FeatureDTO> features;

    // 照片列表
    private List<BoxPhotoDTO> photos;


    /**
     * 支付方式
     0-积分支付
     1-余额支付
     2-微信支付
     3-支付宝
     */
    private Integer payType;
    /**
     * 支付金额
     */
    private BigDecimal payAmount;

    private String password;
}
