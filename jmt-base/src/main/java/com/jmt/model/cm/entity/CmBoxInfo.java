package com.jmt.model.cm.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* 
* @TableName cm_box_info
*/
@Data
public class CmBoxInfo implements Serializable {

    /**
    * 主键
    */
    private Long id;
    /**
    * 包厢编号
    */
    private String boxNo;
    /**
    * 门店编码
    */
    private String shopNo;
    /**
    * 门店名称
    */
    private String shopName;
    /**
    * 店铺面积
    */
    private Integer measure;
    /**
    * 容纳人数
    */
    private Integer capacity;
    /**
    * 星级 1-5 标识1-5个星
    */
    private Integer busStar;
    /**
    * 菜系
    */
    private String cuisineType;
    /**
    * 人均消费
    */
    private Integer comsumeAvg;
    /**
    * 包厢服务电话
    */
    private String servicePhone;
    /**
    * 门头照片
    */
    private String shopPhoto;

    /**
    * 国家
    */
    private String country;
    /**
    * 省份
    */
    private String province;
    /**
    * 城市
    */
    private String city;
    /**
    * 区县
    */
    private String area;
    /**
    * 详细地址
    */
    private String address;
    /**
    * 经度
    */
    private String longitude;
    /**
    * 纬度
    */
    private String latiude;
    private Integer scope;
    /**
    * 创建时间
    */
    private Date createTime;
    /**
    * 更新时间
    */
    private Date updateTime;
    /**
    * 是否删除 0-否 1-是
    */
    private Integer isDelete;



}
