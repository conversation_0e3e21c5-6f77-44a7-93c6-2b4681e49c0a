package com.jmt.model.cm.vo;

import lombok.Data;
import java.util.Date;
@Data
public class CmBusinessInfoVO {

    private Long id;

    private String busNo;

    private String busName;

    /**
     * 省份
     */
    private String province;
    /**
     * 城市
     */
    private String city;
    /**
     * 区县
     */
    private String area;

    /**
     * 推荐业务编号
     jhh_salesman_info的salesmanNo
     */
    private String refereeNo;

    /**
     * 商家照片
     */
    private String busPhoto;

    /**
     * 星级 1-5 标识1-5个星
     */
    private Integer busStar;

    /**
     * 0-待审核
     1-同意
     2-驳回
     3-冻结
     */
    private Integer auditStatus;

    /**
     * 创建时间
     */
    private Date createTime;

}
