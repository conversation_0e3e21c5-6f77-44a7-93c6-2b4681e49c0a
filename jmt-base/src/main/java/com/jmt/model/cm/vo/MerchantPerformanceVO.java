package com.jmt.model.cm.vo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class MerchantPerformanceVO {
    //信息发布数
    private Integer publishedCount;
    //发布总金额
    private BigDecimal postedAmount;
    //投流总笔数
    private Integer adCampaignsCount;
    //投流总金额
    private BigDecimal advertisementInvestment;
    //采购总笔数
    private Integer procurementCount;
    //采购总金额
    private BigDecimal procurementValue;
    //收益总积分
    private Integer incomeCredits;
    //现积分余额
    private Integer pointsBalance;
    //体现总笔数
    private Integer withdrawalCount;
    //可体现余额
    private BigDecimal withdrawalBalance;

    public MerchantPerformanceVO() {
        this.publishedCount = 0;
        this.postedAmount = BigDecimal.ZERO;
        this.adCampaignsCount = 0;
        this.advertisementInvestment = BigDecimal.ZERO;
        this.procurementCount = 0;
        this.procurementValue = BigDecimal.ZERO;
        this.incomeCredits = 0;
        this.pointsBalance = 0;
        this.withdrawalCount = 0;
        this.withdrawalBalance = BigDecimal.ZERO;
    }

}
