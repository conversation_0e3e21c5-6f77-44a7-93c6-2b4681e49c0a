package com.jmt.model.cm.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* 筷圣餐谋商家团队成员表
* @TableName cm_business_member
*/
@Data
public class CmBusinessMember implements Serializable {

    /**
    * 主键
    */

    private Long id;
    /**
    * 统一账号id uaa_user的uaaId
    */
    private Long uaaId;
    /**
    * 行业类别编码
    */
    private String busNo;
    /**
    * 行业类别名称
    */
    private String memName;
    /**
    * 性别 0-男 1-女
    */
    private Integer sex;
    /**
    * 手机号
    */
    private String telPhone;
    /**
    * 国家
    */
    private String country;
    /**
    * 省份
    */
    private String province;
    /**
    * 城市
    */
    private String city;
    /**
    * 区县
    */
    private String area;
    /**
    * 详细地址
    */
    private String address;
    /**
    * 经度
    */
    private String longitude;
    /**
    * 维度
    */
    private String latiude;
    /**
    * 员工状态 0-在职 1-离职
    */
    private Integer memStatus;
    /**
    * 创建时间
    */
    private Date createTime;
    /**
    * 更新时间
    */
    private Date updateTime;
    /**
    * 是否删除 0-否 1-是
    */
    private Integer isDelete;



}
