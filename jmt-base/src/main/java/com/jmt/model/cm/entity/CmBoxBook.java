package com.jmt.model.cm.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class CmBoxBook {

    /**
     * 主键
     */
    private Long id;

    private String boxNo;

    /**
     * 关联的包厢ID，外键关联到CmBoxInfo的id
     */
    private Long userId;

    /**
     * 就餐日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date diningDate;

    /**
     * 就餐时段：1-中餐，2-晚餐
     */
    private Integer diningPeriod;

    /**
     * 就餐人数
     */
    private Integer diningPeople;

    /**
     * 预定人姓名
     */
    private String linkman;

    /**
     * 预定人电话
     */
    private String telPhone;

    /**
     * 预定状态：0-待确认，1-已确认，2-已取消，3-已完成
     */
    private Integer reservationStatus;

    /**
     * 备注信息
     */
    private String remarks;




    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDelete;
}
