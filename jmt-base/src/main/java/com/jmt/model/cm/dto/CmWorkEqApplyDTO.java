package com.jmt.model.cm.dto;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class CmWorkEqApplyDTO {
    private Long id;

    @NotBlank(message = "商家编号不能为空")
    private String busNo;

    @NotBlank(message = "商家名称不能为空")
    private String busName;

    @NotBlank(message = "联系人不能为空")
    private String linkman;

    @NotBlank(message = "联系电话不能为空")
    private String telPhone;

    @NotBlank(message = "详细地址不能为空")
    private String address;

    private String operatorNo;
    private String refereeNo;
    private Integer isProxy;
    private String remark;

    @Valid
    @NotEmpty(message = "设备明细不能为空")
    private List<CmWorkEqApplyDetailDTO> details;

    private String openTime;

    private Integer measure;

    private Integer comsumeAvg;

    private Integer visitorAvg;

    private String shopNo;

    private String shopName;

    private String shopPhoto;

    private String shopInteriorPhoto;
}


