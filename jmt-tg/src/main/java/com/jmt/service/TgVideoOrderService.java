package com.jmt.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.jmt.model.dto.TgVideoOrderDto;
import com.jmt.dao.TgVideoOrderDao;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

@Service
public class TgVideoOrderService {

    @Resource
    private TgVideoOrderDao TgVideoOrderDao;


    public PageResult<TgVideoOrderDto> pageQuery(PageQuery<TgVideoOrderDto> pageQuery) {
        Page<TgVideoOrderDto> page = PageHelper.startPage(
                pageQuery.getPageNo(),
                pageQuery.getPageSize()
        ).doSelectPage(() -> TgVideoOrderDao.pageQuery(pageQuery.getQueryData()));
        return new PageResult<>(page);
    }


    public int add(TgVideoOrderDto dto) {
        return TgVideoOrderDao.add(dto);
    }


    public TgVideoOrderDto getById(Long id) {
        return TgVideoOrderDao.getById(id);
    }


    public int update(TgVideoOrderDto dto) {
        return TgVideoOrderDao.update(dto);
    }


    public int deleteById(Long id) {
        return TgVideoOrderDao.deleteById(id);
    }
}