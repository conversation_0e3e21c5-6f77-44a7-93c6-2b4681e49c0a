package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.dao.TgUserSourceDao;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.tg.TgUserSourceDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;

@Service
public class TgUserSourceService extends BaseService {
    @Resource
    private TgUserSourceDao tgUserSourceDao;

    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    public PageResult<TgUserSourceDto> getPage(PageQuery<TgUserSourceDto> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        TgUserSourceDto tgUserSourceDto = pageQuery.getQueryData();
        List<TgUserSourceDto> list = tgUserSourceDao.getPage(tgUserSourceDto);
        if (list != null) {
            return new PageResult<>(list);
        }else {
            return null;
        }
    }

    /**
     * 依据id查询
     * @param id
     * @return
     */
    public TgUserSourceDto getInfo(@Param("id") Long id) {
        return tgUserSourceDao.getInfo(id);
    }

    /**
     * 新增
     * @param tgUserSourceDto
     * @return
     */
    public Integer add(TgUserSourceDto tgUserSourceDto) {
        return tgUserSourceDao.insert(tgUserSourceDto);
    }

    /**
     * 依据id更新
     * @param tgUserSourceDto
     * @return
     */
    public Integer update(TgUserSourceDto tgUserSourceDto) {
        return tgUserSourceDao.update(tgUserSourceDto);
    }

    /**
     * 删除
     * @param id
     * @return
     */
    public Integer delete(@Param("id") Long id) {
        return tgUserSourceDao.delete(id);
    }

}
