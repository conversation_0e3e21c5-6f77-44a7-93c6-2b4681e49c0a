package com.jmt.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.jmt.dao.TgVideoPlayListDao;
import com.jmt.model.dto.TgVideoPlayListDto;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

@Service
public class TgVideoPlayListService {

    @Resource
    private TgVideoPlayListDao tgVideoPlayListDao;


    public PageResult<TgVideoPlayListDto> pageQuery(PageQuery<TgVideoPlayListDto> pageQuery) {
        Page<TgVideoPlayListDto> page = PageHelper.startPage(
                pageQuery.getPageNo(),
                pageQuery.getPageSize()
        ).doSelectPage(() -> tgVideoPlayListDao.pageQuery(pageQuery.getQueryData()));
        return new PageResult<>(page);
    }


    public int add(TgVideoPlayListDto dto) {
        return tgVideoPlayListDao.add(dto);
    }


    public TgVideoPlayListDto getById(Long id) {
        return tgVideoPlayListDao.getById(id);
    }


    public int update(TgVideoPlayListDto dto) {
        return tgVideoPlayListDao.update(dto);
    }


    public int deleteById(Long id) {
        return tgVideoPlayListDao.deleteById(id);
    }
}