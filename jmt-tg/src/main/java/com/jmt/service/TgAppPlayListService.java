package com.jmt.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.jmt.dao.TgAppPlayListDao;
import com.jmt.model.dto.TgAppPlayListDto;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

@Service
public class TgAppPlayListService {

    @Resource
    private TgAppPlayListDao tgAppPlayListDao;


    public PageResult<TgAppPlayListDto> pageQuery(PageQuery<TgAppPlayListDto> pageQuery) {
        Page<TgAppPlayListDto> page = PageHelper.startPage(
                pageQuery.getPageNo(),
                pageQuery.getPageSize()
        ).doSelectPage(() -> tgAppPlayListDao.pageQuery(pageQuery.getQueryData()));
        return new PageResult<>(page);
    }


    public int add(TgAppPlayListDto dto) {
        return tgAppPlayListDao.add(dto);
    }


    public TgAppPlayListDto getById(Long id) {
        return tgAppPlayListDao.getById(id);
    }


    public int update(TgAppPlayListDto dto) {
        return tgAppPlayListDao.update(dto);
    }


    public int deleteById(Long id) {
        return tgAppPlayListDao.deleteById(id);
    }
}