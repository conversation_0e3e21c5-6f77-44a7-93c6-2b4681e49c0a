package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.jmt.dao.TgBusinessOrderDao;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.tg.TgBusinessOrderDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;

@Service
public class TgBusinessOrderService {
    @Resource
    private TgBusinessOrderDao tgBusinessOrderDao;

    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    public PageResult<TgBusinessOrderDto> getPage(PageQuery<TgBusinessOrderDto> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        TgBusinessOrderDto tgBusinessOrderDto = pageQuery.getQueryData();
        List<TgBusinessOrderDto> list = tgBusinessOrderDao.getPage(tgBusinessOrderDto);
        if(list != null) {
            return new PageResult<>(list);
        }
        return null;
    }

    /**
     * id查询
     * @param id
     * @return
     */
    public TgBusinessOrderDto getInfo(@Param("id") Long id) {return tgBusinessOrderDao.getInfo(id);}

    public Integer edit(TgBusinessOrderDto tgBusinessOrderDto) {
        return tgBusinessOrderDao.update(tgBusinessOrderDto);
    }

    /**
     * 删除
     * @param id
     * @return
     */
    public Integer del(@Param("id") Long id) {return tgBusinessOrderDao.del(id);}

    /**
     * 新增
     * @param tgBusinessOrderDto
     * @return
     */
    public Integer add(TgBusinessOrderDto tgBusinessOrderDto) {return tgBusinessOrderDao.add(tgBusinessOrderDto);}
}
