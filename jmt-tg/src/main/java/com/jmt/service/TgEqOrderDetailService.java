package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.jmt.dao.TgEqOrderDetailDao;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.tg.TgEqOrderDetailDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;

@Service
public class TgEqOrderDetailService {
    @Resource
    private TgEqOrderDetailDao tgEqOrderDetailDao;

    public PageResult<TgEqOrderDetailDto> getPage(PageQuery<TgEqOrderDetailDto> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        TgEqOrderDetailDto tgEqOrderDetailDto = pageQuery.getQueryData();
        List<TgEqOrderDetailDto> list = tgEqOrderDetailDao.getPage(tgEqOrderDetailDto);
        if (list != null) {
            return new PageResult<>(list);
        }
        return null;
    }
    public TgEqOrderDetailDto getInfo(@Param("id") Long id) {
        return tgEqOrderDetailDao.getInfo(id);
    }
    public Integer edit(TgEqOrderDetailDto tgEqOrderDetailDto) {return tgEqOrderDetailDao.update(tgEqOrderDetailDto);}

    public Integer del(@Param("id") Long id) {return tgEqOrderDetailDao.delete(id);}

    public Integer add(TgEqOrderDetailDto tgEqOrderDetailDto) { return tgEqOrderDetailDao.insert(tgEqOrderDetailDto);}


}
