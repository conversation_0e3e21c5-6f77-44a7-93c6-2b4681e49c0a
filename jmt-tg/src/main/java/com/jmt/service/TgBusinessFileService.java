package com.jmt.service;
import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.dao.TgBusinessFileDao;

import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.tg.TgBusinessFileDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;

@Service
public class TgBusinessFileService extends BaseService {
    @Resource
    private TgBusinessFileDao tgBusinessFileDao;

    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    public PageResult<TgBusinessFileDto> getPage(PageQuery<TgBusinessFileDto> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        TgBusinessFileDto tgBusinessFileDto = pageQuery.getQueryData();
        List<TgBusinessFileDto> list = tgBusinessFileDao.getPage(tgBusinessFileDto);
        if (list != null){
            return new PageResult<>(list);
        }
        return null;
    }

    /**
     * 删除
     * @param id
     * @return
     */
    public Integer del(@Param("id") Long id) {
        return tgBusinessFileDao.del(id);
    }

    /**
     * 添加
     * @param tgBusinessFileDto
     * @return
     */
    public Integer add(TgBusinessFileDto tgBusinessFileDto) {return tgBusinessFileDao.add(tgBusinessFileDto);}

    /**
     * id查询
     * @param id
     * @return
     */
    public TgBusinessFileDto getInfo(@Param("id") Long id) {
        return tgBusinessFileDao.getInfo(id);
    }

    /**
     * 编辑
     * @param tgBusinessFileDto
     * @return
     */
    public Integer edit(TgBusinessFileDto tgBusinessFileDto) {
        return tgBusinessFileDao.update(tgBusinessFileDto);
    }
}
