package com.jmt.service;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.jmt.dao.TgAppFileDao;
import com.jmt.model.dto.TgAppFileDto;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

@Service
public class TgAppFileService{

    @Resource
    private TgAppFileDao tgAppFileDao;


    public PageResult<TgAppFileDto> pageQuery(PageQuery<TgAppFileDto> pageQuery) {
        Page<TgAppFileDto> page = PageHelper.startPage(
                pageQuery.getPageNo(),
                pageQuery.getPageSize()
        ).doSelectPage(() -> tgAppFileDao.pageQuery(pageQuery.getQueryData()));
        return new PageResult<>(page);
    }


    public int add(TgAppFileDto dto) {
        return tgAppFileDao.add(dto);
    }


    public TgAppFileDto getById(Long id) {
        return tgAppFileDao.getById(id);
    }

    public int update(TgAppFileDto dto) {
        return tgAppFileDao.update(dto);
    }


    public int deleteById(Long id) {
        return tgAppFileDao.deleteById(id);
    }
}