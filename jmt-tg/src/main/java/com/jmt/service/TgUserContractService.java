package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.dao.TgUserContractDao;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.tg.TgUserContractDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;

@Service
public class TgUserContractService extends BaseService {
    @Resource
    private TgUserContractDao tgUserContractDao;

    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    public PageResult<TgUserContractDto> getPage(PageQuery<TgUserContractDto> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        TgUserContractDto tgUserContractDto = pageQuery.getQueryData();
        List<TgUserContractDto> list = tgUserContractDao.getPage(tgUserContractDto);
        if (list != null) {
            return new PageResult<>(list);
        }
        return null;
    }

    /**
     * 依据id查询
     * @param id
     * @return
     */
    public TgUserContractDto getInfo(@Param("id") Long id) {
        return tgUserContractDao.getInfo(id);
    }

    /**
     * 依据id修改
     * @param tgUserContractDto
     * @return
     */
    public Integer edit(TgUserContractDto tgUserContractDto) {
        return tgUserContractDao.update(tgUserContractDto);
    }

    /**
     * 新增
     * @param tgUserContractDto
     * @return
     */
    public Integer add(TgUserContractDto tgUserContractDto) {
        return tgUserContractDao.add(tgUserContractDto);
    }

    /**
     * 删除
     * @param id
     * @return
     */
    public Integer del(Long id) {
        return tgUserContractDao.delete(id);
    }

}
