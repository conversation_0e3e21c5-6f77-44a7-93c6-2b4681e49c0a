package com.jmt.service;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.jmt.dao.TgBusinesPlayListDao;
import com.jmt.model.dto.TgBusinesPlayListDto;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

@Service
public class TgBusinesPlayListService {

    @Resource
    private TgBusinesPlayListDao tgBusinesPlayListDao;


    public PageResult<TgBusinesPlayListDto> pageQuery(PageQuery<TgBusinesPlayListDto> pageQuery) {
        Page<TgBusinesPlayListDto> page = PageHelper.startPage(
                pageQuery.getPageNo(),
                pageQuery.getPageSize()
        ).doSelectPage(() -> tgBusinesPlayListDao.pageQuery(pageQuery.getQueryData()));
        return new PageResult<>(page);
    }


    public int add(TgBusinesPlayListDto dto) {
        return tgBusinesPlayListDao.add(dto);
    }


    public TgBusinesPlayListDto getById(Long id) {
        return tgBusinesPlayListDao.getById(id);
    }


    public int update(TgBusinesPlayListDto dto) {
        return tgBusinesPlayListDao.update(dto);
    }


    public int deleteById(Long id) {
        return tgBusinesPlayListDao.deleteById(id);
    }
}