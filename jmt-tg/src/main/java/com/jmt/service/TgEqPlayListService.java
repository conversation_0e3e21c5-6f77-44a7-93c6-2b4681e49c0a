package com.jmt.service;
import com.github.pagehelper.PageHelper;
import com.jmt.dao.TgEqPlayListDao;

import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.tg.TgEqPlayListDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;

@Service
public class TgEqPlayListService {
    @Resource
    private TgEqPlayListDao tgEqPlayListDao;

    public PageResult<TgEqPlayListDto> getPage(PageQuery<TgEqPlayListDto> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        TgEqPlayListDto tgEqPlayListDto = pageQuery.getQueryData();
        List<TgEqPlayListDto> list = tgEqPlayListDao.getPage(tgEqPlayListDto);
        if (list != null) {
            return new PageResult<>(list);
        }
        return null;
    }
    public TgEqPlayListDto getInfo(@Param("id") Long id) {
        return tgEqPlayListDao.getInfo(id);
    }
    public Integer edit(TgEqPlayListDto tgEqPlayListDto) {
        return tgEqPlayListDao.update(tgEqPlayListDto);
    }
    public Integer del(@Param("id") Long id) {
        return tgEqPlayListDao.delete(id);
    }
    public Integer add(TgEqPlayListDto tgEqPlayListDto) {
        return tgEqPlayListDao.add(tgEqPlayListDto);
    }
}
