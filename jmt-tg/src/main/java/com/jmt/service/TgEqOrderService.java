package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.dao.TgEqOrderDao;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.tg.TgEqOrderDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
@Service
public class TgEqOrderService extends BaseService {
    @Resource
    private TgEqOrderDao tgEqOrderDao;


    public PageResult<TgEqOrderDto> getPage(PageQuery<TgEqOrderDto> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        TgEqOrderDto tgEqOrderDto = pageQuery.getQueryData();
        List<TgEqOrderDto> list = tgEqOrderDao.getPage(tgEqOrderDto);
        if (list != null)
        {
            return new PageResult<>(list);
        }
        return null;
    }
    public TgEqOrderDto getInfo(@Param("id") Long id) {return tgEqOrderDao.getInfo(id);}

    public Integer edit(TgEqOrderDto tgEqOrderDto) {return tgEqOrderDao.update(tgEqOrderDto);}

    public Integer del(@Param("id") Long id) {return tgEqOrderDao.delete(id);}

    public Integer add(TgEqOrderDto tgEqOrderDto){return tgEqOrderDao.add(tgEqOrderDto);}
}
