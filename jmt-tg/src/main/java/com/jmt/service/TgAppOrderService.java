package com.jmt.service;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.jmt.dao.TgAppOrderDao;
import com.jmt.model.dto.TgAppOrderDto;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

@Service
public class TgAppOrderService {

    @Resource
    private TgAppOrderDao tgAppOrderDao;

    public PageResult<TgAppOrderDto> pageQuery(PageQuery<TgAppOrderDto> pageQuery) {
        Page<TgAppOrderDto> page = PageHelper.startPage(
                pageQuery.getPageNo(),
                pageQuery.getPageSize()
        ).doSelectPage(() -> tgAppOrderDao.pageQuery(pageQuery.getQueryData()));
        return new PageResult<>(page);
    }


    public int add(TgAppOrderDto dto) {
        return tgAppOrderDao.add(dto);
    }


    public TgAppOrderDto getById(Long id) {
        return tgAppOrderDao.getById(id);
    }


    public int update(TgAppOrderDto dto) {
        return tgAppOrderDao.update(dto);
    }


    public int deleteById(Long id) {
        return tgAppOrderDao.deleteById(id);
    }
}