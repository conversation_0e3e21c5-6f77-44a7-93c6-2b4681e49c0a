package com.jmt.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.jmt.dao.TgVideoFileDao;
import com.jmt.model.dto.TgVideoFileDto;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

@Service
public class TgVideoFileService {

    @Resource
    private TgVideoFileDao tgVideoFileDao;


    public PageResult<TgVideoFileDto> pageQuery(PageQuery<TgVideoFileDto> pageQuery) {
        Page<TgVideoFileDto> page = PageHelper.startPage(
                pageQuery.getPageNo(),
                pageQuery.getPageSize()
        ).doSelectPage(() -> tgVideoFileDao.pageQuery(pageQuery.getQueryData()));
        return new PageResult<>(page);
    }


    public int add(TgVideoFileDto dto) {
        return tgVideoFileDao.add(dto);
    }


    public TgVideoFileDto getById(Long id) {
        return tgVideoFileDao.getById(id);
    }


    public int update(TgVideoFileDto dto) {
        return tgVideoFileDao.update(dto);
    }


    public int deleteById(Long id) {
        return tgVideoFileDao.deleteById(id);
    }
}