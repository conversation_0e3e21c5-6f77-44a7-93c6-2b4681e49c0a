package com.jmt.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.jmt.dao.TgVideoOrderDetailDao;
import com.jmt.model.dto.TgVideoOrderDetailDto;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

@Service
public class TgVideoOrderDetailService {

    @Resource
    private TgVideoOrderDetailDao tgVideoOrderDetailDao;


    public PageResult<TgVideoOrderDetailDto> pageQuery(PageQuery<TgVideoOrderDetailDto> pageQuery) {
        Page<TgVideoOrderDetailDto> page = PageHelper.startPage(
                pageQuery.getPageNo(),
                pageQuery.getPageSize()
        ).doSelectPage(() -> tgVideoOrderDetailDao.pageQuery(pageQuery.getQueryData()));
        return new PageResult<>(page);
    }


    public int add(TgVideoOrderDetailDto dto) {
        return tgVideoOrderDetailDao.add(dto);
    }


    public TgVideoOrderDetailDto getById(Long id) {
        return tgVideoOrderDetailDao.getById(id);
    }


    public int update(TgVideoOrderDetailDto dto) {
        return tgVideoOrderDetailDao.update(dto);
    }


    public int deleteById(Long id) {
        return tgVideoOrderDetailDao.deleteById(id);
    }
}