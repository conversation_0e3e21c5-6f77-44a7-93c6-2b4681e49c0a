package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.dao.TgEqFileDao;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.tg.TgEqFileDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;

@Service
public class TgEqFileService extends BaseService {
    @Resource
    private TgEqFileDao tgEqFileDao;

    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    public PageResult<TgEqFileDto> getPage(PageQuery<TgEqFileDto> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        TgEqFileDto tgEqFileDto = pageQuery.getQueryData();
        List<TgEqFileDto> list = tgEqFileDao.getPage(tgEqFileDto);
        if (list != null) {
            return new PageResult<>(list);
        }
        return null;
    }

    /**
     * id查询
     * @param id
     * @return
     */
    public TgEqFileDto getInfo(@Param("id") Long id) {return tgEqFileDao.getInfo(id);}

    /**
     * id编辑
     * @param tgEqFileDto
     * @return
     */
    public Integer edit(TgEqFileDto tgEqFileDto) {return tgEqFileDao.update(tgEqFileDto);}

    /**
     * 新增
     * @param tgEqFileDto
     * @return
     */
    public Integer add(TgEqFileDto tgEqFileDto) {return tgEqFileDao.insert(tgEqFileDto);}

    /**
     * 删除
     * @param id
     * @return
     */
    public Integer del(@Param("id") Long id) {return tgEqFileDao.delete(id);}
    }
