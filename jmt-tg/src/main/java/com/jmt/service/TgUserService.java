package com.jmt.service;
import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.dao.TgUserDao;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.tg.TgUserDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;

@Service
public class TgUserService extends BaseService {
    @Resource
    private TgUserDao tgUserDao;

    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    public PageResult<TgUserDto> getPage(PageQuery<TgUserDto> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        TgUserDto tgUserDto = pageQuery.getQueryData();
        List<TgUserDto> list = tgUserDao.getPage(tgUserDto);
        if (list != null) {
            return new PageResult<>(list);
        }
        return null;
    }

    /**
     * id查询
     * @param id
     * @return
     */
    public TgUserDto getInfo(@Param("id") Long id) {
        return tgUserDao.getInfo(id);
    }

    /**
     * 新增
     * @param tgUserDto
     * @return
     */
    public Integer add(TgUserDto tgUserDto) {
        return tgUserDao.insert(tgUserDto);
    }

    /**
     * 更新
     * @param tgUserDto
     * @return
     */
    public Integer edit(TgUserDto tgUserDto) {
        return tgUserDao.update(tgUserDto);
    }

    /**
     * 删除
     * @param id
     * @return
     */
    public Integer del(Long id) {
        return tgUserDao.delete(id);
    }
}
