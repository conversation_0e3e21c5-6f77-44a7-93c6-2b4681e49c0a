package com.jmt.controller;
import com.jmt.model.dto.TgVideoOrderDto;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.service.TgVideoOrderService;
import org.springframework.web.bind.annotation.*;


import javax.annotation.Resource;

@RestController
@RequestMapping("/tg/video/order/v1")
public class TgVideoOrderController {

    @Resource
    private TgVideoOrderService tgVideoOrderService;

    // 分页查询
    @GetMapping("/page")
    public PageResult<TgVideoOrderDto> pageQuery(PageQuery<TgVideoOrderDto> pageQuery) {
        return tgVideoOrderService.pageQuery(pageQuery);
    }

    // 新增
    @PostMapping("/add")
    public int add(@RequestBody TgVideoOrderDto dto) {
        return tgVideoOrderService.add(dto);
    }

    // 按ID查询
    @GetMapping("/{id}")
    public TgVideoOrderDto getById(@PathVariable Long id) {
        return tgVideoOrderService.getById(id);

    }

    // 修改
    @PostMapping("/update")
    public int update(@RequestBody TgVideoOrderDto dto) {
        return tgVideoOrderService.update(dto);
    }

    // 根据ID删除
    @PostMapping("/delete/{id}")
    public int deleteById(@PathVariable Long id) {
        return tgVideoOrderService.deleteById(id);
    }
}
