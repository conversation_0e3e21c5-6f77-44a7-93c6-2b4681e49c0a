package com.jmt.controller;
import com.jmt.model.dto.TgAppPlayListDto;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.service.TgAppPlayListService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;

@RestController
@RequestMapping("/tg/app/play/list/v1")
public class TgAppPlayListController {

    @Resource
    private TgAppPlayListService tgAppPlayListService;

    // 分页查询
    @GetMapping("/page")
    public PageResult<TgAppPlayListDto> pageQuery(PageQuery<TgAppPlayListDto> pageQuery) {
        return tgAppPlayListService.pageQuery(pageQuery);
    }

    // 新增
    @PostMapping("/add")
    public int add(@RequestBody TgAppPlayListDto dto) {
        return tgAppPlayListService.add(dto);
    }

    // 按ID查询
    @GetMapping("/{id}")
    public TgAppPlayListDto getById(@PathVariable Long id) {
        return tgAppPlayListService.getById(id);

    }

    // 修改
    @PostMapping("/update")
    public int update(@RequestBody TgAppPlayListDto dto) {
        return tgAppPlayListService.update(dto);
    }

    // 根据ID删除
    @PostMapping("/delete/{id}")
    public int deleteById(@PathVariable Long id) {
        return tgAppPlayListService.deleteById(id);
    }
}
