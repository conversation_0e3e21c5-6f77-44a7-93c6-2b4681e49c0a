package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.tg.TgEqFileDto;
import com.jmt.service.TgEqFileService;
import com.jmt.util.LoginUserUtil;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;

@RestController
@RequestMapping("/tg/eq/file/v1/*")
public class TgEqFileController extends BaseController {
    @Resource
    private TgEqFileService tgEqFileService;

    /**
     * 分页
     * @param pageQuery
     * @return
     */
    @ResponseBody
    @GetMapping("/getPage")
    public String getPage(@RequestBody PageQuery<TgEqFileDto> pageQuery) {
        LoginUaaUser loginUser = LoginUserUtil.get();
        PageResult<TgEqFileDto> page = tgEqFileService.getPage(pageQuery);
        return super.responseSuccess(page,"查询成功");
    }

    /**
     * id查询
     * @param id
     * @return
     */
    @ResponseBody
    @GetMapping("/getInfo")
    public String getInfo(@RequestParam Long id) {
        LoginUaaUser loginUser = LoginUserUtil.get();
        TgEqFileDto tgEqFileDto = tgEqFileService.getInfo(id);
        return  super.responseSuccess(tgEqFileDto,"查询成功");

    }

    /**
     * 根据id编辑
     * @param tgEqFileDto
     * @return
     */
    @ResponseBody
    @PostMapping("/edit")
    public String edit(@RequestBody TgEqFileDto tgEqFileDto) {
        LoginUaaUser loginUser = LoginUserUtil.get();
        Integer res = tgEqFileService.edit(tgEqFileDto);
        if (res == 1) {
            return super.responseSuccess("修改成功");
        }else {
            return super.responseFail("修改失败");
        }
    }

    /**
     * 新增
     * @param tgEqFileDto
     * @return
     */
    @ResponseBody
    @PostMapping("/add")
    public String add(@RequestBody TgEqFileDto tgEqFileDto) {
        LoginUaaUser loginUser = LoginUserUtil.get();
        Integer res = tgEqFileService.add(tgEqFileDto);
        if (res == 1) {
            return super.responseSuccess("添加成功");
        }else {
            return super.responseFail("添加失败");
        }
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @ResponseBody
    @PostMapping("/del")
    public String del(@RequestParam("id") Long id) {
        LoginUaaUser loginUser = LoginUserUtil.get();
        Integer res = tgEqFileService.del(id);
        if (res == 1) {
            return super.responseSuccess("删除成功");
        }else {
            return super.responseFail("删除失败");
        }
    }
}
