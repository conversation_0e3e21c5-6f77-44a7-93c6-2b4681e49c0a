package com.jmt.controller;
import com.jmt.model.dto.TgVideoOrderDetailDto;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.service.TgVideoOrderDetailService;
import org.springframework.web.bind.annotation.*;


import javax.annotation.Resource;

@RestController
@RequestMapping("/tg/video/order/detail/v1")
public class TgVideoOrderDetailController {

    @Resource
    private TgVideoOrderDetailService tgVideoOrderDetailService;

    // 分页查询
    @GetMapping("/page")
    public PageResult<TgVideoOrderDetailDto> pageQuery(PageQuery<TgVideoOrderDetailDto> pageQuery) {
        return tgVideoOrderDetailService.pageQuery(pageQuery);
    }

    // 新增
    @PostMapping("/add")
    public int add(@RequestBody TgVideoOrderDetailDto dto) {
        return tgVideoOrderDetailService.add(dto);
    }

    // 按ID查询
    @GetMapping("/{id}")
    public TgVideoOrderDetailDto getById(@PathVariable Long id) {
        return tgVideoOrderDetailService.getById(id);

    }

    // 修改
    @PostMapping("/update")
    public int update(@RequestBody TgVideoOrderDetailDto dto) {
        return tgVideoOrderDetailService.update(dto);
    }

    // 根据ID删除
    @PostMapping("/delete/{id}")
    public int deleteById(@PathVariable Long id) {
        return tgVideoOrderDetailService.deleteById(id);
    }
}
