package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.tg.TgEqOrderDto;
import com.jmt.service.TgEqOrderService;
import com.jmt.util.LoginUserUtil;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;

@RestController
@RequestMapping("/tg/eq/order/v1/*")
public class TgEqOrderController extends BaseController {
    @Resource
    private TgEqOrderService tgEqOrderService;

    /**
     * 分页查询
     * @param PageQuery
     * @return
     */
    @ResponseBody
    @GetMapping("/getPage")
    public String getPage(@RequestBody PageQuery<TgEqOrderDto> PageQuery) {
        LoginUaaUser loginUaaUser = LoginUserUtil.get();
        PageResult<TgEqOrderDto> page = tgEqOrderService.getPage(PageQuery);
        return super.responseSuccess(page,"查询成功");
    }

    /**
     * 依据id查询
     * @param id
     * @return
     */
    @ResponseBody
    @GetMapping("/getInfo")
    public String getInfo(@RequestParam Long id) {
        LoginUaaUser loginUaaUser = LoginUserUtil.get();
        TgEqOrderDto tgEqOrderDto = tgEqOrderService.getInfo(id);
        return super.responseSuccess(tgEqOrderDto,"查询成功");
    }

    /**
     * 新增
     * @param tgEqOrderDto
     * @return
     */
    @ResponseBody
    @PostMapping("/add")
    public String add(@RequestBody TgEqOrderDto tgEqOrderDto) {
        LoginUaaUser loginUaaUser = LoginUserUtil.get();
        Integer res = tgEqOrderService.add(tgEqOrderDto);
        if (res == 1)
        {
            return super.responseSuccess("添加成功");
        }else {
            return super.responseFail("添加失败");
        }
    }

    /**
     * 编辑
     * @param tgEqOrderDto
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/edit")
    public String edit(@RequestBody TgEqOrderDto tgEqOrderDto) {
        LoginUaaUser loginUaaUser = LoginUserUtil.get();
        Integer res = tgEqOrderService.edit(tgEqOrderDto);
        if (res == 1)
        {
            return super.responseSuccess("修改成功");
        }else {
            return super.responseFail("修改失败");
        }
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @ResponseBody
    @PostMapping("/del")
    public String del(@RequestParam("id") Long id) {
        LoginUaaUser loginUaaUser = LoginUserUtil.get();
        Integer res = tgEqOrderService.del(id);
        if (res == 1)
        {
            return super.responseSuccess("删除成功");
        }else {
            return  super.responseFail("修改失败");
        }
    }

}
