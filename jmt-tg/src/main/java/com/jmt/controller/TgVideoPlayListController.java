package com.jmt.controller;
import com.jmt.model.dto.TgVideoPlayListDto;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.service.TgVideoPlayListService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;

@RestController
@RequestMapping("/tg/video/play/list/v1")
public class TgVideoPlayListController {

    @Resource
    private TgVideoPlayListService tgVideoPlayListService;

    // 分页查询
    @GetMapping("/page")
    public PageResult<TgVideoPlayListDto> pageQuery(PageQuery<TgVideoPlayListDto> pageQuery) {
        return tgVideoPlayListService.pageQuery(pageQuery);
    }

    // 新增
    @PostMapping("/add")
    public int add(@RequestBody TgVideoPlayListDto dto) {
        return tgVideoPlayListService.add(dto);
    }

    // 按ID查询
    @GetMapping("/{id}")
    public TgVideoPlayListDto getById(@PathVariable Long id) {
        return tgVideoPlayListService.getById(id);

    }

    // 修改
    @PostMapping("/update")
    public int update(@RequestBody TgVideoPlayListDto dto) {
        return tgVideoPlayListService.update(dto);
    }

    // 根据ID删除
    @PostMapping("/delete/{id}")
    public int deleteById(@PathVariable Long id) {
        return tgVideoPlayListService.deleteById(id);
    }
}
