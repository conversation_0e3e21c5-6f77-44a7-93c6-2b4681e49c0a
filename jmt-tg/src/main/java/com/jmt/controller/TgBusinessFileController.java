package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.tg.TgBusinessFileDto;
import com.jmt.service.TgBusinessFileService;
import com.jmt.util.LoginUserUtil;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;

@RestController
@RequestMapping("/tg/business/file/v1/*")
public class TgBusinessFileController extends BaseController {
    @Resource
    private TgBusinessFileService tgBusinessFileService;

    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    @ResponseBody
    @GetMapping("/getPage")
    public String getPage(@RequestBody PageQuery<TgBusinessFileDto> pageQuery){
        LoginUaaUser loginUser = LoginUserUtil.get();
        PageResult<TgBusinessFileDto> page = tgBusinessFileService.getPage(pageQuery);
        return  super.responseSuccess(page,"查询成功");
    }

    /**
     * id查询
     * @param id
     * @return
     */
    @ResponseBody
    @GetMapping("/getInfo")
    public String getInfo(@RequestParam Long id){
        LoginUaaUser loginUser = LoginUserUtil.get();
        TgBusinessFileDto tgBusinessFileDto = tgBusinessFileService.getInfo(id);
        return  super.responseSuccess(tgBusinessFileDto,"查询成功");
    }

    /**
     * 添加
     * @param tgBusinessFileDto
     * @return
     */
    @ResponseBody
    @PostMapping("/add")
    public String add(@RequestBody TgBusinessFileDto tgBusinessFileDto){
        LoginUaaUser loginUser = LoginUserUtil.get();
        Integer res = tgBusinessFileService.add(tgBusinessFileDto);
        if (res == 1){
            return super.responseSuccess("添加成功");
        }else {
            return super.responseFail("添加失败");
        }
    }

    /**
     * 编辑
     * @param tgBusinessFileDto
     * @return
     */
    @ResponseBody
    @PostMapping("/edit")
    public String edit(@RequestBody TgBusinessFileDto tgBusinessFileDto){
        LoginUaaUser loginUser = LoginUserUtil.get();
        Integer res = tgBusinessFileService.edit(tgBusinessFileDto);
        if (res == 1){
            return super.responseSuccess("编辑成功");
        }else {
            return super.responseFail("编辑失败");
        }
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @ResponseBody
    @PostMapping("/del")
    public String del(@Param("id") Long id){
        LoginUaaUser loginUser = LoginUserUtil.get();
        Integer res = tgBusinessFileService.del(id);
        if (res == 1){
            return super.responseSuccess("修改成功");
        }else {
            return super.responseFail("修改失败");
        }
    }
}
