package com.jmt.controller;
import com.jmt.model.dto.TgBusinesPlayListDto;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.service.TgBusinesPlayListService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;

@RestController
@RequestMapping("/tg/busines/play/list/v1")
public class TgBusinesPlayListController {

    @Resource
    private TgBusinesPlayListService tgBusinesPlayListService;

    // 分页查询
    @GetMapping("/page")
    public PageResult<TgBusinesPlayListDto> pageQuery(PageQuery<TgBusinesPlayListDto> pageQuery) {
        return tgBusinesPlayListService.pageQuery(pageQuery);
    }

    // 新增
    @PostMapping("/add")
    public int add(@RequestBody TgBusinesPlayListDto dto) {
        return tgBusinesPlayListService.add(dto);
    }

    // 按ID查询
    @GetMapping("/{id}")
    public TgBusinesPlayListDto getById(@PathVariable Long id) {
        return tgBusinesPlayListService.getById(id);

    }

    // 修改
    @PostMapping("/update")
    public int update(@RequestBody TgBusinesPlayListDto dto) {
        return tgBusinesPlayListService.update(dto);
    }

    // 根据ID删除
    @PostMapping("/delete/{id}")
    public int deleteById(@PathVariable Long id) {
        return tgBusinesPlayListService.deleteById(id);
    }
}
