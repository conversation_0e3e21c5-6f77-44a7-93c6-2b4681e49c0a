package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.tg.TgUserSourceDto;
import com.jmt.service.TgUserSourceService;
import com.jmt.util.LoginUserUtil;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
@RestController
@RequestMapping("/tg/user/source/v1/*")
public class TgUserSourceController extends BaseController {
    @Resource
    private TgUserSourceService tgUserSourceService;

    /**
     *分页查询
     * @param pageQuery
     * @return
     */
    @ResponseBody
    @GetMapping("/getPage")
    public String getPage(@RequestBody PageQuery<TgUserSourceDto> pageQuery){
        LoginUaaUser loginUaaUser = LoginUserUtil.get();
        PageResult<TgUserSourceDto> page = tgUserSourceService.getPage(pageQuery);
        return super.responseSuccess(page,"查询成功");
    }

    /**
     * 依据id查询
     * @param id
     * @return
     */
    @ResponseBody
    @GetMapping("/getInfo")
    public String getInfo(@RequestParam Long id){
        LoginUaaUser loginUaaUser = LoginUserUtil.get();
        TgUserSourceDto tgUserSourceDto = tgUserSourceService.getInfo(id);
        return super.responseSuccess(tgUserSourceDto,"查询成功");
    }

    /**
     * 新增
     * @param tgUserSourceDto
     * @return
     */
    @ResponseBody
    @PostMapping("/add")
    public String add(@RequestBody TgUserSourceDto tgUserSourceDto){
        LoginUaaUser loginUaaUser = LoginUserUtil.get();
        Integer res = tgUserSourceService.add(tgUserSourceDto);
        if (res == 1) {
            return super.responseSuccess("添加成功");
        }else {
            return super.responseFail("添加失败");
        }
    }

    /**
     * 编辑
     * @param tgUserSourceDto
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/edit")
    public String edit(@RequestBody TgUserSourceDto tgUserSourceDto){
        LoginUaaUser loginUaaUser = LoginUserUtil.get();
        Integer res = tgUserSourceService.update(tgUserSourceDto);
        if (res == 1) {
            return super.responseSuccess("修改成功");
        }else {
            return super.responseFail("修改失败");
        }
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @ResponseBody
    @PostMapping(value = ("del"))
    public String del(@RequestParam Long id){
        LoginUaaUser loginUaaUser = LoginUserUtil.get();
        Integer res = tgUserSourceService.delete(id);
        if (res == 1) {
            return super.responseSuccess("删除成功");
        }else {
            return super.responseFail("删除失败");
        }
    }

}
