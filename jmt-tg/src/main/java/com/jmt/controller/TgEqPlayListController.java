package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.tg.TgEqPlayListDto;
import com.jmt.service.TgEqPlayListService;
import com.jmt.util.LoginUserUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/tg/eq/play/list/v1/*")
public class TgEqPlayListController extends BaseController {
    @Resource
    private TgEqPlayListService tgEqPlayListService;

    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    @ResponseBody
    @GetMapping("/getPage")
    public String getPage(@RequestBody PageQuery<TgEqPlayListDto> pageQuery) {
        LoginUaaUser loginUser = LoginUserUtil.get();
        PageResult<TgEqPlayListDto> page = tgEqPlayListService.getPage(pageQuery);
        return super.responseSuccess(page,"查询成功");
    }

    /**
     * id查询
     * @param id
     * @return
     */
    @ResponseBody
    @GetMapping("/getInfo")
    public String getInfo(@RequestParam Long id) {
        LoginUaaUser loginUser = LoginUserUtil.get();
        TgEqPlayListDto tgEqPlayListDto = tgEqPlayListService.getInfo(id);
        return super.responseSuccess(tgEqPlayListDto,"查询成功");
    }

    /**
     * 新增
     * @param tgEqPlayListDto
     * @return
     */
    @ResponseBody
    @PostMapping("/add")
    public String add(@RequestBody TgEqPlayListDto tgEqPlayListDto) {
        LoginUaaUser loginUser = LoginUserUtil.get();
        Integer res = tgEqPlayListService.add(tgEqPlayListDto);
        if (res == 1){
            return super.responseSuccess("添加成功");
        }else {
            return super.responseFail("添加失败");
        }
    }

    /**
     * 编辑
     * @param tgEqPlayListDto
     * @return
     */
    @ResponseBody
    @PostMapping("/edit")
    public String edit(@RequestBody TgEqPlayListDto tgEqPlayListDto) {
        LoginUaaUser loginUser = LoginUserUtil.get();
        Integer res = tgEqPlayListService.edit(tgEqPlayListDto);
        if (res == 1){
            return super.responseSuccess("编辑成功");
        }else {
            return super.responseFail("编辑失败");
        }
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @ResponseBody
    @PostMapping("/del")
    public String del(@RequestParam("id") Long id) {
        LoginUaaUser loginUser = LoginUserUtil.get();
        Integer res = tgEqPlayListService.del(id);
        if (res == 1){
            return super.responseSuccess("删除成功");
        }else {
            return super.responseFail("删除失败");
        }
    }
}
