package com.jmt.controller;
import com.jmt.model.dto.TgVideoFileDto;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.service.TgVideoFileService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/tg/video/file/v1")
public class TgVideoFileController {

    @Resource
    private TgVideoFileService tgVideoFileService;

    // 分页查询
    @GetMapping("/page")
    public PageResult<TgVideoFileDto> pageQuery(PageQuery<TgVideoFileDto> pageQuery) {
        return tgVideoFileService.pageQuery(pageQuery);
    }

    // 新增
    @PostMapping("/add")
    public int add(@RequestBody TgVideoFileDto dto) {
        return tgVideoFileService.add(dto);
    }

    // 按ID查询
    @GetMapping("/{id}")
    public TgVideoFileDto getById(@PathVariable Long id) {
        return tgVideoFileService.getById(id);
    }

    // 修改
    @PostMapping("/update")
    public int update(@RequestBody TgVideoFileDto dto) {
        return tgVideoFileService.update(dto);
    }

    // 根据ID删除
    @PostMapping("/delete/{id}")
    public int deleteById(@PathVariable Long id) {
        return tgVideoFileService.deleteById(id);
    }
}