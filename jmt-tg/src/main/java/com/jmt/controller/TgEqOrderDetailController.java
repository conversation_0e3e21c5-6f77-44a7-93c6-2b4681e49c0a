package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.tg.TgEqOrderDetailDto;
import com.jmt.service.TgEqOrderDetailService;
import com.jmt.util.LoginUserUtil;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;

@RestController
@RequestMapping("/tg/eq/order/detail/v1/*")
public class TgEqOrderDetailController extends BaseController {
    @Resource
    private TgEqOrderDetailService tgEqOrderDetailService;

    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    @ResponseBody
    @GetMapping("/getPage")
    public String getPage(@RequestBody PageQuery<TgEqOrderDetailDto> pageQuery) {
        LoginUaaUser loginUser = LoginUserUtil.get();
        PageResult<TgEqOrderDetailDto> page = tgEqOrderDetailService.getPage(pageQuery);
        return super.responseSuccess(page,"查询成功");
    }

    /**
     * id查询
     * @param id
     * @return
     */
    @ResponseBody
    @GetMapping(value = "/getInfo")
    public String getInfo(@RequestParam Long id) {
        LoginUaaUser loginUser = LoginUserUtil.get();
        TgEqOrderDetailDto tgEqOrderDetailDto = tgEqOrderDetailService.getInfo(id);
        return super.responseSuccess(tgEqOrderDetailDto,"查询成功");
    }

    /**
     * 新增
     * @param tgEqOrderDetailDto
     * @return
     */
    @ResponseBody
    @PostMapping("/add")
    public String add(@RequestBody TgEqOrderDetailDto tgEqOrderDetailDto) {
        LoginUaaUser loginUser = LoginUserUtil.get();
        Integer res = tgEqOrderDetailService.add(tgEqOrderDetailDto);
        if (res == 1 )
        {
            return  super.responseSuccess("添加成功");
        }else {
            return super.responseFail("添加失败");
        }
    }

    /**
     * 编辑
     * @param tgEqOrderDetailDto
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/edit")
    public String edit(@RequestBody TgEqOrderDetailDto tgEqOrderDetailDto) {
        LoginUaaUser loginUser = LoginUserUtil.get();
        Integer res = tgEqOrderDetailService.edit(tgEqOrderDetailDto);
        if (res == 1 )
        {
            return  super.responseSuccess("修改成功");
        }else {
            return super.responseFail("修改失败");
        }
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/del")
    public String del(@RequestParam Long id) {
        LoginUaaUser loginUser = LoginUserUtil.get();
        Integer res = tgEqOrderDetailService.del(id);
        if (res == 1 )
        {
            return  super.responseSuccess("删除成功");
        }else {
            return super.responseFail("删除失败");
        }
    }
}
