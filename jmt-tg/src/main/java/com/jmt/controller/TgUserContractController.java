package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.tg.TgUserContractDto;
import com.jmt.service.TgUserContractService;
import com.jmt.util.LoginUserUtil;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;

@RestController
@RequestMapping("/tg/user/contract/v1/*")
public class TgUserContractController extends BaseController {
    @Resource
    private TgUserContractService tgUserContractService;

    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    @ResponseBody
    @GetMapping("/getPage")
    public String getPage(@RequestBody PageQuery<TgUserContractDto> pageQuery) {
        LoginUaaUser loginUser = LoginUserUtil.get();
        PageResult<TgUserContractDto> page = tgUserContractService.getPage(pageQuery);
        return super.responseSuccess(page, "查询成功");
    }

    /**
     * 根据id查询
     * @param id
     * @return
     */
    @ResponseBody
    @GetMapping(value = "getInfo")
    public String getInfo(@RequestParam("id") Long id) {
        LoginUaaUser loginUaaUser = LoginUserUtil.get();
        TgUserContractDto tgUserContractDto = tgUserContractService.getInfo(id);
        return  super.responseSuccess(tgUserContractDto,"查询成功");
    }

    /**
     * 依据id修改
     * @param tgUserContractDto
     * @return
     */
    @ResponseBody
    @PostMapping(value = "edit")
    public String edit(@RequestBody TgUserContractDto tgUserContractDto) {
        LoginUaaUser loginUser = LoginUserUtil.get();
        Integer res = tgUserContractService.edit(tgUserContractDto);
        if (res == 1) {
            return super.responseSuccess("修改成功");
        }else {
            return  super.responseFail("修改失败");
        }
    }

    /**
     * 新添
     * @param tgUserContractDto
     * @return
     */
    @ResponseBody
    @PostMapping("/add")
    public String add(@RequestBody TgUserContractDto tgUserContractDto) {
        LoginUaaUser loginUser = LoginUserUtil.get();
        Integer res = tgUserContractService.add(tgUserContractDto);
        if (res == 1) {
            return super.responseSuccess("添加成功");
        }else {
            return super.responseFail("添加失败");
        }
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/del")
    public String del(@RequestParam Long id) {
        LoginUaaUser loginUser = LoginUserUtil.get();
        Integer res = tgUserContractService.del(id);
        if (res == 1) {
            return super.responseSuccess("删除成功");
        }else {
            return super.responseFail("删除失败");
        }
    }
}
