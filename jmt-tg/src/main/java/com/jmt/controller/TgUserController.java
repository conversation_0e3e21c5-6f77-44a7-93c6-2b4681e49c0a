package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.tg.TgUserDto;
import com.jmt.service.TgUserService;
import com.jmt.util.LoginUserUtil;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
@RestController
@RequestMapping("/tg/user/v1/*")
public class TgUserController extends BaseController {
    @Resource
    private TgUserService tgUserService;

    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    @ResponseBody
    @GetMapping("/getPage")
    public String getPage(@RequestBody PageQuery<TgUserDto> pageQuery) {
        LoginUaaUser loginUaaUser = LoginUserUtil.get();
        PageResult<TgUserDto> page = tgUserService.getPage(pageQuery);
        return super.responseSuccess(page,"查询成功");
    }

    /**
     * 根据id查询
     * @param id
     * @return
     */
    @ResponseBody
    @GetMapping(value = "/getInfo")
    public String getInfo(@RequestParam Long id) {
        LoginUaaUser loginUaaUser = LoginUserUtil.get();
        TgUserDto tgUserDto = tgUserService.getInfo(id);
        return super.responseSuccess(tgUserDto,"查询成功");
    }

    /**
     * 新增
     * @param tgUserDto
     * @return
     */
    @ResponseBody
    @PostMapping("/add")
    public String add(@RequestBody TgUserDto tgUserDto) {
        LoginUaaUser loginUaaUser = LoginUserUtil.get();
        Integer res = tgUserService.add(tgUserDto);
        if (res == 1) {
            return super.responseSuccess("新增成功");
        }else {
            return super.responseFail("新增失败");
        }

    }

    /**
     * 修改
     * @param tgUserDto
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/edit")
    public String edit(@RequestBody TgUserDto tgUserDto) {
        LoginUaaUser loginUser = LoginUserUtil.get();
        Integer res = tgUserService.edit(tgUserDto);
        if (res == 1) {
            return super.responseSuccess("编辑成功");
        }else {
            return super.responseFail("编辑失败");
        }
    }

    /**
     * 依据id删除
     * @param id
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/del")
    public  String del(@RequestParam Long id) {
        LoginUaaUser loginUser = LoginUserUtil.get();
        Integer res = tgUserService.del(id);
        if (res == 1) {
            return super.responseSuccess("删除成功");
        }else {
            return super.responseFail("删除失败");
        }
    }



}
