package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.tg.TgBusinessOrderDto;
import com.jmt.service.TgBusinessOrderService;
import com.jmt.util.LoginUserUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;

@Controller
@RequestMapping("/tg/business/order/v1/*")
public class TgBusinessOrderController extends BaseController {
    @Resource
    private TgBusinessOrderService tgBusinessOrderService;

    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    @ResponseBody
    @GetMapping("/getPage")
    public String getPage(@RequestBody PageQuery<TgBusinessOrderDto> pageQuery) {
        LoginUaaUser loginUser = LoginUserUtil.get();
        PageResult<TgBusinessOrderDto> page = tgBusinessOrderService.getPage(pageQuery);
        return super.responseSuccess(page,"查询成功");
    }

    /**
     * id查询
     * @param id
     * @return
     */
    @ResponseBody
    @GetMapping("/getInfo")
    public String getInfo(@RequestParam Long id) {
        LoginUaaUser loginUser = LoginUserUtil.get();
        TgBusinessOrderDto tgBusinessOrderDto = tgBusinessOrderService.getInfo(id);
        return super.responseSuccess(tgBusinessOrderDto,"查询成功");
    }

    /**
     * 添加
     * @param tgBusinessOrderDto
     * @return
     */
    @ResponseBody
    @PostMapping("/add")
    public String add(@RequestBody TgBusinessOrderDto tgBusinessOrderDto){
        LoginUaaUser loginUser = LoginUserUtil.get();
        Integer res = tgBusinessOrderService.add(tgBusinessOrderDto);
        if (res == 1){
            return super.responseSuccess("添加成功");
        }else {
            return super.responseFail("添加失败");
        }
    }

    /**
     * 编辑
     * @param tgBusinessOrderDto
     * @return
     */
    @ResponseBody
    @PostMapping("/edit")
    public String edit(@RequestBody TgBusinessOrderDto tgBusinessOrderDto){
        LoginUaaUser loginUser = LoginUserUtil.get();
        Integer res = tgBusinessOrderService.edit(tgBusinessOrderDto);
        if (res == 1){
            return super.responseSuccess("修改成功");
        }else {
            return super.responseFail("修改失败");
        }

    }

    /**
     * 删除
     * @param id
     * @return
     */
    @ResponseBody
    @PostMapping("/del")
    public String del(@RequestParam Long id) {
        LoginUaaUser loginUser = LoginUserUtil.get();
        Integer res = tgBusinessOrderService.del(id);
        if (res == 1){
            return super.responseSuccess("删除成功");
        }else {
            return super.responseFail("删除失败");
        }
    }

}
