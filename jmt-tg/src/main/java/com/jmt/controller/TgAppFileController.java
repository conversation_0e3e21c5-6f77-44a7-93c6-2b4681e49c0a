package com.jmt.controller;
import com.jmt.model.dto.TgAppFileDto;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.service.TgAppFileService;
import org.springframework.web.bind.annotation.*;


import javax.annotation.Resource;

@RestController
@RequestMapping("/tg/app/file/v1")
public class TgAppFileController {

    @Resource
    private TgAppFileService tgAppFileService;

    // 分页查询
    @GetMapping("/page")
    public PageResult<TgAppFileDto> pageQuery(PageQuery<TgAppFileDto> pageQuery) {
        return tgAppFileService.pageQuery(pageQuery);
    }

    // 新增
    @PostMapping("/add")
    public int add(@RequestBody TgAppFileDto dto) {
        return tgAppFileService.add(dto);
    }

    // 按ID查询
    @GetMapping("/{id}")
    public TgAppFileDto getById(@PathVariable Long id) {
        return tgAppFileService.getById(id);

    }

    // 修改
    @PostMapping("/update")
    public int update(@RequestBody TgAppFileDto dto) {
        return tgAppFileService.update(dto);
    }

    // 根据ID删除
    @PostMapping("/delete/{id}")
    public int deleteById(@PathVariable Long id) {
        return tgAppFileService.deleteById(id);
    }
}
