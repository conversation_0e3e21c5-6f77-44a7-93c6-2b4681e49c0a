package com.jmt.controller;
import com.jmt.model.dto.TgAppOrderDto;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.service.TgAppOrderService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;

@RestController
@RequestMapping("/tg/app/order/v1")
public class TgAppOrderController {

    @Resource
    private TgAppOrderService tgAppOrderService;

    // 分页查询
    @GetMapping("/page")
    public PageResult<TgAppOrderDto> pageQuery(PageQuery<TgAppOrderDto> pageQuery) {
        return tgAppOrderService.pageQuery(pageQuery);
    }

    // 新增
    @PostMapping("/add")
    public int add(@RequestBody TgAppOrderDto dto) {
        return tgAppOrderService.add(dto);
    }

    // 按ID查询
    @GetMapping("/{id}")
    public TgAppOrderDto getById(@PathVariable Long id) {
        return tgAppOrderService.getById(id);

    }

    // 修改
    @PostMapping("/update")
    public int update(@RequestBody TgAppOrderDto dto) {
        return tgAppOrderService.update(dto);
    }

    // 根据ID删除
    @PostMapping("/delete/{id}")
    public int deleteById(@PathVariable Long id) {
        return tgAppOrderService.deleteById(id);
    }
}
