package com.jmt.model.dto;

import lombok.Data;
import java.util.Date;

@Data
public class TgAppOrderDto {
    private Long id;
    private String orderNo;
    private String orderName;
    private String tgUserNo;
    private String handleUser;
    private String playPosition;
    private String playApp;
    private Date playStartTime;
    private Date playEndTime;
    private Long fileId;
    private Integer playDays;
    private Integer sourceNum;
    private String payType;
    private Double payAmount;
    private Integer payStauts; // 注意：字段名与数据库一致
    private Integer playStatus;
    private Integer auditStatus;
    private String reason;
    private Date createTime;
    private Date updateTime;
    private Integer isDelete;
}