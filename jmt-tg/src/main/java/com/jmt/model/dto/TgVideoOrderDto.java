package com.jmt.model.dto;

import lombok.Data;
import java.util.Date;

@Data
public class TgVideoOrderDto {
    private Long id;
    private String orderNo;
    private String orderName;
    private String handleUser;
    private Date playStartTime;
    private Date playEndTime;
    private Long fileId; // fileId
    private Integer playBusNum;
    private Integer playEqNum;
    private Integer playDays;
    private Integer playStatus;
    private Integer auditStatus;
    private String reason;
    private Date createTime;
    private Date updateTime;
    private Integer isDelete;
}