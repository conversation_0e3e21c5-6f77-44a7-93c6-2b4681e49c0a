package com.jmt.dao;

import com.jmt.model.tg.TgBusinessOrderDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface TgBusinessOrderDao {
    /**
     * 分页查询
     * @param tgBusinessOrderDto
     * @return
     */
    List<TgBusinessOrderDto> getPage(TgBusinessOrderDto tgBusinessOrderDto);

    /**
     * id查询
     * @param id
     * @return
     */
    TgBusinessOrderDto getInfo(@Param("id") Long id);

    /**
     * 编辑
     * @param tgBusinessOrderDto
     * @return
     */
    Integer update(TgBusinessOrderDto tgBusinessOrderDto);

    /**
     * 新增
     * @param tgBusinessOrderDto
     * @return
     */
    Integer add(TgBusinessOrderDto tgBusinessOrderDto);

    /**
     * 删除
     * @param id
     * @return
     */
    Integer del(@Param("id") Long id);
}
