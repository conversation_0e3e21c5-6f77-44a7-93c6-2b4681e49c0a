package com.jmt.dao;

import com.jmt.model.tg.TgUserSourceDto;
import org.apache.ibatis.annotations.Param;
import javax.annotation.Resource;
import java.util.List;

@Resource
public interface TgUserSourceDao {
    /**
     * 分页查询
     * @param tgUserSourceDto
     * @return
     */
    List<TgUserSourceDto> getPage(TgUserSourceDto tgUserSourceDto);

    /**
     * 依据id查询
     * @param id
     * @return
     */
    TgUserSourceDto getInfo(@Param("id") Long id);

    /**
     * 新增
     * @param tgUserSourceDto
     * @return
     */
    Integer insert(TgUserSourceDto tgUserSourceDto);

    /**
     * 依据id更新
     * @param tgUserSourceDto
     * @return
     */
    Integer update(TgUserSourceDto tgUserSourceDto);

    /**
     * 依据id删除
     * @param id
     * @return
     */
    Integer delete(@Param("id") Long id);
}
