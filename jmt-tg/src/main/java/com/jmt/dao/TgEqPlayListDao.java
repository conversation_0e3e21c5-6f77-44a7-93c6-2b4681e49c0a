package com.jmt.dao;

import com.jmt.model.tg.TgEqPlayListDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface TgEqPlayListDao {
    /**
     * 分页查询
     * @param tgEqPlayListDto
     * @return
     */
    List<TgEqPlayListDto> getPage(TgEqPlayListDto tgEqPlayListDto);

    /**
     * id查询
     * @param id
     * @return
     */
    TgEqPlayListDto getInfo(@Param("id") Long id);

    /**
     * 编辑
     * @param tgEqPlayListDto
     * @return
     */
    Integer update(TgEqPlayListDto tgEqPlayListDto);

    /**
     * id删除
     * @param id
     * @return
     */
    Integer delete(@Param("id") Long id);

    /**
     * 新增
     * @param tgEqPlayListDto
     * @return
     */
    Integer add(TgEqPlayListDto tgEqPlayListDto);
}
