package com.jmt.dao;
import com.jmt.model.tg.TgEqFileDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface TgEqFileDao {
    /**
     * 分页查询
     * @param fileDto
     * @return
     */
    List<TgEqFileDto> getPage(TgEqFileDto fileDto);

    /**
     * id查询
     * @param id
     * @return
     */
    TgEqFileDto getInfo(@Param("id") Long id);

    /**
     * 更新
     * @param tgEqFileDto
     * @return
     */
    Integer update(TgEqFileDto tgEqFileDto);

    /**
     * 新增
     * @param tgEqFileDto
     * @return
     */
    Integer insert(TgEqFileDto tgEqFileDto);

    /**
     * 删除
     * @param id
     * @return
     */
    Integer delete(@Param("id") Long id);
}
