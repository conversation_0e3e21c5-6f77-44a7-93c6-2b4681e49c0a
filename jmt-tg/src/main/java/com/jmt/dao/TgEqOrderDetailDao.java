package com.jmt.dao;

import com.jmt.model.tg.TgEqOrderDetailDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface TgEqOrderDetailDao {
    /**
     * 分页查询
     * @param tgEqOrderDetailDto
     * @return
     */
    List<TgEqOrderDetailDto> getPage(TgEqOrderDetailDto tgEqOrderDetailDto);

    /**
     * id查询
     * @param id
     * @return
     */
    TgEqOrderDetailDto getInfo(@Param("id") Long id);

    /**
     * 删除
     * @param id
     * @return
     */
    Integer delete(@Param("id") Long id);

    /**
     * 新增
     * @param tgEqOrderDetailDto
     * @return
     */
    Integer insert(TgEqOrderDetailDto tgEqOrderDetailDto);

    /**
     * 编辑
     * @param tgEqOrderDetailDto
     * @return
     */
    Integer update(TgEqOrderDetailDto tgEqOrderDetailDto);
}
