package com.jmt.dao;

import com.jmt.model.tg.TgUserContractDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface TgUserContractDao {
    /**
     * 分页查询
     * @param tgUserContractDto
     * @return
     */
    List<TgUserContractDto> getPage(TgUserContractDto tgUserContractDto);

    /**
     * 依据id查询
     * @param id
     * @return
     */
    TgUserContractDto getInfo(@Param("id") Long id);

    /**
     * 依据id更新
     * @param tgUserContractDto
     * @return
     */
    Integer update(TgUserContractDto tgUserContractDto);

    /**
     * 新增
     * @param tgUserContractDto
     * @return
     */
    Integer add(TgUserContractDto tgUserContractDto);

    /**
     * 删除
     * @param id
     * @return
     */
    Integer delete(@Param("id") Long id);
}
