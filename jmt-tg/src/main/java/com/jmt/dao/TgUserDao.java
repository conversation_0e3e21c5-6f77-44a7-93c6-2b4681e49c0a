package com.jmt.dao;

import com.jmt.model.tg.TgUserDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface TgUserDao {
    /**
     * 分页查询
     * @param tgUserDto
     * @return
     */
    List<TgUserDto> getPage(TgUserDto tgUserDto);
    /**
     * id查询
     * @param id
     * @return
     */
    TgUserDto getInfo(@Param("id") Long id);

    /**
     * 新增
     * @param tgUserDto
     * @return
     */
    Integer insert(TgUserDto tgUserDto);

    /**
     * 更新
     * @param tgUserDto
     * @return
     */
    Integer update(TgUserDto tgUserDto);

    /**
     * 删除
     * @param id
     * @return
     */
    Integer delete(@Param("id") Long id);

}
