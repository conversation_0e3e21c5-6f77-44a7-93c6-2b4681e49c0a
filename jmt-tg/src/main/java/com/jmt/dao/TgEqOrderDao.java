package com.jmt.dao;

import com.jmt.model.tg.TgEqOrderDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface TgEqOrderDao {
    /**
     * 分页查询
     * @param tgEqOrderDto
     * @return
     */
    List<TgEqOrderDto> getPage(TgEqOrderDto tgEqOrderDto);

    /**
     * id查询
     * @param id
     * @return
     */
    TgEqOrderDto getInfo(@Param("id") Long id);

    /**
     * 编辑
     * @param tgEqOrderDto
     * @return
     */
    Integer update(TgEqOrderDto tgEqOrderDto);

    /**
     * 新增
     * @param tgEqOrderDto
     * @return
     */
    Integer add(TgEqOrderDto tgEqOrderDto);

    /**
     * 删除
     * @param id
     * @return
     */
    Integer delete(@Param("id") Long id);

}
