package com.jmt.dao;

import com.jmt.model.tg.TgBusinessFileDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface TgBusinessFileDao {
    /**
     * 分页查询
     * @param tgBusinessFileDto
     * @return
     */
    List<TgBusinessFileDto> getPage(TgBusinessFileDto tgBusinessFileDto);

    /**
     * 更新
     * @param tgBusinessFileDto
     * @return
     */
    Integer update(TgBusinessFileDto tgBusinessFileDto);

    /**
     * 添加
     * @param tgBusinessFileDto
     * @return
     */
    Integer add(TgBusinessFileDto tgBusinessFileDto);

    /**
     * 删除
     * @param id
     * @return
     */
    Integer del(@Param("id") Long id);

    /**
     * id查询
     * @param id
     * @return
     */
    TgBusinessFileDto getInfo(@Param("id") Long id);
}
