package com.jmt.util;

import com.jmt.model.auth.LoginUaaUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 获取当前登录用户信息工具类
 * <AUTHOR>
 */
public class LoginUserUtil {

    private final static Logger logger = LoggerFactory.getLogger(LoginUserUtil.class);

    public static LoginUaaUser get() {
        LoginUaaUser loginUaaUser = new LoginUaaUser();
        loginUaaUser.setUaaId(1L);
        loginUaaUser.setLoginName("admin");
        logger.info("获取当前登录用户信息:"+ GsonUtil.toJson(loginUaaUser));
        return loginUaaUser;
    }
}
