<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.TgEqPlayListDao">
<!--    分页查询-->
    <select id="getPage" parameterType="com.jmt.model.tg.TgEqPlayListDto" resultType="com.jmt.model.tg.TgEqPlayListDto">
        SELECT
            id,
            tgUserNo,
            fileId,
            fileUrl,
            payType,
            busNo,
            playSeq,
            createTime,
            updateTime,
            isDelete
        FROM tg_eq_play_list
        WHERE
            isDelete = 0
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="tgUserNo != null">
                AND tgUserNo = #{tgUserNo}
            </if>
            <if test="fileId != null">
                AND fileId = #{fileId}
            </if>
            <if test="fileUrl != null">
                AND fileUrl = #{fileUrl}
            </if>
            <if test="payType != null">
                AND payType = #{payType}
            </if>
            <if test="busNo != null">
                AND busNo = #{busNo}
            </if>
            <if test="playSeq != null">
                AND playSeq = #{playSeq}
            </if>
            <if test="createTime != null">
                AND createTime = #{createTime}
            </if>
            <if test="updateTime != null">
                AND updateTime = #{updateTime}
            </if>
        ORDER BY updateTime DESC
    </select>
<!--    id查询-->
    <select id="getInfo" parameterType="java.lang.Long" resultType="com.jmt.model.tg.TgEqPlayListDto">
        SELECT
            id,
            tgUserNo,
            fileId,
            fileUrl,
            payType,
            busNo,
            playSeq,
            createTime,
            updateTime,
            isDelete
        FROM tg_eq_play_list
        WHERE isDelete = 0 AND id = #{id}
    </select>
<!--    编辑-->
    <update id="update" parameterType="com.jmt.model.tg.TgEqPlayListDto">
        UPDATE tg_eq_play_list SET
        <if test="tgUserNo != null">tgUserNo = #{tgUserNo},</if>
        <if test="fileId != null">fileId = #{fileId},</if>
        <if test="fileUrl != null">fileUrl = #{fileUrl},</if>
        <if test="payType != null">payType = #{payType},</if>
        <if test="busNo != null">busNo = #{busNo},</if>
        <if test="playSeq != null">playSeq = #{playSeq},</if>
        <if test="createTime != null">createTime = #{createTime},</if>
        updateTime = NOW()
        WHERE id = #{id}
    </update>
<!--    新增-->
    <insert id="add" parameterType="com.jmt.model.tg.TgEqPlayListDto" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO tg_eq_play_list(
                tgUserNo,
                fileId,
                fileUrl,
                payType,
                busNo,
                playSeq,
                createTime,
                updateTime,
                isDelete
        )
        VALUES (
                   #{tgUserNo},
                   #{fileId},
                   #{fileUrl},
                   #{payType},
                   #{busNo},
                   #{playSeq},
                   NOW(),
                   NOW(),
                   0
               )
    </insert>
<!--    删除-->
    <delete id="delete" parameterType="java.lang.Long">
        UPDATE tg_eq_play_list SET
                        isDelete = 1,
                        updateTime = NOW()

    </delete>

</mapper>
