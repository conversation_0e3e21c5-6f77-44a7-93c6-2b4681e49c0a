<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.TgBusinessFileDao">
<!--    分页查询-->
    <select id="getPage" parameterType="com.jmt.model.tg.TgBusinessFileDto" resultType="com.jmt.model.tg.TgBusinessFileDto">
        SELECT
            id,
            busNo,
            upUser,
            fileName,
            fileRealName,
            fileUrl,
            fileSize,
            fileMd5,
            fileType,
            videoDuration,
            playStatus,
            createTime,
            updateTime,
            isDelete
        FROM tg_business_file
        WHERE
            isDelete = 0
        <if test="id != null">
            AND id = #{id}
        </if>
        <if test="busNo != null">
            AND busNo = #{busNo}
        </if>
        <if test="upUser != null">
            AND upUser = #{upUser}
        </if>
        <if test="fileName != null">
            AND fileName = #{fileName}
        </if>
        <if test="fileRealName != null">
            AND fileRealName = #{fileRealName}
        </if>
        <if test="fileUrl != null">
            AND fileUrl = #{fileUrl}
        </if>
        <if test="fileSize != null">
            AND fileSize = #{fileSize}
        </if>
        <if test="fileMd5 != null">
            AND fileMd5 = #{fileMd5}
        </if>
        <if test="fileType != null">
            AND fileType = #{fileType}
        </if>
        <if test="videoDuration != null">
            AND videoDuration = #{videoDuration}
        </if>
        <if test="playStatus != null">
            AND playStatus = #{playStatus}
        </if>
        <if test="createTime != null">
            AND createTime = #{createTime}
        </if>
        <if test="updateTime != null">
            AND updateTime = #{updateTime}
        </if>
        ORDER BY updateTime DESC
    </select>
<!--    id查询-->
    <select id="getInfo" resultType="com.jmt.model.tg.TgBusinessFileDto" parameterType="java.lang.Long">
        SELECT
            id,
            busNo,
            upUser,
            fileName,
            fileRealName,
            fileUrl,
            fileSize,
            fileMd5,
            fileType,
            videoDuration,
            playStatus,
            createTime,
            updateTime,
            isDelete
        FROM tg_business_file
        WHERE isDelete = 0 AND id = #{id}
    </select>
<!--    更新-->
    <update id="update" parameterType="com.jmt.model.tg.TgBusinessFileDto" >
        UPDATE tg_business_file SET

            <if test="busNo != null">busNo = #{busNo},</if>
            <if test="upUser != null">upUser = #{upUser},</if>
            <if test="fileName != null">fileName = #{fileName},</if>
            <if test="fileRealName != null">fileRealName = #{fileRealName},</if>
            <if test="fileUrl != null">fileUrl = #{fileUrl},</if>
            <if test="fileSize != null">fileSize = #{fileSize},</if>
            <if test="fileMd5 != null">fileMd5 = #{fileMd5},</if>
            <if test="fileType != null">fileType = #{fileType},</if>
            <if test="videoDuration != null">videoDuration = #{videoDuration},</if>
            <if test="playStatus != null">playStatus = #{playStatus},</if>
            <if test="createTime != null">createTime = #{createTime},</if>
            <if test="isDelete != null">isDelete = #{isDelete},</if>
            updateTime = NOW()
        WHERE id = #{id}
    </update>
<!--    删除-->
    <delete id="del" parameterType="java.lang.Long" >
        UPDATE tg_business_file SET
                                    isDelete = 1,
                                    updateTime = NOW()
        WHERE id = #{id}
    </delete>
<!--    添加-->
    <insert id="add" parameterType="com.jmt.model.tg.TgBusinessFileDto" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tg_business_file (
                busNo,
                upUser,
                fileName,
                fileRealName,
                fileUrl,
                fileSize,
                fileMd5,
                fileType,
                videoDuration,
                playStatus,
                createTime,
                updateTime,
                isDelete
        )
        VALUES(
            #{busNo},
            #{upUser},
            #{fileName},
            #{fileRealName},
            #{fileUrl},
            #{fileSize},
            #{fileMd5},
            #{fileType},
            #{videoDuration},
            #{playStatus},
            NOW(),
            NOW(),
            0)
    </insert>
</mapper>
