<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.TgAppPlayListDao">

    <resultMap id="BaseResultMap" type="com.jmt.model.dto.TgAppPlayListDto">
        <id column="id" property="id" />
        <result column="tgUserNo" property="tgUserNo" />
        <result column="fileId" property="fileId" />
        <result column="fileUrl" property="fileUrl" />
        <result column="payPosition" property="payPosition" />
        <result column="playApp" property="playApp" />
        <result column="playSeq" property="playSeq" />
        <result column="createTime" property="createTime" />
        <result column="updateTime" property="updateTime" />
        <result column="isDelete" property="isDelete" />
    </resultMap>

    <sql id="Base_Column_List">
        id, tgUserNo, fileId, fileUrl, payPosition, playApp, playSeq,
        createTime, updateTime, isDelete
    </sql>

    <!-- 分页查询 -->
    <select id="pageQuery" resultMap="BaseResultMap" parameterType="com.jmt.model.dto.TgAppPlayListDto">
        SELECT
        <include refid="Base_Column_List" />
        FROM tg_app_play_list
        WHERE isDelete = 0
        <if test="tgUserNo != null and tgUserNo != ''">
            AND tgUserNo = #{tgUserNo}
        </if>
        <if test="playApp != null and playApp != ''">
            AND playApp = #{playApp}
        </if>
        <if test="payPosition != null and payPosition != ''">
            AND payPosition LIKE CONCAT('%', #{payPosition}, '%')
        </if>
        ORDER BY playSeq ASC
    </select>

    <!-- 新增 -->
    <insert id="add" parameterType="com.jmt.model.dto.TgAppPlayListDto" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tg_app_play_list (
            tgUserNo, fileId, fileUrl, payPosition, playApp, playSeq
        ) VALUES (
                     #{tgUserNo}, #{fileId}, #{fileUrl}, #{payPosition}, #{playApp}, #{playSeq}
                 )
    </insert>

    <!-- 按ID查询 -->
    <select id="getById" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT
        <include refid="Base_Column_List" />
        FROM tg_app_play_list
        WHERE id = #{id} AND isDelete = 0
    </select>

    <!-- 修改 -->
    <update id="update" parameterType="com.jmt.model.dto.TgAppPlayListDto">
        UPDATE tg_app_play_list
        SET
            tgUserNo = #{tgUserNo},
            fileId = #{fileId},
            fileUrl = #{fileUrl},
            payPosition = #{payPosition},
            playApp = #{playApp},
            playSeq = #{playSeq},
            updateTime = NOW()
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除（逻辑删除） -->
    <update id="deleteById" parameterType="java.lang.Long">
        UPDATE tg_app_play_list
        SET isDelete = 1
        WHERE id = #{id}
    </update>
</mapper>