<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.TgBusinessOrderDao">
<!--    分页查询-->
    <select id="getPage" parameterType="com.jmt.model.tg.TgBusinessOrderDto" resultType="com.jmt.model.tg.TgBusinessOrderDto">
        SELECT
            id,
            orderNo,
            orderName,
            busNo,
            handleUser,
            playPosition,
            fileId,
            playStatus,
            auditStatus,
            reason,
            createTime,
            updateTime,
            isDelete
        FROM tg_business_order
        WHERE
            isDelete = 0
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="orderNo != null">
                AND orderNo = #{orderNo}
            </if>
            <if test="orderName != null">
                AND orderName = #{orderName}
            </if>
            <if test="busNo != null">
                AND busNo = #{busNo}
            </if>
            <if test="handleUser != null">
                AND handleUser = #{handleUser}
            </if>
            <if test="playPosition != null">
                AND playPosition = #{playPosition}
            </if>
            <if test="fileId != null">
                AND fileId = #{fileId}
            </if>
            <if test="playStatus != null">
                AND playStatus = #{playStatus}
            </if>
            <if test="auditStatus != null">
                AND auditStatus = #{auditStatus}
            </if>
            <if test="reason != null">
                AND reason = #{reason}
            </if>
            <if test="createTime != null">
                AND createTime = #{createTime}
            </if>
            <if test="updateTime != null">
                AND updateTime = #{updateTime}
            </if>
            ORDER BY updateTime DESC
    </select>
<!--    id查询-->
    <select id="getInfo" parameterType="java.lang.Long" resultType="com.jmt.model.tg.TgBusinessOrderDto">
        SELECT
            id,
            orderNo,
            orderName,
            busNo,
            handleUser,
            playPosition,
            fileId,
            playStatus,
            auditStatus,
            reason,
            createTime,
            updateTime,
            isDelete
        FROM tg_business_order
        WHERE isDelete = 0 AND id = #{id}
    </select>
<!--    添加-->
    <insert id="add" parameterType="com.jmt.model.tg.TgBusinessOrderDto" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO tg_business_order(
            orderNo,
            orderName,
            busNo,
            handleUser,
            playPosition,
            fileId,
            playStatus,
            auditStatus,
            reason,
            createTime,
            updateTime,
            isDelete
        )
        VALUES (
                   #{orderNo},
                   #{orderName},
                   #{busNo},
                   #{handleUser},
                   #{playPosition},
                   #{fileId},
                   #{playStatus},
                   #{auditStatus},
                   #{reason},
                   NOW(),
                   NOW(),
                   0
               )
    </insert>
<!--   更新 -->
    <update id="update" parameterType="com.jmt.model.tg.TgBusinessOrderDto" >
        update tg_business_order SET
        <if test="orderNo != null">orderNo = #{orderNo},</if>
        <if test="orderName != null">orderName = #{orderName},</if>
        <if test="busNo != null">busNo = #{busNo},</if>
        <if test="handleUser != null">handleUser = #{handleUser},</if>
        <if test="playPosition != null">playPosition = #{playPosition},</if>
        <if test="fileId != null">fileId = #{fileId},</if>
        <if test="playStatus != null">playStatus = #{playStatus},</if>
        <if test="auditStatus != null">auditStatus = #{auditStatus},</if>
        <if test="reason != null">reason = #{reason},</if>
        <if test="createTime != null">createTime = #{createTime},</if>
        <if test="updateTime != null">updateTime = #{updateTime},</if>
        <if test="isDelete != null">isDelete = #{isDelete},</if>
        updateTime = NOW()
        WHERE
             id = #{id}
    </update>
<!--    删除-->
    <delete id="del" parameterType="java.lang.Long" >
        UPDATE tg_business_order SET
                                     isDelete = 1,
                                     updateTime = NOW()
        WHERE isDelete = 0 AND id = #{id}
    </delete>


</mapper>
