<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.TgAppOrderDao">

    <resultMap id="BaseResultMap" type="com.jmt.model.dto.TgAppOrderDto">
        <id column="id" property="id" />
        <result column="orderNo" property="orderNo" />
        <result column="orderName" property="orderName" />
        <result column="tgUserNo" property="tgUserNo" />
        <result column="handleUser" property="handleUser" />
        <result column="playPosition" property="playPosition" />
        <result column="playApp" property="playApp" />
        <result column="playStartTime" property="playStartTime" />
        <result column="playEndTime" property="playEndTime" />
        <result column="fileId" property="fileId" />
        <result column="playDays" property="playDays" />
        <result column="sourceNum" property="sourceNum" />
        <result column="payType" property="payType" />
        <result column="payAmount" property="payAmount" />
        <result column="payStauts" property="payStauts" />
        <result column="playStatus" property="playStatus" />
        <result column="auditStatus" property="auditStatus" />
        <result column="reason" property="reason" />
        <result column="createTime" property="createTime" />
        <result column="updateTime" property="updateTime" />
        <result column="isDelete" property="isDelete" />
    </resultMap>

    <sql id="Base_Column_List">
        id, orderNo, orderName, tgUserNo, handleUser, playPosition, playApp,
        playStartTime, playEndTime, fileId, playDays, sourceNum, payType,
        payAmount, payStauts, playStatus, auditStatus, reason, createTime,
        updateTime, isDelete
    </sql>

    <!-- 分页查询 -->
    <select id="pageQuery" resultMap="BaseResultMap" parameterType="com.jmt.model.dto.TgAppOrderDto">
        SELECT
        <include refid="Base_Column_List" />
        FROM tg_app_order
        WHERE isDelete = 0
        <if test="orderNo != null and orderNo != ''">
            AND orderNo LIKE CONCAT('%', #{orderNo}, '%')
        </if>
        <if test="orderName != null and orderName != ''">
            AND orderName LIKE CONCAT('%', #{orderName}, '%')
        </if>
        <if test="tgUserNo != null and tgUserNo != ''">
            AND tgUserNo = #{tgUserNo}
        </if>
        <if test="playStatus != null">
            AND playStatus = #{playStatus}
        </if>
        <if test="auditStatus != null">
            AND auditStatus = #{auditStatus}
        </if>
    </select>

    <!-- 新增 -->
    <insert id="add" parameterType="com.jmt.model.dto.TgAppOrderDto" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tg_app_order (
            orderNo, orderName, tgUserNo, handleUser, playPosition,
            playApp, playStartTime, playEndTime, fileId, playDays,
            sourceNum, payType, payAmount, payStauts, playStatus,
            auditStatus, reason
        ) VALUES (
                     #{orderNo}, #{orderName}, #{tgUserNo}, #{handleUser}, #{playPosition},
                     #{playApp}, #{playStartTime}, #{playEndTime}, #{fileId}, #{playDays},
                     #{sourceNum}, #{payType}, #{payAmount}, #{payStauts}, #{playStatus},
                     #{auditStatus}, #{reason}
                 )
    </insert>

    <!-- 按ID查询 -->
    <select id="getById" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT
        <include refid="Base_Column_List" />
        FROM tg_app_order
        WHERE id = #{id} AND isDelete = 0
    </select>

    <!-- 修改 -->
    <update id="update" parameterType="com.jmt.model.dto.TgAppOrderDto">
        UPDATE tg_app_order
        SET
            orderNo = #{orderNo},
            orderName = #{orderName},
            tgUserNo = #{tgUserNo},
            handleUser = #{handleUser},
            playPosition = #{playPosition},
            playApp = #{playApp},
            playStartTime = #{playStartTime},
            playEndTime = #{playEndTime},
            fileId = #{fileId},
            playDays = #{playDays},
            sourceNum = #{sourceNum},
            payType = #{payType},
            payAmount = #{payAmount},
            payStauts = #{payStauts},
            playStatus = #{playStatus},
            auditStatus = #{auditStatus},
            reason = #{reason},
            updateTime = NOW()
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除（逻辑删除） -->
    <update id="deleteById" parameterType="java.lang.Long">
        UPDATE tg_app_order
        SET isDelete = 1
        WHERE id = #{id}
    </update>
</mapper>