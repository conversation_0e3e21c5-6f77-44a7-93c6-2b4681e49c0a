<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.TgUserContractDao">
<!--    分页查询-->
    <select id="getPage" parameterType="com.jmt.model.tg.TgUserContractDto" resultType="com.jmt.model.tg.TgUserContractDto">
        SELECT
            id,
            contractNo,
            contractName,
            tgUserNo,
            tgUserName,
            handleUser,
            amount,
            sourceNum,
            contractFile,
            content,
            contractStartTime,
            contractEndTime,
            createTime,
            updateTime
        FROM tg_user_contract
        WHERE
            isDelete = 0
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="contractNo != null">
                AND contractNo = #{contractNo}
            </if>
            <if test="contractName != null">
                AND contractName = #{contractName}
            </if>
            <if test="tgUserNo != null">
                AND tgUserNo = #{tgUserNo}
            </if>
            <if test="tgUserName != null">
                AND tgUserName = #{tgUserName}
            </if>
            <if test="handleUser != null">
                AND handleUser = #{handleUser}
            </if>
            <if test="amount != null">
                AND amount = #{amount}
            </if>
            <if test="sourceNum != null">
                AND sourceNum = #{sourceNum}
            </if>
            <if test="contractFile != null">
                AND contractFile = #{contractFile}
            </if>
            <if test="content != null">
                AND content = #{content}
            </if>
            <if test="contractStartTime != null">
                AND contractStartTime = #{contractStartTime}
            </if>
            <if test="contractEndTime != null">
                AND contractEndTime = #{contractEndTime}
            </if>
            <if test="createTime != null">
                AND createTime = #{createTime}
            </if>
            <if test="updateTime != null">
                AND updateTime = #{updateTime}
            </if>
        ORDER BY updateTime DESC
    </select>
<!--    根据id查询-->
    <select id="getInfo" parameterType="java.lang.Long" resultType="com.jmt.model.tg.TgUserContractDto">
        SELECT
            id,
            contractNo,
            contractName,
            tgUserNo,
            tgUserName,
            handleUser,
            amount,
            sourceNum,
            contractFile,
            content,
            contractStartTime,
            contractEndTime,
            createTime,
            updateTime
        FROM tg_user_contract
        WHERE
            isDelete = 0 AND id = #{id}
    </select>
<!--    根据id更新-->
    <update id="update" parameterType="com.jmt.model.tg.TgUserContractDto">
        UPDATE tg_user_contract SET
                        <if test="contractNo != null">contractNo = #{contractNo},</if>
                        <if test="contractName != null">contractName = #{contractName},</if>
                        <if test="tgUserNo != null">tgUserNo = #{tgUserNo},</if>
                        <if test="tgUserName != null">tgUserName = #{tgUserName},</if>
                        <if test="handleUser != null">handleUser = #{handleUser},</if>
                        <if test="amount != null">amount = #{amount},</if>
                        <if test="sourceNum != null">sourceNum = #{sourceNum},</if>
                        <if test="contractFile != null">contractFile = #{contractFile},</if>
                        <if test="content != null">content = #{content},</if>
                        <if test="contractStartTime != null">contractStartTime = #{contractStartTime},</if>
                        <if test="contractEndTime != null">contractEndTime = #{contractEndTime},</if>
                        <if test="isDelete != null">isDelete = #{isDelete},</if>
                        updateTime = NOW()
        WHERE
            isDelete = 0 AND id = #{id}
    </update>
<!--    新增-->
    <insert id="add" parameterType="com.jmt.model.tg.TgUserContractDto" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO tg_user_contract(
                                     contractNo,
                                     contractName,
                                     tgUserNo,
                                     tgUserName,
                                     handleUser,
                                     amount,
                                     sourceNum,
                                     contractFile,
                                     content,
                                     contractStartTime,
                                     contractEndTime,
                                     createTime,
                                     updateTime,
                                    isDelete)
        VALUES (
                #{contractNo},
                #{contractName},
                #{tgUserNo},
                #{tgUserName},
                #{handleUser},
                #{amount},
                #{sourceNum},
                #{contractFile},
                #{content},
                #{contractStartTime},
                #{contractEndTime},
                NOW(),
                NOW(),
                0
               )
    </insert>
<!--    删除-->
    <delete id="delete" parameterType="java.lang.Long">
        UPDATE tg_user_contract SET
                                    isDelete = 1,
                                    updateTime = NOW()
        WHERE
             id = #{id}
    </delete>
</mapper>
