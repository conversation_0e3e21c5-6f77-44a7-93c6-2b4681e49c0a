<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.TgVideoOrderDetailDao">

    <resultMap id="BaseResultMap" type="com.jmt.model.dto.TgVideoOrderDetailDto">
        <result column="orderNo" property="orderNo" />
        <result column="fileId" property="fileId" />
        <result column="fileUrl" property="fileUrl" />
        <result column="busNo" property="busNo" />
        <result column="createTime" property="createTime" />
        <result column="updateTime" property="updateTime" />
        <result column="isDelete" property="isDelete" />
    </resultMap>

    <sql id="Base_Column_List">
        orderNo, fileId, fileUrl, busNo, createTime, updateTime, isDelete
    </sql>

    <!-- 分页查询 -->
    <select id="pageQuery" resultMap="BaseResultMap" parameterType="com.jmt.model.dto.TgVideoOrderDetailDto">
        SELECT
        <include refid="Base_Column_List" />
        FROM tg_video_order_detail
        WHERE isDelete = 0
        <if test="orderNo != null and orderNo != ''">
            AND orderNo LIKE CONCAT('%', #{orderNo}, '%')
        </if>
        <if test="busNo != null and busNo != ''">
            AND busNo LIKE CONCAT('%', #{busNo}, '%')
        </if>
    </select>

    <!-- 新增 -->
    <insert id="add" parameterType="com.jmt.model.dto.TgVideoOrderDetailDto">
        INSERT INTO tg_video_order_detail (
            orderNo, fileId, fileUrl, busNo
        ) VALUES (
                     #{orderNo}, #{fileId}, #{fileUrl}, #{busNo}
                 )
    </insert>

    <!-- 按id查询 -->
    <select id="getById" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
        <include refid="Base_Column_List" />
        FROM tg_video_order_detail
        WHERE id = #{id} AND isDelete = 0
    </select>

    <!-- 修改 -->
    <update id="update" parameterType="com.jmt.model.dto.TgVideoOrderDetailDto">
        UPDATE tg_video_order_detail
        SET
            fileId = #{fileId},
            fileUrl = #{fileUrl},
            busNo = #{busNo},
            updateTime = NOW()
        WHERE orderNo = #{orderNo}
    </update>

    <!-- 根据id删除（逻辑删除） -->
    <update id="deleteById" parameterType="java.lang.String">
        UPDATE tg_video_order_detail
        SET isDelete = 1
        WHERE id = #{id}
    </update>
</mapper>