<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.TgEqOrderDao">
<!--    分页查询-->
    <select id="getPage" resultType="com.jmt.model.tg.TgEqOrderDto" parameterType="com.jmt.model.tg.TgEqOrderDto">
        SELECT
            id,
            orderNo,
            orderName,
            tgUserNo,
            handleUser,
            playType,
            playStartTime,
            playEndTime,
            fileId,
            playbusNum,
            playEqNum,
            playDays,
            sourceNum,
            payType,
            payAmount,
            payStauts,
            playStatus,
            auditStatus,
            reason,
            createTime,
            updateTime,
            isDelete
        FROM tg_eq_order
        WHERE
            isDelete = 0
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="orderNo != null">
                AND orderNo = #{orderNo}
            </if>
            <if test="orderName != null">
                AND orderName = #{orderName}
            </if>
            <if test="tgUserNo != null">
                AND tgUserNo = #{tgUserNo}
            </if>
            <if test="handleUser != null">
                AND handleUser = #{handleUser}
            </if>
            <if test="playType != null">
                AND playType = #{playType}
            </if>
            <if test="playStartTime != null">
                AND playStartTime = #{playStartTime}
            </if>
            <if test="playEndTime != null">
                AND playEndTime = #{playEndTime}
            </if>
            <if test="fileId != null">
                AND fileId = #{fileId}
            </if>
            <if test="playbusNum != null">
                AND playbusNum = #{playbusNum}
            </if>
            <if test="playEqNum != null">
                AND playEqNum = #{playEqNum}
            </if>
            <if test="playDays != null">
                AND playDays = #{playDays}
            </if>
            <if test="sourceNum != null">
                AND sourceNum = #{sourceNum}
            </if>
            <if test="payType != null">
                AND payType = #{payType}
            </if>
            <if test="payAmount != null">
                AND payAmount = #{payAmount}
            </if>
            <if test="payStauts != null">
                AND payStauts = #{payStauts}
            </if>
            <if test="playStatus != null">
                AND playStatus = #{playStatus}
            </if>
            <if test="auditStatus != null">
                AND auditStatus = #{auditStatus}
            </if>
            <if test="reason != null">
                AND reason = #{reason}
            </if>
            <if test="createTime != null">
                AND createTime = #{createTime}
            </if>
            <if test="updateTime != null">
                AND updateTime = #{updateTime}
            </if>
            ORDER BY updateTime DESC
    </select>
<!--    id查询-->
    <select id="getInfo" parameterType="java.lang.Long" resultType="com.jmt.model.tg.TgEqOrderDto">
        SELECT
            id,
            orderNo,
            orderName,
            tgUserNo,
            handleUser,
            playType,
            playStartTime,
            playEndTime,
            fileId,
            playbusNum,
            playEqNum,
            playDays,
            sourceNum,
            payType,
            payAmount,
            payStauts,
            playStatus,
            auditStatus,
            reason,
            createTime,
            updateTime,
            isDelete
        FROM tg_eq_order
        WHERE isDelete = 0 AND id = #{id}
    </select>
<!--    编辑-->
    <update id="update" parameterType="com.jmt.model.tg.TgEqOrderDto">
        UPDATE tg_eq_order SET
            <if test="orderNo != null">orderNo = #{orderNo},</if>
            <if test="orderName != null">orderName = #{orderName},</if>
            <if test="tgUserNo != null">tgUserNo = #{tgUserNo},</if>
            <if test="handleUser != null">handleUser = #{handleUser},</if>
            <if test="playType != null">playType = #{playType},</if>
            <if test="playStartTime != null">playStartTime = #{playStartTime},</if>
            <if test="playEndTime != null">playEndTime = #{playEndTime},</if>
            <if test="fileId != null">fileId = #{fileId},</if>
            <if test="playbusNum != null">playbusNum = #{playbusNum},</if>
            <if test="playEqNum != null">playEqNum = #{playEqNum},</if>
            <if test="playDays != null">playDays = #{playDays},</if>
            <if test="sourceNum != null">sourceNum = #{sourceNum},</if>
            <if test="payType != null">payType = #{payType},</if>
            <if test="payAmount != null">payAmount = #{payAmount},</if>
            <if test="payStauts != null">payStauts = #{payStauts},</if>
            <if test="playStatus != null">playStatus = #{playStatus},</if>
            <if test="auditStatus != null">auditStatus = #{auditStatus},</if>
            <if test="reason != null">reason = #{reason},</if>
            updateTime = NOW()
        WHERE  id = #{id}
    </update>
<!--    删除-->
    <delete id="delete" parameterType="java.lang.Long">
        UPDATE tg_eq_order SET
                               isDelete = 0,
                               updateTime = NOW()
        WHERE
            id = #{id}
    </delete>
<!--    新增-->
    <insert id="add" parameterType="com.jmt.model.tg.TgEqOrderDto" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO tg_eq_order(
                orderNo,
                orderName,
                tgUserNo,
                handleUser,
                playType,
                playStartTime,
                playEndTime,
                fileId,
                playbusNum,
                playEqNum,
                playDays,
                sourceNum,
                payType,
                payAmount,
                payStauts,
                playStatus,
                auditStatus,
                reason,
                createTime,
                updateTime,
                isDelete
        )
        VALUES (#{orderNo},
                #{orderName},
                #{tgUserNo},
                #{handleUser},
                #{playType},
                #{playStartTime},
                #{playEndTime},
                #{fileId},
                #{playbusNum},
                #{playEqNum},
                #{playDays},
                #{sourceNum},
                #{payType},
                #{payAmount},
                #{payStauts},
                #{playStatus},
                #{auditStatus},
                #{reason},
                NOW(),
                NOW(),
                0)
    </insert>
</mapper>
