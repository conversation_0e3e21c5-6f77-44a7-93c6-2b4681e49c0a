<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.TgEqFileDao">
<!--    分页查询-->
    <select id="getPage" parameterType="com.jmt.model.tg.TgEqFileDto" resultType="com.jmt.model.tg.TgEqFileDto">
        SELECT
            id,
            tgUserNo,
            upUser,
            fileName,
            fileRealName,
            fileUrl,
            fileSize,
            fileMd5,
            fileType,
            videoDuration,
            playStatus,
            createTime,
            updateTime,
            isDelete
        FROM tg_eq_file
        WHERE
            isDelete = 0
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="tgUserNo != null">
                AND tgUserNo = #{tgUserNo}
            </if>
            <if test="upUser != null">
                AND upUser = #{upUser}
            </if>
            <if test="fileName != null">
                AND fileName = #{fileName}
            </if>
            <if test="fileRealName != null">
                AND fileRealName = #{fileRealName}
            </if>
            <if test="fileUrl != null">
                AND fileUrl = #{fileUrl}
            </if>
            <if test="fileSize != null">
                AND fileSize = #{fileSize}
            </if>
            <if test="fileMd5 != null">
                AND fileMd5 = #{fileMd5}
            </if>
            <if test="fileType != null">
                AND fileType = #{fileType}
            </if>
            <if test="videoDuration != null">
                AND videoDuration = #{videoDuration}
            </if>
            <if test="playStatus != null">
                AND playStatus = #{playStatus}
            </if>
            <if test="createTime != null">
                AND createTime = #{createTime}
            </if>
            <if test="updateTime != null">
                AND updateTime = #{updateTime}
            </if>
            <if test="isDelete != null">
                AND isDelete = #{isDelete}
            </if>
        ORDER BY updateTime DESC
    </select>
<!--    id查询-->
    <select id="getInfo" parameterType="java.lang.Long" resultType="com.jmt.model.tg.TgEqFileDto">
        SELECT
            id,
            tgUserNo,
            upUser,
            fileName,
            fileRealName,
            fileUrl,
            fileSize,
            fileMd5,
            fileType,
            videoDuration,
            playStatus,
            createTime,
            updateTime,
            isDelete
        FROM tg_eq_file
        WHERE isDelete = 0 AND id = #{id}
        </select>
<!--    更新-->
    <update id="update" parameterType="com.jmt.model.tg.TgEqFileDto">
        UPDATE tg_eq_file SET
        <if test="tgUserNo != null">tgUserNo = #{tgUserNo},</if>
        <if test="upUser != null">upUser = #{upUser},</if>
        <if test="fileName != null">fileName = #{fileName},</if>
        <if test="fileRealName != null">fileRealName = #{fileRealName},</if>
        <if test="fileUrl != null">fileUrl = #{fileUrl},</if>
        <if test="fileSize != null">fileSize = #{fileSize},</if>
        <if test="fileMd5 != null">fileMd5 = #{fileMd5},</if>
        <if test="fileType != null">fileType = #{fileType},</if>
        <if test="videoDuration != null">videoDuration = #{videoDuration},</if>
        <if test="playStatus != null">playStatus = #{playStatus},</if>
        <if test="createTime != null">createTime = #{createTime},</if>
        <if test="isDelete != null">isDelete = #{isDelete},</if>
                updateTime = NOW()
        WHERE
            isDelete = 0 AND id = #{id}
    </update>
<!--    新增-->
    <insert id="insert" parameterType="com.jmt.model.tg.TgEqFileDto" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO tg_eq_file(
                            tgUserNo,
                            upUser,
                            fileName,
                            fileRealName,
                            fileUrl,
                            fileSize,
                            fileMd5,
                            fileType,
                            videoDuration,
                            playStatus,
                            createTime,
                            updateTime,
                            isDelete
                         )
        VALUES (
                   #{tgUserNo},
                   #{upUser},
                   #{fileName},
                   #{fileRealName},
                   #{fileUrl},
                   #{fileSize},
                   #{fileMd5},
                   #{fileType},
                   #{videoDuration},
                   #{playStatus},
                   NOW(),
                   NOW(),
                   0
               )
    </insert>
<!--    删除-->
    <delete id="delete" parameterType="java.lang.Long" >
        UPDATE tg_eq_file SET
                              isDelete = 1,
                              updateTime = NOW()
        WHERE
            id = #{id}
    </delete>

</mapper>
