<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.TgVideoPlayListDao">

    <resultMap id="BaseResultMap" type="com.jmt.model.dto.TgVideoPlayListDto">
        <id column="id" property="id" />
        <result column="fileId" property="fileId" />
        <result column="fileUrl" property="fileUrl" />
        <result column="busNo" property="busNo" />
        <result column="playSeq" property="playSeq" />
        <result column="createTime" property="createTime" />
        <result column="updateTime" property="updateTime" />
        <result column="isDelete" property="isDelete" />
    </resultMap>

    <sql id="Base_Column_List">
        id, fileId, fileUrl, busNo, playSeq, createTime, updateTime, isDelete
    </sql>

    <!-- 分页查询 -->
    <select id="pageQuery" resultMap="BaseResultMap" parameterType="com.jmt.model.dto.TgVideoPlayListDto">
        SELECT
        <include refid="Base_Column_List" />
        FROM tg_video_play_list
        WHERE isDelete = 0
        <if test="busNo != null and busNo != ''">
            AND busNo LIKE CONCAT('%', #{busNo}, '%')
        </if>
        <if test="fileId != null and fileId != ''">
            AND fileId LIKE CONCAT('%', #{fileId}, '%')
        </if>
    </select>

    <!-- 新增 -->
    <insert id="add" parameterType="com.jmt.model.dto.TgVideoPlayListDto" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tg_video_play_list (
            fileId, fileUrl, busNo, playSeq
        ) VALUES (
                     #{fileId}, #{fileUrl}, #{busNo}, #{playSeq}
                 )
    </insert>

    <!-- 按ID查询 -->
    <select id="getById" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT
        <include refid="Base_Column_List" />
        FROM tg_video_play_list
        WHERE id = #{id} AND isDelete = 0
    </select>

    <!-- 修改 -->
    <update id="update" parameterType="com.jmt.model.dto.TgVideoPlayListDto">
        UPDATE tg_video_play_list
        SET
            fileId = #{fileId},
            fileUrl = #{fileUrl},
            busNo = #{busNo},
            playSeq = #{playSeq},
            updateTime = NOW()
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除（逻辑删除） -->
    <update id="deleteById" parameterType="java.lang.Long">
        UPDATE tg_video_play_list
        SET isDelete = 1
        WHERE id = #{id}
    </update>
</mapper>