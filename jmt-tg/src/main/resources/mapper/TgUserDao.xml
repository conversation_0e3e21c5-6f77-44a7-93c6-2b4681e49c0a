<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.TgUserDao">
    <!--分页查询-->
    <select id="getPage" parameterType="com.jmt.model.tg.TgUserDto" resultType="com.jmt.model.tg.TgUserDto">
    SELECT
        id,
        uaaId,
        tgUserNo,
        tgUserName,
        headImg,
        nickname,
        telPhone,
        email,
        country,
        province,
        city,
        area,
        address,
        roleType,
        createTime,
        updateTime
    FROM
        tg_user
    WHERE
        isDelete = 0
        <if test="id != null">
            AND id = #{id}
        </if>
        <if test="uaaId != null">
            AND uaald = #{uaaId}
        </if>
        <if test="tgUserNo != null">
            AND tgUserNo = #{tgUserNo}
        </if>
        <if test="tgUserName != null">
            AND tgUserName = #{tgUserName}
        </if>
        <if test="headImg != null">
            AND headImg = #{headImg}
        </if>
        <if test="nickName != null">
            AND nickName = #{nickName}
        </if>
        <if test="telPhone != null">
            AND telPhone = #{telPhone}
        </if>
        <if test="email != null">
            AND email = #{email}
        </if>
        <if test="country != null">
            AND country = #{country}
        </if>
        <if test="province != null">
            AND province = #{province}
        </if>
        <if test="city != null">
            AND city = #{city}
        </if>
        <if test="area != null">
            AND area = #{area}
        </if>
        <if test="address != null">
            AND address = #{address}
        </if>
        <if test="roleType != null">
            AND roleType = #{roleType}
        </if>
        <if test="createTime != null">
            AND createTime = #{createTime}
        </if>
        <if test="updateTime != null">
            AND updateTime = #{updateTime}
        </if>
    ORDER BY updateTime DESC
    </select>
    <!--id查询-->
    <select id="getInfo" parameterType="java.lang.Long" resultType="com.jmt.model.tg.TgUserDto">
        SELECT
            uaaId,
            tgUserNo,
            tgUserName,
            headImg,
            nickname,
            telPhone,
            email,
            country,
            province,
            city,
            area,
            address,
            roleType,
            createTime,
            updateTime
        FROM
            tg_user
        WHERE
            isDelete = 0 AND id = #{id}
    </select>
    <!--新增-->
    <insert id="insert" parameterType="com.jmt.dao.TgUserDao" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO tg_User(
            uaaId,
            tgUserNo,
            tgUserName,
            headImg,
            nickname,
            sex,
            telPhone,
            email,
            country,
            province,
            city,
            area,
            address,
            roleType,
            isFirstLogin,
            createTime,
            updateTime,
            isDelete
        )
        VALUES (#{uaaId},
                #{tgUserNo},
                #{tgUserName},
                #{headImg},
                #{nickname},
                #{sex},
                #{telPhone},
                #{email},
                #{country},
                #{province},
                #{city},
                #{area},
                #{address},
                #{roleType},
                #{isFirstLogin},
                Now(),
                Now(),
                #{isDelete})
    </insert>
    <!--    根据id修改-->
    <update id="update" parameterType="com.jmt.model.tg.TgUserDto">
        UPDATE tg_user SET
                    <if test="uaaId != null">uaaId = #{uaaId},</if>
                    <if test="tgUserNo != null">tgUserNo = #{tgUserNo},</if>
                    <if test="tgUserName != null">tgUserName = #{tgUserName},</if>
                    <if test="headImg != null">headImg = #{headImg},</if>
                    <if test="nickname != null">nickname = #{nickname},</if>
                    <if test="sex != null">sex = #{sex},</if>
                    <if test="telPhone != null">telPhone = #{telPhone},</if>
                    <if test="email != null">email = #{email},</if>
                    <if test="country != null">country = #{country},</if>
                    <if test="province != null">province = #{province},</if>
                    <if test="city != null">city = #{city},</if>
                    <if test="area != null">area = #{area},</if>
                    <if test="address != null">address = #{address},</if>
                    <if test="roleType != null">roleType = #{roleType},</if>
                    <if test="isFirstLogin != null">isFirstLogin = #{isFirstLogin},</if>
                    <if test="isDelete != null">isDelete = #{isDelete},</if>
                    updateTime = NOW()
        WHERE
            isDelete = 0 AND id = #{id}
    </update>
    <!--    根据id删除-->
    <delete id="delete" parameterType="java.lang.Long">
        UPDATE tg_user SET
                           isDelete = 1,
                           updateTime = NOW()
        WHERE
             id = #{id}
    </delete>

</mapper>
