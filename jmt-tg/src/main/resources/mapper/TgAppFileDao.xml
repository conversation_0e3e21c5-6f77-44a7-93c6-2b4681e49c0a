<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.TgAppFileDao">

    <resultMap id="BaseResultMap" type="com.jmt.model.dto.TgAppFileDto">
        <id column="id" property="id" />
        <result column="tgUserNo" property="tgUserNo" />
        <result column="upUser" property="upUser" />
        <result column="fileName" property="fileName" />
        <result column="fileRealName" property="fileRealName" />
        <result column="fileUrl" property="fileUrl" />
        <result column="fileSize" property="fileSize" />
        <result column="fileMd5" property="fileMd5" />
        <result column="fileType" property="fileType" />
        <result column="videoDuration" property="videoDuration" />
        <result column="playStatus" property="playStatus" />
        <result column="createTime" property="createTime" />
        <result column="updateTime" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, tgUserNo, upUser, fileName, fileRealName, fileUrl, fileSize,
        fileMd5, fileType, videoDuration, playStatus, createTime, updateTime
    </sql>

    <!-- 分页查询 -->
    <select id="pageQuery" resultMap="BaseResultMap" parameterType="com.jmt.model.dto.TgAppFileDto">
        SELECT
        <include refid="Base_Column_List" />
        FROM tg_app_file
        WHERE 1=1
        <if test="fileName != null and fileName != ''">
            AND fileName LIKE CONCAT('%', #{fileName}, '%')
        </if>
        <if test="tgUserNo != null and tgUserNo != ''">
            AND tgUserNo = #{tgUserNo}
        </if>
        <if test="upUser != null and upUser != ''">
            AND upUser = #{upUser}
        </if>
    </select>

    <!-- 新增 -->
    <insert id="add" parameterType="com.jmt.model.dto.TgAppFileDto" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tg_app_file (
            tgUserNo, upUser, fileName, fileRealName, fileUrl,
            fileSize, fileMd5, fileType, videoDuration, playStatus
        ) VALUES (
                     #{tgUserNo}, #{upUser}, #{fileName}, #{fileRealName}, #{fileUrl},
                     #{fileSize}, #{fileMd5}, #{fileType}, #{videoDuration}, #{playStatus}
                 )
    </insert>

    <!-- 按ID查询 -->
    <select id="getById" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT
        <include refid="Base_Column_List" />
        FROM tg_app_file
        WHERE id = #{id}
    </select>

    <!-- 修改 -->
    <update id="update" parameterType="com.jmt.model.dto.TgAppFileDto">
        UPDATE tg_app_file
        SET
            tgUserNo = #{tgUserNo},
            upUser = #{upUser},
            fileName = #{fileName},
            fileRealName = #{fileRealName},
            fileUrl = #{fileUrl},
            fileSize = #{fileSize},
            fileMd5 = #{fileMd5},
            fileType = #{fileType},
            videoDuration = #{videoDuration},
            playStatus = #{playStatus},
            updateTime = NOW()
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM tg_app_file
        WHERE id = #{id}
    </delete>
</mapper>