<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.TgEqOrderDetailDao">
<!--    分页查询-->
    <select id="getPage" parameterType="com.jmt.model.tg.TgEqOrderDetailDto" resultType="com.jmt.model.tg.TgEqOrderDetailDto">
        SELECT
            id,
            orderNo,
            tgUserNo,
            fileId,
            fileUrl,
            playType,
            busNo,
            createTime,
            updateTime,
            isDelete
        FROM tg_eq_order_detail
        WHERE
                isDelete = 0
                <if test="id != null">
                    AND id = #{id}
                </if>
                <if test="orderNo != null">
                    AND orderNo = #{orderNo}
                </if>
                <if test="tgUserNo != null">
                    AND tgUserNo = #{tgUserNo}
                </if>
                <if test="fileId != null">
                    AND fileId = #{fileId}
                </if>
                <if test="fileUrl != null">
                    AND fileUrl = #{fileUrl}
                </if>
                <if test="playType != null">
                    AND playType = #{playType}
                </if>
                <if test="busNo != null">
                    AND busNo = #{busNo}
                </if>
                <if test="createTime != null">
                    AND createTime = #{createTime}
                </if>
                <if test="updateTime != null">
                    AND updateTime = #{updateTime}
                </if>
                ORDER BY updateTime DESC
    </select>
<!--    id查询-->
    <select id="getInfo" parameterType="java.lang.Long" resultType="com.jmt.model.tg.TgEqOrderDetailDto">
        SELECT
            id,
            orderNo,
            tgUserNo,
            fileId,
            fileUrl,
            playType,
            busNo,
            createTime,
            updateTime,
            isDelete
        FROM tg_eq_order_detail
        WHERE isDelete = 0 AND id = #{id}
    </select>
<!--    新增-->
    <insert id="insert" parameterType="com.jmt.model.tg.TgEqOrderDetailDto" keyProperty="id" useGeneratedKeys="true">
            INSERT INTO tg_eq_order_detail (
            orderNo,
            tgUserNo,
            fileId,
            fileUrl,
            playType,
            busNo,
            createTime,
            updateTime,
            isDelete)
            VALUES (
                       #{orderNo},
                       #{tgUserNo},
                       #{fileId},
                       #{fileUrl},
                       #{playType},
                       #{busNo},
                       NOW(),
                       NOW(),
                       0
                   )

    </insert>
<!--    编辑-->
    <update id="update" parameterType="com.jmt.model.tg.TgEqOrderDetailDto">
        UPDATE tg_eq_order_detail SET
        <if test="orderNo != null">orderNo = #{orderNo},</if>
        <if test="tgUserNo != null">tgUserNo = #{tgUserNo},</if>
        <if test="fileId != null">fileId = #{fileId},</if>
        <if test="fileUrl != null">fileUrl = #{fileUrl},</if>
        <if test="playType != null">playType = #{playType},</if>
        <if test="busNo != null">busNo = #{busNo},</if>
        <if test="createTime != null">createTime = #{createTime},</if>
        <if test="isDelete != null">isDelete = #{isDelete},</if>
        updateTime = NOW()
        WHERE isDelete = 0 AND id = #{id}
    </update>
<!--    删除-->
    <delete id="delete" parameterType="java.lang.Long">
        UPDATE tg_eq_order_detail SET
                                      isDelete = 1,
                                      updateTime = NOW()
        WHERE isDelete = 0 AND id = #{id}
    </delete>
</mapper>
