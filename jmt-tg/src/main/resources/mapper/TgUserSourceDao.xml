<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.TgUserSourceDao">
<!--    分页查询-->
    <select id="getPage" parameterType="com.jmt.model.tg.TgUserSourceDto" resultType="com.jmt.model.tg.TgUserSourceDto">
        SELECT
            id,
            orderNo,
            orderName,
            contractNo,
            tgUserNo,
            handleUser,
            sourceNum,
            auditStatus,
            reason,
            createTime,
            updateTime,
            isDelete
        FROM tg_user_source_order
        WHERE
            isDelete = 0
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="orderNo != null">
                AND orderNo = #{orderNo}
            </if>
            <if test="orderName != null">
                AND orderName = #{orderName}
            </if>
            <if test="contractNo != null">
                AND contractNo = #{contractNo}
            </if>
            <if test="tgUserNo != null">
                AND tgUserNo = #{tgUserNo}
            </if>
            <if test="handleUer != null">
                AND handleUser = #{handleUser}
            </if>
            <if test="sourceNum != null">
                AND sourceNum = #{sourceNum}
            </if>
            <if test="auditStatus != null">
                AND auditStatus = #{auditStatus}
            </if>
            <if test="reason != null">
                AND reason = #{reason}
            </if>
            <if test="creteTime != null">
                AND createTime = #{createTime}
            </if>
            <if test="updateTime != null">
                AND updateTime = #{updateTime}
            </if>
            <if test="isDelete != null">
                AND isDelete = #{isDelete}
            </if>
            ORDER BY updateTime DESC
    </select>
<!--    依据id查询-->
    <select id="getInfo" parameterType="java.lang.Long" resultType="com.jmt.model.tg.TgUserSourceDto">
        SELECT
            orderNo,
            orderName,
            contractNo,
            tgUserNo,
            handleUser,
            sourceNum,
            auditStatus,
            reason,
            createTime,
            updateTime
        FROM tg_user_source_order
        WHERE
            isDelete = 0 AND id = #{id}
    </select>
<!--    新增-->
    <insert id="insert" parameterType="com.jmt.model.tg.TgUserSourceDto" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tg_user_source_order (
            orderNo,
            orderName,
            contractNo,
            tgUserNo,
            handleUser,
            sourceNum,
            auditStatus,
            reason,
            createTime,
            updateTime,
            isDelete
        )
        VALUES (
                   #{orderNo},
                   #{orderName},
                   #{contractNo},
                   #{tgUserNo},
                   #{handleUser},
                   #{sourceNum},
                   #{auditStatus},
                   #{reason},
                   Now(),  -- 创建时间使用数据库当前时间
                   Now(),  -- 更新时间使用数据库当前时间
                   0
               )
    </insert>
<!--    依据id更新-->
    <update id="update" parameterType="com.jmt.model.tg.TgUserSourceDto" >
        UPDATE tg_user_source_order SET
            <if test="orderNo != null">orderNo = #{orderNo},</if>
            <if test="orderName != null">orderName = #{orderName},</if>
            <if test="contractNo != null">contractNo = #{contractNo},</if>
            <if test="tgUserNo != null">tgUserNo = #{tgUserNo},</if>
            <if test="handleUser != null">handleUser = #{handleUser},</if>
            <if test="sourceNum != null">sourceNum = #{sourceNum},</if>
            <if test="auditStatus != null">auditStatus = #{auditStatus},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="createTime != null">createTime = #{createTime},</if>
            <if test="isDelete != null">isDelete = #{isDelete},</if>
            updateTime = NOW()
        WHERE
            isDelete = 0 AND id = #{id}

    </update>
<!--    依据id删除-->
    <delete id="delete" parameterType="java.lang.Long" >
        UPDATE tg_user_source_order SET
                                        isDelete = 1
        WHERE
            isDelete = 0 AND  id = #{id}
    </delete>
</mapper>
