<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.TgVideoOrderDao">

    <resultMap id="BaseResultMap" type="com.jmt.model.dto.TgVideoOrderDto">
        <id column="id" property="id" />
        <result column="orderNo" property="orderNo" />
        <result column="orderName" property="orderName" />
        <result column="handleUser" property="handleUser" />
        <result column="playStartTime" property="playStartTime" />
        <result column="playEndTime" property="playEndTime" />
        <result column="fileId" property="fileId" />
        <result column="playBusNum" property="playBusNum" />
        <result column="playEqNum" property="playEqNum" />
        <result column="playDays" property="playDays" />
        <result column="playStatus" property="playStatus" />
        <result column="auditStatus" property="auditStatus" />
        <result column="reason" property="reason" />
        <result column="createTime" property="createTime" />
        <result column="updateTime" property="updateTime" />
        <result column="isDelete" property="isDelete" />
    </resultMap>

    <sql id="Base_Column_List">
        id, orderNo, orderName, handleUser, playStartTime, playEndTime,
        fileId, playbusNum, playEqNum, playDays, playStatus, auditStatus,
        reason, createTime, updateTime, isDelete
    </sql>

    <!-- 分页查询 -->
    <select id="pageQuery" resultMap="BaseResultMap" parameterType="com.jmt.model.dto.TgVideoOrderDto">
        SELECT
        <include refid="Base_Column_List" />
        FROM tg_Video_order
        WHERE isDelete = 0
        <if test="orderNo != null and orderNo != ''">
            AND orderNo LIKE CONCAT('%', #{orderNo}, '%')
        </if>
        <if test="orderName != null and orderName != ''">
            AND orderName LIKE CONCAT('%', #{orderName}, '%')
        </if>
    </select>

    <!-- 新增 -->
    <insert id="add" parameterType="com.jmt.model.dto.TgVideoOrderDto" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tg_Video_order (
            orderNo, orderName, handleUser, playStartTime, playEndTime,
            fileId, playBusNum, playEqNum, playDays, playStatus,
            auditStatus, reason
        ) VALUES (
                     #{orderNo}, #{orderName}, #{handleUser}, #{playStartTime}, #{playEndTime},
                     #{fileId}, #{playBusNum}, #{playEqNum}, #{playDays}, #{playStatus},
                     #{auditStatus}, #{reason}
                 )
    </insert>

    <!-- 按ID查询 -->
    <select id="getById" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT
        <include refid="Base_Column_List" />
        FROM tg_Video_order
        WHERE id = #{id} AND isDelete = 0
    </select>

    <!-- 修改 -->
    <update id="update" parameterType="com.jmt.model.dto.TgVideoOrderDto">
        UPDATE tg_Video_order
        SET
            orderNo = #{orderNo},
            orderName = #{orderName},
            handleUser = #{handleUser},
            playStartTime = #{playStartTime},
            playEndTime = #{playEndTime},
            fileId = #{fileId},
            playBusNum = #{playBusNum},
            playEqNum = #{playEqNum},
            playDays = #{playDays},
            playStatus = #{playStatus},
            auditStatus = #{auditStatus},
            reason = #{reason},
            updateTime = NOW()
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除（逻辑删除） -->
    <update id="deleteById" parameterType="java.lang.Long">
        UPDATE tg_Video_order
        SET isDelete = 1
        WHERE id = #{id}
    </update>
</mapper>