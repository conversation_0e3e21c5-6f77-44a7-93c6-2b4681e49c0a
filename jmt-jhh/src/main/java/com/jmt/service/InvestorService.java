package com.jmt.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.jmt.client.CmShopFeignClient;
import com.jmt.client.InvestorEqFeignClient;
import com.jmt.client.UaaFeignClient;
import com.jmt.constant.JmtConstant;
import com.jmt.enums.AuditStatusEnum;
import com.jmt.exception.BusinessException;
import com.jmt.dao.InvestorDao;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.cm.vo.CmBusinessShopVO;
import com.jmt.model.eq.dto.InvestorEqPageQueryDto;
import com.jmt.model.eq.vo.EqInfoVo;
import com.jmt.model.eq.vo.InvestorEqInfoVo;
import com.jmt.model.jhh.dto.*;
import com.jmt.model.jhh.vo.*;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.uaa.UaaUser;
import com.jmt.model.uaa.UaaUserAddress;
import com.jmt.model.uaa.dto.UaaUserDTO;
import com.jmt.util.JhhUserNoUtil;
import com.jmt.util.LoginUserUtil;
import com.jmt.util.ResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class InvestorService {
    /**
     * 根据id获取投资人信息
     * @param id
     * @return
     */
    @Resource
    private InvestorDao investorDao;

    @Resource
    private OperatorService operatorService;

    @Resource
    private InvestorEqFeignClient investorEqFeignClient;

    @Resource
    private UaaFeignClient uaaFeignClient;

    @Resource
    private CmShopFeignClient cmShopFeignClient;

    /******
     * 分配设备
     * @param assignInvestorEqDto
     * @return
     */
    @Transactional
    public void assignEq(AssignInvestorEqDto assignInvestorEqDto) {
        String investorNo = assignInvestorEqDto.getInvestorNo();
        InvestorVo investorInfo = investorDao.getByInvestorNo(investorNo);
        if (investorInfo == null||investorInfo.getIsDelete() == 1){
            throw new BusinessException("设备收益人编号不存在");
        }
        if (investorInfo.getAuditStatus() != 1){
            throw new BusinessException("设备收益人不通过审核");
        }
        investorEqFeignClient.assign(assignInvestorEqDto);
    }

    public InvestorVo getInfo(Long id) {
        InvestorVo info = investorDao.getInfo(id);
        if (ObjectUtil.isNotEmpty(info)) {
            InvestorProfitVo investorProfitVo = new InvestorProfitVo();
            info.setInvestorProfit(investorProfitVo);
        }
        return info;
    }

    private CmBusinessShopVO getShopByNo(String shopNo) {
        String response = cmShopFeignClient.getShopByNo(shopNo);
        return ResponseUtil.getData(response,CmBusinessShopVO.class);
    }

    /**
     * 分页查询投资人列表
     * @param
     * @return
     */
    public PageResult<InvestorVo> getPage(PageQuery<InvestorPageQueryDto> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<InvestorVo> page = investorDao.getPage(pageQuery.getQueryData());
        if (page != null){
            return new PageResult<>(page);
        }
        return null;
    }
    /**
     * 新增投资人
     * @param investorInfoDto
     * @return
     */
    public Integer add(InvestorInfoDto investorInfoDto){
        Long uaaId = Objects.requireNonNull(LoginUserUtil.get()).getUaaId();
        String investorNo = JhhUserNoUtil.generate("JMT-B1-");
        InvestorInfo investorInfo = new InvestorInfo();
        BeanUtils.copyProperties(investorInfoDto,investorInfo);
        investorInfo.setInvestorNo(investorNo);
        OperatorVo operatorInfo = operatorService.getByUaaId(uaaId);
        if (operatorInfo != null) {
            investorInfo.setOperatorNo(operatorInfo.getOperatorNo());
        }
        investorInfo.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        return investorDao.add(investorInfo);
    }
    /**
     * 修改收益人
     * @param investorInfoDto
     * @return
     */
    public Integer update(InvestorInfoDto investorInfoDto) {
        InvestorInfo investorInfo = new InvestorInfo();
        BeanUtils.copyProperties(investorInfoDto,investorInfo);
        return investorDao.update(investorInfo);
    }
    /**
     * 删除投资人
     * @param id
     * @return
     */
    public Integer delete(Long id) {
        InvestorVo info = investorDao.getInfo(id);
        uaaFeignClient.lock(info.getUaaId());
        return investorDao.delete(id);
    }

    /**
     * 审核
     * @param investorInfoAuditDto 审核参数
     * @param loginUaaUser
     */
    @Transactional(rollbackFor = {Exception.class,BusinessException.class})
    public Integer audit(InvestorAuditDto investorInfoAuditDto, LoginUaaUser loginUaaUser) {
        Long id = investorInfoAuditDto.getId();
        Integer auditStatus = investorInfoAuditDto.getAuditStatus();
        String reason = investorInfoAuditDto.getReason();
        Long auditUser = loginUaaUser.getUaaId();
        String auditUserName = loginUaaUser.getUsername();

        InvestorVo investorVo = investorDao.getInfo(id);
        if (ObjectUtil.isEmpty(investorVo)) {
            throw new BusinessException("未找到要审核的对象信息");
        }
        if (!auditStatus.equals(AuditStatusEnum.APPROVED.getCode()) && !auditStatus.equals(AuditStatusEnum.REJECTED.getCode())) {
            throw new BusinessException("审核状态错误");
        }
        if (!AuditStatusEnum.PENDING.getCode().equals(investorVo.getAuditStatus())) {
            throw new BusinessException("不是待审核状态");
        }
        Integer r = investorDao.audit(id,auditStatus,reason,auditUser,auditUserName);
        if (r == 1 && auditStatus.equals(AuditStatusEnum.APPROVED.getCode())){
            UaaUserDTO uaaUserDTO = new UaaUserDTO();
            uaaUserDTO.setPassword(JmtConstant.DEFAULT_PASSWORD);
            uaaUserDTO.setLoginName(investorVo.getTelPhone());
            uaaUserDTO.setTelPhone(investorVo.getTelPhone());
            String response = uaaFeignClient.registerUser(uaaUserDTO);
            UaaUser uaaUser = ResponseUtil.getData(response,UaaUser.class);
            if (uaaUser != null) {
                investorDao.setInvestorUaaId(id,uaaUser.getUaaId());
            } else {
                throw new BusinessException("审核失败:无法创建账号");
            }
        }
        return r;
    }
    /**
     * 获取操作员下拉列表
     *
     * @return
     */
    public List<InvestorSelectVo> getOperatorSelect() {
        return investorDao.getInvestorSelect();
    }
    /**
     * 冻结
     * @param id 设备收益人Id
     */
    public Integer freeze(Long id) {
        InvestorVo info = investorDao.getInfo(id);
        if (ObjectUtil.isEmpty(info)) {
            throw new BusinessException("未找到设备收益人");
        }
        uaaFeignClient.lock(info.getUaaId());
        return investorDao.freeze(id,AuditStatusEnum.FREEZE.getCode());
    }

    public JhhUserInfoVo getUserInfo(Long uaaId) {
        JhhUserInfoVo jhhUserInfoVo = new JhhUserInfoVo();
        InvestorVo investorInfo = investorDao.getByUaaId(uaaId);
        BeanUtils.copyProperties(investorInfo,jhhUserInfoVo);
        jhhUserInfoVo.setUserName(investorInfo.getInvestorName());
        jhhUserInfoVo.setUserNo(investorInfo.getInvestorNo());
        String response = uaaFeignClient.getDefaultAddress(uaaId);
        UaaUserAddress defaultAddress = ResponseUtil.getData(response, UaaUserAddress.class);
        if (ObjectUtil.isNotEmpty(defaultAddress)) {
            jhhUserInfoVo.setDefaultAddress(defaultAddress);
        }
        return jhhUserInfoVo;
    }
    /**
     * 根据uaaId获取投资信息
     * @param uaaId
     * @return
     */
    public InvestorVo getInvestorInfoByUaaId(Long uaaId) {
        return investorDao.getByUaaId(uaaId);
    }

    public JhhUserInfoVo getUserInfoByInvestor(String investorNo) {
        InvestorVo byInvestorNo = investorDao.getByInvestorNo(investorNo);
        JhhUserInfoVo jhhUserInfoVo = new JhhUserInfoVo();

        BeanUtils.copyProperties(byInvestorNo,jhhUserInfoVo);
        return jhhUserInfoVo;
    }

    public Integer setupUserInfo(JhhUserInfoDto jhhUserInfoDto,Long uaaId) {
        jhhUserInfoDto.setUaaId(uaaId);
        return investorDao.setupInfo(jhhUserInfoDto);
    }

    public PageResult<InvestorEqInfoVo> getInvestorEqPage(PageQuery<String> pageQuery) {
        List<InvestorEqInfoVo> list = new ArrayList<>();
        InvestorVo info = investorDao.getByInvestorNo(pageQuery.getQueryData());
        if (ObjectUtil.isNotEmpty(info)) {
            String response = investorEqFeignClient.getInvestorEqPage(pageQuery);
            List<EqInfoVo> pageList = ResponseUtil.getDataList(response, EqInfoVo.class);
            for (EqInfoVo eqInfoVo : pageList) {
                InvestorEqInfoVo investorEqInfoVo = new InvestorEqInfoVo();
                BeanUtil.copyProperties(eqInfoVo,investorEqInfoVo);
                OperatorVo operatorVo = operatorService.getInfoByNo(eqInfoVo.getOperatorNo());
                if (operatorVo != null) {
                    investorEqInfoVo.setOperatorNo(operatorVo.getOperatorName());
                }

                CmBusinessShopVO shop = this.getShopByNo(eqInfoVo.getShopNo());
                if (shop != null) {
                    investorEqInfoVo.setShopName(shop.getShopName());
                }
                if (StrUtil.isNotEmpty(investorEqInfoVo.getInvestorNo())) {
                    investorEqInfoVo.setChecked(true);
                }
                list.add(investorEqInfoVo);
            }
        }
        return new PageResult<>(list);
    }

    public PageResult<InvestorEqInfoVo> getOptionalEqPageByInvestor(PageQuery<InvestorEqPageQueryDto> pageQuery) {
        List<InvestorEqInfoVo> list = new ArrayList<>();
        InvestorEqPageQueryDto investorEqPageQueryDto = pageQuery.getQueryData();
        InvestorVo info = investorDao.getByInvestorNo(investorEqPageQueryDto.getInvestorNo());
        if (ObjectUtil.isNotEmpty(info)) {
            String response = investorEqFeignClient.getOptionalPage(pageQuery);
            List<EqInfoVo> pageList = ResponseUtil.getDataList(response, EqInfoVo.class);
            for (EqInfoVo eqInfoVo : pageList) {
                InvestorEqInfoVo investorEqInfoVo = new InvestorEqInfoVo();
                BeanUtil.copyProperties(eqInfoVo,investorEqInfoVo);
                OperatorVo operatorVo = operatorService.getInfoByNo(eqInfoVo.getOperatorNo());
                if (operatorVo != null) {
                    investorEqInfoVo.setOperatorNo(operatorVo.getOperatorName());
                }
                CmBusinessShopVO shop = this.getShopByNo(eqInfoVo.getShopNo());
                if (shop != null) {
                    investorEqInfoVo.setShopName(shop.getShopName());
                }
                if (StrUtil.isNotEmpty(investorEqInfoVo.getInvestorNo())) {
                    investorEqInfoVo.setChecked(true);
                }
                list.add(investorEqInfoVo);
            }
        }
        return new PageResult<>(list);
    }

    public PageResult<InvestorVo> getMyPage(PageQuery<InvestorPageQueryDto> pageQuery, LoginUaaUser loginUser) {
        InvestorPageQueryDto dto = pageQuery.getQueryData();
        dto.setOperatorNo(loginUser.getOperatorNo());
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<InvestorVo> page = investorDao.getPage(pageQuery.getQueryData());
        if (page != null){
            return new PageResult<>(page);
        }
        return null;
    }
}
