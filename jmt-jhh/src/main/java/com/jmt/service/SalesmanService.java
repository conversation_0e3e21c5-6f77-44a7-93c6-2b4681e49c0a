package com.jmt.service;

import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.client.CmUserFeignClient;
import com.jmt.client.SalesmanEqFeignClient;
import com.jmt.client.UaaFeignClient;
import com.jmt.constant.JmtConstant;
import com.jmt.exception.BusinessException;
import com.jmt.dao.SalesmanDao;
import com.jmt.model.jhh.dto.*;
import com.jmt.model.jhh.vo.JhhUserInfoVo;
import com.jmt.model.jhh.vo.OperatorVo;
import com.jmt.model.jhh.vo.SalesmanSelectVo;
import com.jmt.model.jhh.vo.SalesmanVo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.uaa.UaaUser;
import com.jmt.model.uaa.UaaUserAddress;
import com.jmt.model.uaa.dto.UaaUserDTO;
import com.jmt.util.JhhUserNoUtil;
import com.jmt.util.LoginUserUtil;
import com.jmt.util.ResponseUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
public class SalesmanService extends BaseService {

    @Resource
    private SalesmanDao salesmanDao;

    @Resource
    private OperatorService operatorService;

    @Resource
    private UaaFeignClient uaaFeignClient;

    @Resource
    private SalesmanEqFeignClient salesmanEqFeignClient;

    @Resource
    private CmUserFeignClient cmUserFeignClient;

    /**
     * 获取分页数据
     *
     * @param pageQuery
     * @return
     */
    public PageResult<SalesmanVo> getPage(PageQuery<SalesmanPageQueryDto> pageQuery) {
        Long loginUser = Objects.requireNonNull(LoginUserUtil.get()).getUaaId();
        OperatorVo operatorInfo = operatorService.getByUaaId(loginUser);
        if (operatorInfo == null) {
            return null;
        }
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        SalesmanPageQueryDto dto = pageQuery.getQueryData();
        dto.setOperatorNo(operatorInfo.getOperatorNo());
        List<SalesmanVo> page = salesmanDao.getPage(pageQuery.getQueryData());
        if (page != null){
            return new PageResult<>(page);
        }
        return null;
    }
    /**
     * 运营商添加业务员
     *
     * @param salesmanInfoDto
     */
    @Transactional
    public Integer add(SalesmanInfoDto salesmanInfoDto) {
        Long uaaId = Objects.requireNonNull(LoginUserUtil.get()).getUaaId();
        OperatorVo operatorInfo = operatorService.getByUaaId(uaaId);
        if (operatorInfo == null) {
            throw new BusinessException("新增失败:运营商不存在");
        }
        String salesmanNo = JhhUserNoUtil.generate("JMT-A2-");
        SalesmanInfo salesmanInfo = new SalesmanInfo();
        BeanUtils.copyProperties(salesmanInfoDto,salesmanInfo);
        salesmanInfo.setOperatorNo(operatorInfo.getOperatorNo());
        salesmanInfo.setSalesmanNo(salesmanNo);
        UaaUserDTO uaaUserDTO = new UaaUserDTO();
        uaaUserDTO.setPassword(salesmanInfoDto.getPassword());
        uaaUserDTO.setLoginName(salesmanInfo.getTelPhone());
        uaaUserDTO.setTelPhone(salesmanInfo.getTelPhone());
        SalesmanInfo info = salesmanDao.getInfoByTelPhone(salesmanInfo.getTelPhone());
        if (ObjectUtil.isNotEmpty(info)) {
            throw new BusinessException("新增失败:注册手机号已被使用");
        }
        String response = uaaFeignClient.registerUser(uaaUserDTO);
        UaaUser uaaUser = ResponseUtil.getData(response,UaaUser.class);
        if (uaaUser != null) {
            salesmanInfo.setUaaId(uaaUser.getUaaId());
        }
        return salesmanDao.add(salesmanInfo);
    }
    /**
     * 修改业务员信息
     *
     * @param salesmanInfoDto
     */
    @Transactional
    public Integer update(SalesmanInfoDto salesmanInfoDto) {
        Long uaaId = Objects.requireNonNull(LoginUserUtil.get()).getUaaId();
        OperatorVo operatorInfo = operatorService.getByUaaId(uaaId);
        if (operatorInfo == null) {
            throw new BusinessException("修改失败:运营商不存在");
        }
        String salesmanNo = JhhUserNoUtil.generate("JMT-A2-");
        SalesmanInfo salesmanInfo = new SalesmanInfo();
        BeanUtils.copyProperties(salesmanInfoDto,salesmanInfo);
        salesmanInfo.setOperatorNo(operatorInfo.getOperatorNo());
        salesmanInfo.setSalesmanNo(salesmanNo);
        UaaUserDTO uaaUserDTO = new UaaUserDTO();
        uaaUserDTO.setPassword(salesmanInfoDto.getPassword());
        uaaUserDTO.setLoginName(salesmanInfo.getTelPhone());
        uaaUserDTO.setTelPhone(salesmanInfo.getTelPhone());
        SalesmanInfo info = salesmanDao.getInfoByTelPhone(salesmanInfo.getTelPhone());
        if (ObjectUtil.isNotEmpty(info)) {
            throw new BusinessException("修改失败:注册手机号已被使用");
        }
        String response = uaaFeignClient.registerUser(uaaUserDTO);
        UaaUser uaaUser = ResponseUtil.getData(response,UaaUser.class);
        if (uaaUser != null) {
            salesmanInfo.setUaaId(uaaUser.getUaaId());
        }
        Integer r =  salesmanDao.add(salesmanInfo);
        if (r == 1) {
            TransferSalesmanEqDto transferSalesmanEqDto = new TransferSalesmanEqDto();
            transferSalesmanEqDto.setSourceSalesmanNo(salesmanInfoDto.getSalesmanNo());
            transferSalesmanEqDto.setTargetSalesmanNo(salesmanNo);
            String eqTransferSalesman = salesmanEqFeignClient.transfer(transferSalesmanEqDto);
            Integer eqRsCode = ResponseUtil.getCode(eqTransferSalesman);
            if (!eqRsCode.equals(JmtConstant.SERVICE_RESPONSE_SUCCESS_CODE)) {
                throw new BusinessException("修改失败:设备迁移失败");
            }
            String cmTransferSalesman = cmUserFeignClient.transferSalesman(transferSalesmanEqDto);
            Integer cmRsCode = ResponseUtil.getCode(cmTransferSalesman);
            if (!cmRsCode.equals(JmtConstant.SERVICE_RESPONSE_SUCCESS_CODE)) {
                throw new BusinessException("修改失败:商家迁移失败");
            }
        }
        return r;
    }

    /**
     * 获取业务员下拉框
     *
     * @return
     */
    public List<SalesmanSelectVo> getSalesmanSelect() {
        Long uaaId = Objects.requireNonNull(LoginUserUtil.get()).getUaaId();
        OperatorVo operatorInfo = operatorService.getByUaaId(uaaId);
        return salesmanDao.getSalesmanSelect(operatorInfo.getOperatorNo());
    }
    /**
     * 根据uaaId获取业务员信息
     *
     * @param uaaId
     * @return
     */
    public SalesmanInfo getSalesmanInfoByUaaId(Long uaaId) {
        return salesmanDao.getByUaaId(uaaId);
    }
    /**
     * 根据id获取业务员信息
     *
     * @param id
     * @return
     */
    public SalesmanInfo getInfo(Long id) {
        return salesmanDao.getInfo(id);
    }


    /**
     * 根据运营商获取业务员下拉框
     * @param operatorNo
     * @return
     */
    public List<SalesmanSelectVo> getDropByOperatorNo(String operatorNo) {
        return salesmanDao.getDropByOperatorNo(operatorNo);
    }
    /**
     * 根据部署人获取业务员
     * @param salesmanNo
     * @return
     */
    public SalesmanInfo getInfoBySalesmanNo(String salesmanNo) {
        return salesmanDao.getInfoBySalesmanNo(salesmanNo);
    }

    public JhhUserInfoVo getUserInfo(Long uaaId) {
        JhhUserInfoVo jhhUserInfoVo = new JhhUserInfoVo();
        SalesmanInfo info = salesmanDao.getByUaaId(uaaId);
        BeanUtils.copyProperties(info,jhhUserInfoVo);
        jhhUserInfoVo.setUserName(info.getSalesmanName());
        jhhUserInfoVo.setUserNo(info.getSalesmanNo());
        String response = uaaFeignClient.getDefaultAddress(uaaId);
        UaaUserAddress defaultAddress = ResponseUtil.getData(response, UaaUserAddress.class);
        if (ObjectUtil.isNotEmpty(defaultAddress)) {
            jhhUserInfoVo.setDefaultAddress(defaultAddress);
        }
        return jhhUserInfoVo;
    }

    public Integer setupUserInfo(JhhUserInfoDto jhhUserInfoDto, Long uaaId) {
        jhhUserInfoDto.setUaaId(uaaId);
        return salesmanDao.setupInfo(jhhUserInfoDto);
    }
}
