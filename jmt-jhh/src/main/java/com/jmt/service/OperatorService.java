package com.jmt.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.client.*;
import com.jmt.constant.JmtConstant;
import com.jmt.dao.OperatorDao;
import com.jmt.dao.SalesmanDao;
import com.jmt.enums.AuditStatusEnum;
import com.jmt.exception.BusinessException;
import com.jmt.model.jhh.dto.*;
import com.jmt.model.jhh.vo.JhhUserInfoVo;
import com.jmt.model.jhh.vo.OperatorProfitVo;
import com.jmt.model.jhh.vo.OperatorSelectVo;
import com.jmt.model.jhh.vo.OperatorVo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.profit.vo.ShareProjectVo;
import com.jmt.model.uaa.UaaUser;
import com.jmt.model.uaa.UaaUserAddress;
import com.jmt.model.uaa.UaaUserBankcard;
import com.jmt.model.uaa.dto.UaaUserDTO;
import com.jmt.util.JhhUserNoUtil;
import com.jmt.util.ResponseUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.*;

@Service
public class OperatorService extends BaseService {

    @Resource
    private OperatorDao operatorDao;

    @Resource
    private SalesmanDao salesmanDao;

    @Resource
    private  ShareConfigService shareConfigService;

    @Resource
    private OperatorEqFeignClient operatorEqFeignClient;

    @Resource
    private UaaFeignClient uaaFeignClient;

    @Resource
    private ProfitFeignClient profitFeignClient;

    /**
     * 添加运营商
     * @param operatorInfoDto 新增表单参数
     */
    @Transactional
    public String add(OperatorInfoDto operatorInfoDto) {
        String operatorNo = JhhUserNoUtil.generate("JMT-A1-");
        OperatorInfo operatorInfo = new OperatorInfo();
        BeanUtils.copyProperties(operatorInfoDto,operatorInfo);
        operatorInfo.setAuditStatus(AuditStatusEnum.APPROVED.getCode());
        operatorInfo.setOperatorNo(operatorNo);
        OperatorVo info = operatorDao.getByTelPhone(operatorInfo.getTelPhone());
        if (ObjectUtil.isNotEmpty(info)) {
            return super.responseFail("新增失败:注册手机号已被使用");
        }
        UaaUserDTO uaaUserDTO = new UaaUserDTO();
        uaaUserDTO.setPassword(JmtConstant.DEFAULT_PASSWORD);
        uaaUserDTO.setLoginName(operatorInfo.getTelPhone());
        uaaUserDTO.setTelPhone(operatorInfo.getTelPhone());
        String response = uaaFeignClient.registerUser(uaaUserDTO);
        UaaUser uaaUser = ResponseUtil.getData(response, UaaUser.class);
        if (ObjectUtil.isNotEmpty(uaaUser)) {
            assert uaaUser != null;
            operatorInfo.setUaaId(uaaUser.getUaaId());
        }
        Integer r = operatorDao.add(operatorInfo);
        List<ShareConfig> shareConfigs = operatorInfoDto.getShareConfigs();
        if (shareConfigs != null && shareConfigs.size() > 0){
            for (ShareConfig shareConfig : shareConfigs){
                shareConfig.setOperatorNo(operatorNo);
                shareConfigService.add(shareConfig);
            }
        }
        if (r == 1) {
            return super.responseSuccess("新增成功");
        }
        return super.responseFail("新增失败");
    }

    /**
     * 获取运营商分页列表
     * @param pageQuery 分页查询参数
     * @return PageResult
     */
    public PageResult<OperatorVo> getPage(PageQuery<OperatorInfoQueryDto> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<OperatorVo> page = operatorDao.getPage(pageQuery.getQueryData());
        if (page != null){
            return new PageResult<>(page);
        }
        return null;
    }
    /**
     * 获取运营商信息
     * @param id 运营商id
     * @return OperatorVo
     */
    public OperatorVo getInfo(Long id) {
        OperatorVo info = operatorDao.getInfo(id);
        if (ObjectUtil.isNotEmpty(info)) {
            List<ShareConfig> configs = shareConfigService.getShareConfig(info.getOperatorNo());
            info.setShareConfigs(configs);
        }
        return info;
    }
    /**
     * 修改运营商信息
     * @param operatorInfoDto 修改参数
     */
    @Transactional
    public Integer update(OperatorInfoDto operatorInfoDto) {
        OperatorVo info = operatorDao.getInfo(operatorInfoDto.getId());
        if (ObjectUtil.isEmpty(info)) {
            return 0;
        }
        String operatorInfoNo = info.getOperatorNo();
        OperatorInfo operatorInfo = new OperatorInfo();
        BeanUtils.copyProperties(operatorInfoDto, operatorInfo);
        operatorInfo.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        Integer r = operatorDao.update(operatorInfo);
        shareConfigService.deleteByOperatorNo(operatorInfoNo);
        List<ShareConfig> shareConfigs = operatorInfoDto.getShareConfigs();
        if (shareConfigs != null && shareConfigs.size() > 0){
            for (ShareConfig shareConfig : shareConfigs){
                shareConfig.setOperatorNo(operatorInfoNo);
                shareConfigService.add(shareConfig);
            }
        }
        return r;
    }
    /**
     * 删除运营商信息
     * @param id 运营商ID
     */
    public Integer delete(Long id) {
        OperatorVo info = operatorDao.getInfo(id);
        if (ObjectUtil.isNotEmpty(info)) {
            shareConfigService.deleteByOperatorNo(info.getOperatorNo());
            uaaFeignClient.lock(info.getUaaId());
        }
        return operatorDao.delete(id);
    }

    /**
     * 获取运营商下拉列表
     * @return OperatorSelectVo
     * @param name
     */
    public List<OperatorSelectVo> getOperatorSelect(String name) {
        return operatorDao.getOperatorSelect(name);
    }

    /**
     * 运营商冻结接口
     * @param id 运营商ID
     */
    @Transactional
    public Integer freeze(Long id) {
        OperatorVo operatorVo = operatorDao.getInfo(id);
        if (operatorVo == null || operatorVo.getOperatorNo() == null) {
            throw new BusinessException("未找到运营商");
        }
        uaaFeignClient.lock(operatorVo.getUaaId());
        return operatorDao.freeze(id,AuditStatusEnum.FREEZE.getCode());
    }
    /**
     * 运营商迁移接口
     * @param transferOperatorEqDto 迁移参数
     */
    @Transactional
    public void transfer(TransferOperatorEqDto transferOperatorEqDto) {
        String sourceOperatorNo = transferOperatorEqDto.getSourceOperatorNo();
        String targetOperatorNo = transferOperatorEqDto.getTargetOperatorNo();
        //校验编号是否有效
        OperatorVo sourceOperatorInfo = operatorDao.getByOperatorNo(sourceOperatorNo);
        OperatorVo targetOperatorInfo = operatorDao.getByOperatorNo(targetOperatorNo);
        if (sourceOperatorInfo == null || sourceOperatorInfo.getIsDelete() == 1){
            throw new BusinessException("原运营商编号无效");
        }
        if (targetOperatorInfo == null || targetOperatorInfo.getIsDelete() == 1){
            throw new BusinessException("目标运营商编号无效");
        }
/*        if (targetOperatorInfo.getAuditStatus()!=1){
            throw new BusinessException("目标运营商未审核通过");
        }
        if (sourceOperatorInfo.getAuditStatus()!=1){
            throw new BusinessException("原运营商未审核通过");
        }*/
        //禁用运营商
        operatorDao.freeze(sourceOperatorInfo.getId(),AuditStatusEnum.FREEZE.getCode());
        uaaFeignClient.lock(sourceOperatorInfo.getUaaId());
        // 迁移设备（更新设备的运营商编号和业务员编号）
        operatorEqFeignClient.transfer(transferOperatorEqDto);
        // 迁移业务员（更新业务员所属运营商编号）
        salesmanDao.transfer(sourceOperatorNo,targetOperatorNo);
        //启用运营商
        operatorDao.freeze(sourceOperatorInfo.getId(),AuditStatusEnum.COMPLETED.getCode());
        uaaFeignClient.unlock(sourceOperatorInfo.getUaaId());
    }

    public OperatorVo getByUaaId(Long uaaId) {
        return operatorDao.getByUaaId(uaaId);
    }

    public JhhUserInfoVo getUserInfo(Long uaaId) {
        JhhUserInfoVo jhhUserInfoVo = new JhhUserInfoVo();
        OperatorVo info = operatorDao.getByUaaId(uaaId);
        BeanUtils.copyProperties(info,jhhUserInfoVo);
        jhhUserInfoVo.setUserName(info.getOperatorName());
        jhhUserInfoVo.setUserNo(info.getOperatorNo());
        String addressResponse = uaaFeignClient.getDefaultAddress(uaaId);
        UaaUserAddress defaultAddress = ResponseUtil.getData(addressResponse, UaaUserAddress.class);
        if (ObjectUtil.isNotEmpty(defaultAddress)) {
            jhhUserInfoVo.setDefaultAddress(defaultAddress);
        }

        String bankcardResponse = uaaFeignClient.getDefaultBankcard(uaaId);
        UaaUserBankcard defaultBankcard = ResponseUtil.getData(bankcardResponse, UaaUserBankcard.class);
        if (ObjectUtil.isNotEmpty(defaultBankcard)) {
            jhhUserInfoVo.setDefaultBankcard(defaultBankcard);
        }
        return jhhUserInfoVo;
    }

    public Integer setupUserInfo(JhhUserInfoDto jhhUserInfoDto, Long uaaId) {
        jhhUserInfoDto.setUaaId(uaaId);
        return operatorDao.setupInfo(jhhUserInfoDto);
    }

    public OperatorProfitVo profit(Long id, Integer timeScope) {
        OperatorProfitVo operatorProfitVo = new OperatorProfitVo();
        List<ShareProjectProfitVo> profitVos = new ArrayList<>();
        String response = profitFeignClient.getShareProjectSelect();
        List<ShareProjectVo> dataList = ResponseUtil.getDataList(response, ShareProjectVo.class);
        if (dataList.size() > 0) {
            for (ShareProjectVo shareProjectVo : dataList) {
                ShareProjectProfitVo vo = new ShareProjectProfitVo();
                BeanUtil.copyProperties(shareProjectVo,vo);
                vo.setProjectId(shareProjectVo.getId());
                profitVos.add(vo);
            }
        }
        operatorProfitVo.setId(id);
        operatorProfitVo.setTotalProfit(0);
        operatorProfitVo.setCurrMonthProfit(0);
        operatorProfitVo.setYesterdayProfit(0);
        operatorProfitVo.setProjectProfit(profitVos);
        return operatorProfitVo;
    }

    public OperatorVo getInfoByNo(String operatorNo) {
        OperatorVo operatorVo = operatorDao.getByOperatorNo(operatorNo);
        if (ObjectUtil.isEmpty(operatorVo)) {
            return new OperatorVo();
        }
        return operatorVo;
    }
}
