package com.jmt.service;

import com.jmt.base.BaseService;
import com.jmt.model.jhh.dto.ProxyOperatorDto;
import com.jmt.model.jhh.dto.ProxyOperatorVo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import org.springframework.stereotype.Service;

@Service
public class ProxyOperatorService extends BaseService {

    public PageResult<ProxyOperatorVo> getPage(PageQuery<ProxyOperatorDto> pageQuery) {
        return null;
    }

    public Integer add(ProxyOperatorDto dto) {
        return 1;
    }
}
