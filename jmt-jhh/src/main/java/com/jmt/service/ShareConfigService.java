package com.jmt.service;

import com.jmt.base.BaseService;
import com.jmt.dao.ShareConfigDao;
import com.jmt.model.jhh.dto.ShareConfig;
import com.jmt.model.jhh.vo.ShareProjectProfitShopVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class ShareConfigService extends BaseService {

    @Resource
    private ShareConfigDao shareConfigDao;

    /**
     * 添加
     * @param shareConfig
     *
     */
    public void add(ShareConfig shareConfig) {
        shareConfigDao.add(shareConfig);
    }
    /**
     * 删除
     * @param operatorNo
     */
    public void deleteByOperatorNo(String operatorNo) {
        shareConfigDao.deleteByOperatorNo(operatorNo);
    }

    public List<ShareConfig> getShareConfig(String operatorNo) {
        return shareConfigDao.getShareConfig(operatorNo);
    }

    public List<ShareProjectProfitShopVo> getShopRatioByOPN(String operatorNo) {
        return shareConfigDao.getShopRatioByOPN(operatorNo);
    }


}
