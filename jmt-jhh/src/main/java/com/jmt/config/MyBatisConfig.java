package com.jmt.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.github.pagehelper.PageInterceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.annotation.PostConstruct;
import java.sql.Connection;
import java.sql.DriverManager;
import java.util.Properties;

@Configuration
@MapperScan(value = "com.jmt.dao")
public class MyBatisConfig {

	@Value("${spring.datasource.type}")
	private String type;

	@Value("${spring.datasource.driver-class-name}")
	private String driverClassName;

	@Value("${spring.datasource.url}")
	private String url;

	@Value("${spring.datasource.username}")
	private String username;

	@Value("${spring.datasource.password}")
	private String password;


	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	/*
	 * 解决druid 日志报错：discard long time none received connection:xxx
	 * */
	@PostConstruct
	public void setProperties(){
		System.setProperty("druid.mysql.usePingMethod","false");
	}

	// 注册dataSource
	@Bean
	public DruidDataSource dataSource() {
		/**
		 * 以下是解决设备开机数据库启动比java容器慢导致容器启动失败的问题
		 * 方案：先尝试获取数据库连接，如果成功继续启动容器；如果失败等待30秒后再尝试，尝试3次后还是无法获取数据库连接
		 * 说明数据库出问题了
		 */
		Connection connection;
		int tryCount = 1;
		boolean fail = false;
		try {
			connection = DriverManager.getConnection(url, username, password);
			connection.close();
			logger.info("获取数据库连接成功");
		} catch (Exception e) {
			logger.error("获取数据库连接失败", e);
			fail = true;
		}
		while (fail && tryCount < 4) {
			try {
				logger.info("获取数据库连接失败,等待" + 30000 + "ms后重新尝试(第" + tryCount + "次)");
				Thread.sleep(30000);
				try {
					connection = DriverManager.getConnection(url, username, password);
					connection.close();
					logger.info("获取数据库连接成功");
					fail = false;
				} catch (Exception e) {
					logger.error("获取数据库连接失败", e);
					fail = true;
				}

			} catch (Exception ex) {
				logger.error("主线程休眠时发生异常", ex);
				fail = true;
			}
			tryCount++;
		}
		DruidDataSource druidDataSource = new DruidDataSource();
		druidDataSource.setDbType(type);
		druidDataSource.setDriverClassName(driverClassName);
		druidDataSource.setUrl(url);
		druidDataSource.setUsername(username);
		druidDataSource.setPassword(password);
		druidDataSource.setInitialSize(5);
		druidDataSource.setMinIdle(5);
		druidDataSource.setMaxActive(200);
		druidDataSource.setMaxWait(30000);
		druidDataSource.setTimeBetweenConnectErrorMillis(30000);
		druidDataSource.setTimeBetweenEvictionRunsMillis(30000);
		druidDataSource.setMinEvictableIdleTimeMillis(30000);
		druidDataSource.setValidationQuery("SELECT 1 FROM DUAL");
		druidDataSource.setTestWhileIdle(true);
		druidDataSource.setTestOnBorrow(true);
		druidDataSource.setTestOnReturn(false);
		druidDataSource.setPoolPreparedStatements(true);
		druidDataSource.setMaxPoolPreparedStatementPerConnectionSize(20);
		return druidDataSource;
	}

	@Bean(name = "sqlSessionFactory")
	public SqlSessionFactory sqlSessionFactory() throws Exception {
		SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
		bean.setDataSource(dataSource());
		// mybatis分页
		Properties props = new Properties();
		props.setProperty("helperDialect", "mysql");
		props.setProperty("defaultCount", "true");
		props.setProperty("reasonable", "false");
		props.setProperty("supportMethodsArguments", "false");
		// 添加插件
		PageInterceptor pageInterceptor = new PageInterceptor();
		pageInterceptor.setProperties(props);
		bean.setPlugins(pageInterceptor);
		PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
		bean.setMapperLocations(resolver.getResources("classpath:/mapper/**/*.xml"));
		org.apache.ibatis.session.Configuration config = new org.apache.ibatis.session.Configuration();
		config.setCallSettersOnNulls(true);
		bean.setConfiguration(config);
		return bean.getObject();
	}
	@Bean
	public PlatformTransactionManager transactionManager() {
		return new DataSourceTransactionManager(dataSource());
	}

}
