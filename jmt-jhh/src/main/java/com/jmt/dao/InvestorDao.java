package com.jmt.dao;

import com.jmt.model.jhh.dto.InvestorInfo;
import com.jmt.model.jhh.dto.InvestorPageQueryDto;
import com.jmt.model.jhh.dto.JhhUserInfoDto;
import com.jmt.model.jhh.vo.InvestorSelectVo;
import com.jmt.model.jhh.vo.InvestorVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface InvestorDao {
    /**
     * 根据id获取投资人信息
     * @param id
     * @return
     */

    InvestorVo getInfo(@Param("id") Long id);
    /**
     * 分页查询投资人列表
     * @param queryData
     * @return
     */
    List<InvestorVo> getPage(InvestorPageQueryDto queryData);
    /**
     * 新增投资人信息
     * @param investorInfo
     */
    Integer add(InvestorInfo investorInfo);

    /**
     * 修改投资人信息
     * @param investorInfo
     */
    Integer update(InvestorInfo investorInfo);
    /**
     * 删除投资人信息
     * @param id
     */
    Integer delete(@Param("id") Long id);
    /**
     * 根据投资人编号查询
     * @param investorNo
     * @return
     */
    InvestorVo getByInvestorNo(String investorNo);
    /**
     * 获取下拉框数据
     *
     * @return
     */
    List<InvestorSelectVo> getInvestorSelect();
    /**
     * 根据uaaId查询
     * @param uaaId
     * @return
     */
    InvestorVo getByUaaId(@Param("uaaId") Long uaaId);
    /**
     * 根据操作员编号查询数量
     * @param operatorNo
     * @return
     */
    Integer getCountByOperatorNo(@Param("operatorNo") String operatorNo);

    Integer setupInfo(JhhUserInfoDto jhhUserInfoDto);

    Integer freeze(@Param("id") Long id, @Param("auditStatus") Integer auditStatus);

    Integer audit(@Param("id") Long id, @Param("auditStatus") Integer auditStatus, @Param("reason") String reason, @Param("auditUser")Long auditUser, @Param("auditUserName")String auditUserName);

    void setInvestorUaaId(@Param("id")Long id, @Param("uaaId")Long uaaId);
}
