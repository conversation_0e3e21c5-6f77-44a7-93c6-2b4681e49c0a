package com.jmt.dao;

import com.jmt.model.jhh.dto.ShareConfig;
import com.jmt.model.jhh.vo.ShareProjectProfitShopVo;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ShareConfigDao {
    /**
     * 新增配置
     * @param shareConfig
     */
    void add(ShareConfig shareConfig);
    /**
     * 删除配置
     * @param operatorNo
     */
    void deleteByOperatorNo(String operatorNo);

    List<ShareConfig> getShareConfig(String operatorNo);

    List<ShareProjectProfitShopVo> getShopRatioByOPN(String operatorNo);
}
