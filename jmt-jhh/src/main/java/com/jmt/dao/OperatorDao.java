package com.jmt.dao;

import com.jmt.model.jhh.dto.JhhUserInfoDto;
import com.jmt.model.jhh.dto.OperatorInfo;
import com.jmt.model.jhh.dto.OperatorInfoDto;
import com.jmt.model.jhh.dto.OperatorInfoQueryDto;
import com.jmt.model.jhh.vo.OperatorSelectVo;
import com.jmt.model.jhh.vo.OperatorVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OperatorDao {
    /**
     * 获取运营商信息
     * @param id
     * @return
     */
    OperatorVo getInfo(Long id);
    /**
     * 获取运营商分页列表
     * @param queryData
     * @return
     */
    List<OperatorVo> getPage(OperatorInfoQueryDto queryData);
    /**
     * 添加运营商信息
     * @param operatorInfo
     */
    Integer add(OperatorInfo operatorInfo);
    /**
     * 修改运营商信息
     * @param operatorInfo
     */
    Integer update(OperatorInfo operatorInfo);
    /**
     * 删除运营商信息
     * @param id
     */
    Integer delete(Long id);
    /**
     * 获取运营商下拉列表
     * @param
     * @return
     */
    List<OperatorSelectVo> getOperatorSelect(String kn);
    /**
     * 根据运营商编号查找运营商信息
     * @param
     * @return
     */
    OperatorVo getByOperatorNo(String sourceOperatorNo);
    /**
     * 根据uaaId查找运营商信息
     * @param
     * @return
     */
    OperatorVo getByUaaId(Long uaaId);

    Integer freeze(@Param("id") Long id, @Param("auditStatus") Integer auditStatus);

    Integer setupInfo(JhhUserInfoDto jhhUserInfoDto);

    OperatorVo getByTelPhone(String telPhone);
}
