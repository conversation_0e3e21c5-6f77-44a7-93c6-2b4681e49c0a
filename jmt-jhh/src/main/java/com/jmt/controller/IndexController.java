package com.jmt.controller;

import com.jmt.base.BaseController;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;


@RestController
@RequestMapping("/jhh/index/stat/v1")
public class IndexController extends BaseController{

    /**
     * 聚合伙首页收益统计
     */
    @ResponseBody
    @GetMapping(value = "/profits")
    public String getProfitCount() {
        Map<String,Object> map = new HashMap<>();
        map.put("yesterdayProfit",20000);
        map.put("totalProfit",20000);
        map.put("balance",200.00);
        return super.responseSuccess(map,"查询成功");
    }
    /**
     * 聚合伙首页用户统计
     */
    @ResponseBody
    @GetMapping (value = "/users")
    public String getUserCount() {
        Map<String,Object> map = new HashMap<>();
        map.put("userTotal",1000);
        map.put("busNum",928);
        map.put("touristNum",72);
        return super.responseSuccess(map,"查询成功");
    }
    /**
     * 聚合伙首页设备统计
     */
    @ResponseBody
    @GetMapping(value = "/eqs")
    public String getEqCount() {
        Map<String,Object> map = new HashMap<>();
        map.put("orderEqNum",1000);
        map.put("deployEqNum",928);
        map.put("onlineEqNum",72);
        map.put("offlineEqNum",0);
        return super.responseSuccess(map,"查询成功");
    }

    /**
     * 聚合伙首页工单统计
     */
    @ResponseBody
    @GetMapping(value = "/works")
    public String update() {
        Map<String,Object> map = new HashMap<>();
        map.put("recallWorkNum",10);
        map.put("deployWorkNum",928);
        map.put("serviceWorkNum",72);
        map.put("otherWorkNum",0);
        return super.responseSuccess(map,"查询成功");

    }
    /**
     * 聚合伙首页待办统计
     */
    @ResponseBody
    @GetMapping(value = "/todo")
    public String getTodoCount() {
        Map<String,Object> map = new HashMap<>();
        map.put("busApplyNum",10);
        map.put("deployApplyNum",928);
        map.put("applyNum",72);
        map.put("productAuditNum",0);
        return super.responseSuccess(map,"查询成功");
    }
}
