package com.jmt.controller;

import cn.hutool.core.util.ObjectUtil;
import com.jmt.base.BaseController;
import com.jmt.enums.UserTypeEnum;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.jhh.dto.JhhUserInfoDto;
import com.jmt.model.jhh.dto.SalesmanInfo;
import com.jmt.model.jhh.vo.InvestorVo;
import com.jmt.model.jhh.vo.JhhUserInfoVo;
import com.jmt.model.jhh.vo.JhhUserNoVo;
import com.jmt.model.jhh.vo.OperatorVo;
import com.jmt.service.InvestorService;
import com.jmt.service.OperatorService;
import com.jmt.service.SalesmanService;
import com.jmt.util.LoginUserUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


@RestController
@RequestMapping("/jhh/user/v1")
public class JhhUserController extends BaseController{

    @Resource
    private OperatorService operatorService;

    @Resource
    private InvestorService investorService;

    @Resource
    private SalesmanService salesmanService;

    @ResponseBody
    @GetMapping("/getJhhUserNo")
    public String getJhhUserNo(@RequestParam Long uaaId) {
        JhhUserNoVo jhhUserNoVo = new JhhUserNoVo();
        OperatorVo operator = operatorService.getByUaaId(uaaId);
        if (operator != null) {
            jhhUserNoVo.setOperatorNo(operator.getOperatorNo());
        }
        InvestorVo investor = investorService.getInvestorInfoByUaaId(uaaId);
        if (investor != null) {
            jhhUserNoVo.setInvestorNo(investor.getInvestorNo());
        }
        SalesmanInfo salesman = salesmanService.getSalesmanInfoByUaaId(uaaId);
        if (salesman != null) {
            jhhUserNoVo.setSalesmanNo(salesman.getSalesmanNo());
        }
        return super.responseSuccess(jhhUserNoVo,"查询成功");
    }
    /**
     * 获取聚合伙当前登录用户信息
     */
    @ResponseBody
    @GetMapping(value = "/userInfo")
    public String userInfo() {
        JhhUserInfoVo jhhUserInfoVo;
        LoginUaaUser loginUaaUser = LoginUserUtil.get();
        assert loginUaaUser != null;
        Integer userType = loginUaaUser.getUserType();

        if (UserTypeEnum.INVESTOR.getCode().equals(userType)) {
            jhhUserInfoVo = investorService.getUserInfo(loginUaaUser.getUaaId());
        } else if (UserTypeEnum.SALESMAN.getCode().equals(userType)) {
            jhhUserInfoVo = salesmanService.getUserInfo(loginUaaUser.getUaaId());
        } else {
            jhhUserInfoVo = operatorService.getUserInfo(loginUaaUser.getUaaId());
        }
        if (ObjectUtil.isEmpty(jhhUserInfoVo)) {
            return super.responseFail("查询失败");
        }
        jhhUserInfoVo.setUserType(userType);
        return super.responseSuccess(jhhUserInfoVo,"查询成功");
    }

    /**
     * 设置聚合伙用户信息
     * @param jhhUserInfoDto 设置
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/setup")
    public String setup(@RequestBody JhhUserInfoDto jhhUserInfoDto) {
        logger.info("设置聚合伙用户信息",jhhUserInfoDto);
        Integer r;
        LoginUaaUser loginUaaUser = LoginUserUtil.get();
        Integer userType = loginUaaUser.getUserType();
        if (UserTypeEnum.INVESTOR.getCode().equals(userType)) {
            r = investorService.setupUserInfo(jhhUserInfoDto,loginUaaUser.getUaaId());
        } else if (UserTypeEnum.SALESMAN.getCode().equals(userType)) {
            r = salesmanService.setupUserInfo(jhhUserInfoDto,loginUaaUser.getUaaId());
        } else {
            r = operatorService.setupUserInfo(jhhUserInfoDto,loginUaaUser.getUaaId());
        }
        if (r == 1) {
            return super.responseSuccess("设置成功");
        } else {
            return super.responseFail("设置失败");
        }
    }
}
