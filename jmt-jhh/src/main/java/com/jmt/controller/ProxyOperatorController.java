package com.jmt.controller;
import com.jmt.base.BaseController;
import com.jmt.model.jhh.dto.*;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.service.ProxyOperatorService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
/**
 * 运营商账号托管
 */
@RestController
@RequestMapping("/jhh/operator/proxy/v1")
public class ProxyOperatorController extends BaseController{

    @Resource
    private ProxyOperatorService proxyOperatorService;

    /**
     * 分页查询
     * @param pageQuery 查询参数
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/getPage")
    public String getPage(@RequestBody(required = false) PageQuery<ProxyOperatorDto> pageQuery) {
        PageResult<ProxyOperatorVo> page = proxyOperatorService.getPage(pageQuery);
        return super.responseSuccess(page,"查询成功");
    }
    /**
     * 新增
     * @param dto 新增参数
     * @return ResBody
     */
    @ResponseBody
    @PostMapping(value = "/add")
    public String add(@RequestBody ProxyOperatorDto dto) {
        Integer r = proxyOperatorService.add(dto);
        if (r==1) {
            return super.responseSuccess("新增成功");
        } else {
            return super.responseFail("新增失败");
        }

    }

}
