package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.jhh.dto.SalesmanInfo;
import com.jmt.model.jhh.dto.SalesmanInfoDto;
import com.jmt.model.jhh.dto.SalesmanPageQueryDto;
import com.jmt.model.jhh.vo.SalesmanSelectVo;
import com.jmt.model.jhh.vo.SalesmanVo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.service.SalesmanService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/jhh/salesman/v1")
public class SalesmanController extends BaseController{

    @Resource
    private SalesmanService salesmanService;

    /**
     * 分页查询业务员信息
     * @param pageQuery
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/getPage")
    public String getPage(@RequestBody(required = false) PageQuery<SalesmanPageQueryDto> pageQuery){
        logger.info("分页查询参数:{}",pageQuery);
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
        }
        PageResult<SalesmanVo> page = salesmanService.getPage(pageQuery);
        return super.responseSuccess(page,"查询成功");
    }

    /**
     * 分页查询业务员信息
     * @param id 业务员ID
     * @return
     */
    @ResponseBody
    @GetMapping(value = "/getInfo")
    public String getInfo(@RequestParam Long id){
        SalesmanInfo info = salesmanService.getInfo(id);
        if (info == null) {
            return super.responseFail("业务员不存在");
        }
        return super.responseSuccess(info,"查询成功");
    }
    /**
     * 新增业务员
     * @param  salesmanInfoDto 新增参数
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/add")
    public String add(@RequestBody SalesmanInfoDto salesmanInfoDto) {
        logger.info("新增员工:{}",salesmanInfoDto);
        Integer r = salesmanService.add(salesmanInfoDto);
        if (r == 1) {
            return super.responseSuccess("新增成功");
        } else {
            return super.responseFail("新增失败");
        }

    }

    /**
     * 修改业务员信息
     * @param salesmanInfoDto 修改参数
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/update")
    public String update(@RequestBody SalesmanInfoDto salesmanInfoDto) {
        logger.info("修改参数:{}",salesmanInfoDto);
        Integer r = salesmanService.update(salesmanInfoDto);
        if (r == 1) {
            return super.responseSuccess("修改成功");
        } else {
            return super.responseFail("修改失败");
        }

    }

    /**
     * 获取业务员下拉框数据
     * @return String
     */
    @ResponseBody
    @GetMapping(value = "/select")
    public String select() {
        List<SalesmanSelectVo> salesmanList = salesmanService.getSalesmanSelect();
        return super.responseSuccess(salesmanList,"查询成功");
    }
    /**
     * 按uaaid查询业务员信息
     * @param
     * @return String
     */
    @ResponseBody
    @GetMapping(value = "/getSalesmanInfoByUaaId")
    public String getSalesmanInfoByUaaId(@RequestParam Long uaaId) {
        SalesmanInfo salesmanInfo = salesmanService.getSalesmanInfoByUaaId(uaaId);
        return super.responseSuccess(salesmanInfo,"查询成功");
    }
    /**
     * 按id查询业务员信息
     * @param
     * @return String
     */
    @ResponseBody
    @GetMapping(value = "/getSalesmanInfoById")
    public String getSalesmanInfoById(@RequestParam Long id) {
        SalesmanInfo salesmanInfo = salesmanService.getInfo(id);
        return super.responseSuccess(salesmanInfo,"查询成功");
    }

    /**
     * 按运营商编号查询业务员信息
     * @param operatorNo
     * @return
     */
    @GetMapping(value = "/getDropByOperatorNo")
    public String getDropByOperatorNo(@RequestParam String operatorNo) {
        List<SalesmanSelectVo> salesmanList = salesmanService.getDropByOperatorNo(operatorNo);
        return super.responseSuccess(salesmanList,"查询成功");
    }
    /**
     * 按业务员编号查询业务员信息
     * @param salesmanNo
     * @return
     */
    @GetMapping(value = "/getInfoBySalesmanNo")
    public String getInfoBySalesmanNo(@RequestParam String salesmanNo) {
        SalesmanInfo salesmanInfo = salesmanService.getInfoBySalesmanNo(salesmanNo);
        return super.responseSuccess(salesmanInfo,"查询成功");
    }
}
