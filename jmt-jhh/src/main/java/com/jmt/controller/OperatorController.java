package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.client.CmBusinessInfoFeignClient;
import com.jmt.client.CmUserFeignClient;
import com.jmt.model.cm.entity.CmBusinessMange;
import com.jmt.model.common.ObjIdDto;
import com.jmt.model.jhh.dto.*;
import com.jmt.model.jhh.vo.OperatorProfitVo;
import com.jmt.model.jhh.vo.OperatorSelectVo;
import com.jmt.model.jhh.vo.OperatorVo;
import com.jmt.model.jhh.vo.ShareProjectProfitShopVo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.service.OperatorService;
import com.jmt.service.ShareConfigService;
import com.jmt.util.LoginUserUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


@RestController
@RequestMapping("/jhh/operator/v1")
public class OperatorController extends BaseController{

    @Resource
    private OperatorService operatorService;

    @Resource
    private CmUserFeignClient cmUserFeignClient;

    @Resource
    private CmBusinessInfoFeignClient cmBusinessInfoFeignClient;

    @Resource
    private ShareConfigService shareConfigService;

    /**
     * 根据id查看运营商信息
     * @param id 主键ID
     * @return String
     *
     */
    @ResponseBody
    @GetMapping(value = "/getInfo")
    public String getInfo(@RequestParam Long id) {
        OperatorVo operatorInfo= operatorService.getInfo(id);
        return super.responseSuccess(operatorInfo,"查询成功");
    }
    /**
     * 分页查询运营商信息
     * @param pageQuery 查询参数
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/getPage")
    public String getPage(@RequestBody(required = false) PageQuery<OperatorInfoQueryDto> pageQuery) {
        logger.info("分页查询参数:{}",pageQuery);
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
        }
        PageResult<OperatorVo> page = operatorService.getPage(pageQuery);
        return super.responseSuccess(page,"查询成功");
    }
    /**
     * 新增运营商
     * @param operatorInfoDto 新增参数
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/add")
    public String add(@RequestBody OperatorInfoDto operatorInfoDto) {
        logger.info("新增运营商:{}",operatorInfoDto);
        return operatorService.add(operatorInfoDto);
    }

    /**
     * 修改运营商
     * @param operatorInfoDto 修改参数
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/update")
    public String update(@RequestBody OperatorInfoDto operatorInfoDto) {
        logger.info("修改参数:{}",operatorInfoDto);
        Integer r = operatorService.update(operatorInfoDto);
        if (r == 1) {
            return super.responseSuccess("修改成功");
        } else {
            return super.responseFail("修改失败");
        }
    }
    /**
     * 删除运营商
     * @param id 主键ID
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/delete")
    public String delete(@RequestParam Long id) {
        logger.info("删除参数:{}",id);
        Integer r = operatorService.delete(id);
        if (r == 1) {
            return super.responseSuccess("删除成功");
        } else {
            return super.responseFail("删除失败");
        }
    }

    /**
     * 运营商下拉框数据接口
     */
    @ResponseBody
    @GetMapping(value = "/select")
    public String getOperatorSelect(@RequestParam String name) {
        List<OperatorSelectVo> list = operatorService.getOperatorSelect(name);
        return super.responseSuccess(list,"查询成功");
    }
    /**
     * 运营商冻结接口
     */
    @ResponseBody
    @PostMapping(value = "/freeze")
    public String freeze(@RequestBody ObjIdDto dto) {
        Integer r = operatorService.freeze(dto.getId());
        if (r == 1) {
            return super.responseSuccess("冻结成功");
        } else {
            return super.responseFail("冻结失败");
        }
    }
    /**
     * 运营商收益接口
     */
    @ResponseBody
    @PostMapping(value = "/profit")
    public String profit(@RequestBody OperatorProfitDto dto) {
        OperatorProfitVo operatorProfitVo = operatorService.profit(dto.getId(),dto.getTimeScope());
        return super.responseSuccess(operatorProfitVo,"查询");
    }
    /**
     * 运营商迁移接口
     */
    @ResponseBody
    @PostMapping(value = "/transfer")
    public String transfer(@RequestBody TransferOperatorEqDto transferOperatorEqDto) {
        operatorService.transfer(transferOperatorEqDto);
        return super.responseSuccess("迁移成功");
    }

    /**
     * 获取游客列表
     * @return
     */
    @ResponseBody
    @GetMapping(value = "/getUserList")
    public String getUserList() {
        Long uaaId = LoginUserUtil.get().getUaaId();
        OperatorVo operatorVo = operatorService.getByUaaId(uaaId);
        String operatorNo = operatorVo.getOperatorNo();
        return cmUserFeignClient.getUserList(operatorNo);
    }

    /**
     * 运营商商家
     * @return
     */
    @ResponseBody
    @GetMapping(value = "/getBusinessList")
    public String getBusinessList(@RequestBody(required = false)CmBusinessMange cmBusinessMange) {
        Long uaaId = LoginUserUtil.get().getUaaId();
        OperatorVo operatorVo = operatorService.getByUaaId(uaaId);
        String operatorNo = operatorVo.getOperatorNo();
        cmBusinessMange.setOperatorNo(operatorNo);
        return cmBusinessInfoFeignClient.getBusinessList(cmBusinessMange);
    }

    @ResponseBody
    @GetMapping(value = "/getOperatorInfoByUaaId")
    String getOperatorInfoByUaaId(@RequestParam("uaaId") Long uaaId) {
        OperatorVo operatorInfo = operatorService.getByUaaId(uaaId);
        return super.responseSuccess(operatorInfo, "查询成功");
    }

    @ResponseBody
    @GetMapping(value = "/getOperatorInfoByOperatorNo")
    String getOperatorInfoByOperatorNo(@RequestParam("operatorNo") String operatorNo) {
        OperatorVo operatorInfo = operatorService.getInfoByNo(operatorNo);
        return super.responseSuccess(operatorInfo, "查询成功");
    }

    @ResponseBody
    @GetMapping(value = "/getShopRatioByOperatorNo")
    String getShopRatioByOPN(@RequestParam("operatorNo") String operatorNo) {
        List<ShareProjectProfitShopVo> shareProjectProfitShopVo = shareConfigService.getShopRatioByOPN(operatorNo);
        return super.responseSuccess(shareProjectProfitShopVo, "查询成功");
    }

}
