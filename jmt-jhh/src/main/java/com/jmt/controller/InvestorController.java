package com.jmt.controller;

import cn.hutool.core.util.ObjectUtil;
import com.jmt.base.BaseController;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.common.ObjIdDto;
import com.jmt.model.eq.dto.InvestorEqPageQueryDto;
import com.jmt.model.eq.vo.InvestorEqInfoVo;
import com.jmt.model.jhh.dto.AssignInvestorEqDto;
import com.jmt.model.jhh.dto.InvestorAuditDto;
import com.jmt.model.jhh.dto.InvestorInfoDto;
import com.jmt.model.jhh.dto.InvestorPageQueryDto;
import com.jmt.model.jhh.vo.InvestorSelectVo;
import com.jmt.model.jhh.vo.InvestorVo;
import com.jmt.model.jhh.vo.JhhUserInfoVo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.service.InvestorService;
import com.jmt.util.LoginUserUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


@RestController
@RequestMapping("/jhh/investor/v1")
public class InvestorController extends BaseController{

    @Resource
    private InvestorService investorService;

    /**
     * 查询收益人的设备
     * @param pageQuery
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/eqPage")
    public String getInvestorEqPage(@RequestBody PageQuery<String> pageQuery) {
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
        }
        logger.info("分页查询参数:{}",pageQuery);
        PageResult<InvestorEqInfoVo> page = investorService.getInvestorEqPage(pageQuery);
        return super.responseSuccess(page,"查询成功");
    }
    /**
     * 查询收益人的设备
     * @param pageQuery
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/optionalEqPage")
    public String getOptionalEqPageByInvestor(@RequestBody PageQuery<InvestorEqPageQueryDto> pageQuery) {
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
        }
        logger.info("分页查询参数:{}",pageQuery);
        PageResult<InvestorEqInfoVo> page = investorService.getOptionalEqPageByInvestor(pageQuery);
        return super.responseSuccess(page,"查询成功");
    }
    /**
     * 按id查询收益人
     * @param id 主键ID
     * @return String
     */
    @ResponseBody
    @GetMapping(value = "/getInfo")
    public String getInfo(@RequestParam Long id) {
        InvestorVo investorInfo = investorService.getInfo(id);
        if (ObjectUtil.isNotEmpty(investorInfo)) {
            return super.responseSuccess(investorInfo,"查询成功");
        } else {
            return super.responseFail("查询失败:设备收益人不存在");
        }
    }
    /**
     * 分页查询
     * @param pageQuery 查询参数
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/getPage")
    public String getPage(@RequestBody(required = false) PageQuery<InvestorPageQueryDto> pageQuery) {
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
        }
        logger.info("分页查询参数:{}",pageQuery);
        PageResult<InvestorVo> page = investorService.getPage(pageQuery);
        return super.responseSuccess(page,"查询成功");
    }
    /**
     * 分页查询
     * @param pageQuery 查询参数
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/myList")
    public String getMyPage(@RequestBody(required = false) PageQuery<InvestorPageQueryDto> pageQuery) {
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
        }
        LoginUaaUser loginUser = LoginUserUtil.get();
        logger.info("分页查询参数:{}",pageQuery);
        PageResult<InvestorVo> page = investorService.getMyPage(pageQuery,loginUser);
        return super.responseSuccess(page,"查询成功");
    }

    /**
     * 新增收益人
     * @param investorInfoDto 新增参数
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/add")
    public String add(@RequestBody InvestorInfoDto investorInfoDto) {
        logger.info("新增收益人:{}",investorInfoDto);
        Integer r = investorService.add(investorInfoDto);
        if (r == 1) {
            return super.responseSuccess("新增成功");
        } else {
            return super.responseFail("新增失败");
        }
    }

    /**
     * 修改收益人信息，并修改相关审核信息
     * @param investorInfoDto 修改参数
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/update")
    public String update(@RequestBody InvestorInfoDto investorInfoDto) {
        logger.info("修改参数:{}",investorInfoDto);
        Integer r = investorService.update(investorInfoDto);
        if (r == 1) {
            return super.responseSuccess("修改成功");
        } else {
            return super.responseFail("修改失败");
        }
    }
    /**
     * 删除收益人以及相关信息
     * @param dto 主键ID
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/delete")
    public String delete(@RequestBody ObjIdDto dto) {
        logger.info("删除参数:{}",dto.getId());
        Integer r = investorService.delete(dto.getId());
        if (r == 1) {
            return super.responseSuccess("删除成功");
        } else {
            return super.responseFail("删除失败");
        }
    }
    /**
     * 收益人状态变更，并填写相关审核信息
     * @param investorAuditDto
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/audit")
    public String audit(@RequestBody InvestorAuditDto investorAuditDto) {
        LoginUaaUser auditUser = LoginUserUtil.get();
        logger.info("收益人状态变更参数:{}",investorAuditDto);
        Integer r = investorService.audit(investorAuditDto,auditUser);
        if (r == 1) {
            return super.responseSuccess("操作成功");
        } else {
            return super.responseFail("操作失败");
        }
    }
    /**
     * 设备收益人分配设备接口
     * @param assignInvestorEqDto
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/assign")
    public String assignEq(@RequestBody AssignInvestorEqDto assignInvestorEqDto) {
            logger.info("设备收益人分配设备参数:{}", assignInvestorEqDto);
            investorService.assignEq(assignInvestorEqDto);
            return super.responseSuccess("分配成功");
    }
    /**
     * 下拉数据框
     *
     * @return String
     */
    @ResponseBody
    @GetMapping(value = "/getInvestorSelect")
    public String getInvestorSelect() {
        List<InvestorSelectVo> list = investorService.getOperatorSelect();
        return super.responseSuccess(list,"查询成功");
    }
    /**
     * 冻结接口
     * @param dto 设备收益人ID
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/freeze")
    public String freeze(@RequestBody ObjIdDto dto) {
        Integer r = investorService.freeze(dto.getId());
        if (r == 1) {
            return super.responseSuccess("冻结成功");
        } else {
            return super.responseFail("冻结失败");
        }
    }
    /**
     * 设备收益人查询接口
     * @param uaaId
     * @return String
     */
    @ResponseBody
    @GetMapping(value = "/getInvestorInfoByUaaId")
    public String getInvestorInfoByUaaId(@RequestParam Long uaaId) {
            InvestorVo investorInfo = investorService.getInvestorInfoByUaaId(uaaId);
            return super.responseSuccess(investorInfo,"查询成功");
    }

    @ResponseBody
    @GetMapping(value = "/getUserInfoByInvestorNo")
    public String getUserInfo(@RequestParam String  investorNo) {
        JhhUserInfoVo jhhUserInfoVo = investorService.getUserInfoByInvestor(investorNo);
        return super.responseSuccess(jhhUserInfoVo,"查询成功");
    }
}
