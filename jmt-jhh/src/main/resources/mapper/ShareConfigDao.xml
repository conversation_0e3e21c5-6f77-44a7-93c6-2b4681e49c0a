<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace名字必须与Mapper接口名一致 -->

<mapper namespace="com.jmt.dao.ShareConfigDao">
	<insert id="add" parameterType="com.jmt.model.jhh.dto.ShareConfig">
		INSERT INTO jhh_profit_share_config
		    (projectId,projectName,operatorNo,operatorRatio,investorRatio,platformRatio,shopRatio,createTime,updateTime,isDelete)
		VALUES
		    (#{projectId},#{projectName},#{operatorNo},#{operatorRatio},#{investorRatio},#{platformRatio},#{shopRatio},NOW(),NOW(),0)
	</insert>
	<delete id="deleteByOperatorNo" parameterType="java.lang.String">
		DELETE FROM  jhh_profit_share_config WHERE operatorNo = #{operatorNo}
	</delete>
	<select id="getShareConfig" parameterType="java.lang.String" resultType="com.jmt.model.jhh.dto.ShareConfig">
		SELECT
			id,
			projectId,
			projectName,
			operatorNo,
			operatorRatio,
			investorRatio,
			platformRatio,
			shopRatio
		FROM
			jhh_profit_share_config
		WHERE
			isDelete = 0 AND operatorNo = #{operatorNo}
	</select>

	<select id="getShopRatioByOPN" parameterType="java.lang.String" resultType="com.jmt.model.jhh.vo.ShareProjectProfitShopVo">
		SELECT
			id,
			projectId,
			projectName,
			shopRatio
		FROM
			jhh_profit_share_config
		WHERE
			isDelete = 0 AND operatorNo = #{operatorNo}
	</select>
</mapper>
