<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace名字必须与Mapper接口名一致 -->

<mapper namespace="com.jmt.dao.SalesmanDao">

    <select id="getPage" parameterType="com.jmt.model.jhh.dto.SalesmanInfoDto" resultType="com.jmt.model.jhh.vo.SalesmanVo">
        SELECT
            s.id,
            s.uaaId,
            s.salesmanName,
            s.salesmanNo,
            s.idNo,
            s.headImg,
            s.salesmanType,
            s.operatorNo,
            s.dutyName,
            s.telPhone,
            s.isFirstLogin,
            s.fStatus,
            s.isDelete,
            s.country,
            s.province,
            s.city,
            s.area,
            s.address,
            o.operatorName AS operatorName
        FROM
            jhh_salesman_info s LEFT JOIN jhh_operator_info o ON s.operatorNo = o.operatorNo
        WHERE
            s.isDelete = 0
            <if test="salesmanType!=null">
                AND s.salesmanType = #{salesmanType}
            </if>
            <if test="operatorNo!=null">
                AND s.operatorNo = #{operatorNo}
            </if>
    </select>
    <select id="getInfo" parameterType="java.lang.Long" resultType="com.jmt.model.jhh.dto.SalesmanInfo">
        SELECT
            id,
            uaaId,
            salesmanName,
            salesmanNo,
            idNo,
            headImg,
            salesmanType,
            operatorNo,
            dutyName,
            telPhone,
            isFirstLogin,
            fStatus,
            isDelete,
            country,
            province,
            city,
            area,
            address
        FROM
            jhh_salesman_info
        WHERE
            id = #{id} AND isDelete = 0
    </select>
    <select id="getByUaaId" resultType="com.jmt.model.jhh.dto.SalesmanInfo">
        SELECT
            id,
            uaaId,
            salesmanName,
            salesmanNo,
            idNo,
            headImg,
            salesmanType,
            operatorNo,
            dutyName,
            telPhone,
            isFirstLogin,
            fStatus,
            isDelete,
            country,
            province,
            city,
            area,
            address
        FROM
            jhh_salesman_info
        WHERE
            uaaId = #{uaaId} AND isDelete = 0
    </select>

    <select id="getSalesmanSelect" parameterType="java.lang.String" resultType="com.jmt.model.jhh.vo.SalesmanSelectVo">
        SELECT id,salesmanName,salesmanNo FROM jhh_salesman_info WHERE isDelete = 0 AND operatorNo = #{operatorNo}
    </select>

    <insert id="add">
        INSERT INTO jhh_salesman_info (
            uaaId,
            salesmanName,
            salesmanNo,
            idNo,
            headImg,
            salesmanType,
            operatorNo,
            dutyName,
            telPhone,
            isFirstLogin,
            fStatus,
            createTime,
            updateTime,
            isDelete,
            country,
            province,
            city,
            area,
            address
        )
        VALUES
        (
            #{uaaId},
            #{salesmanName},
            #{salesmanNo},
            #{idNo},
            #{headImg},
            #{salesmanType},
            #{operatorNo},
            #{dutyName},
            #{telPhone},
            1,
            0,
            NOW(),
            NOW(),
            0,
            #{country},
            #{province},
            #{city},
            #{area},
            #{address}
        )
    </insert>
    <update id="transfer">
        UPDATE jhh_salesman_info SET operatorNo = #{targetOperatorNo} WHERE operatorNo = #{sourceOperatorNo} AND isdelete = 0 AND fStatus = 0
    </update>

    <select id="getDropByOperatorNo" resultType="com.jmt.model.jhh.vo.SalesmanSelectVo">
        SELECT id,salesmanName,salesmanNo
        FROM jhh_salesman_info
        WHERE isDelete = 0 and operatorNo = #{operatorNo}
    </select>
    <select id="getInfoBySalesmanNo" resultType="com.jmt.model.jhh.dto.SalesmanInfo">
        SELECT id,uaaId,salesmanName,salesmanNo,idNo,headImg,salesmanType,operatorNo,dutyName,telPhone,isFirstLogin,fStatus,isDelete
        FROM jhh_salesman_info
        WHERE isDelete = 0 and salesmanNo = #{salesmanNo}
    </select>
    <select id="getCountByOperatorNo" resultType="java.lang.Integer">
        SELECT count(1)
        FROM jhh_salesman_info
        WHERE isDelete = 0 and operatorNo = #{operatorNo}
    </select>

    <select id="getInfoByTelPhone" resultType="com.jmt.model.jhh.dto.SalesmanInfo">
        SELECT id,uaaId,salesmanName,salesmanNo,idNo,headImg,salesmanType,operatorNo,dutyName,telPhone,isFirstLogin,fStatus,isDelete
        FROM jhh_salesman_info
        WHERE isDelete = 0 and telPhone = #{telPhone}
        ORDER BY id DESC
        LIMIT 1
    </select>

    <update id="setupInfo" parameterType="com.jmt.model.jhh.dto.JhhUserInfoDto">
        UPDATE jhh_salesman_info SET
            salesmanName = #{userName},
            headImg = #{headImg},
            address = #{address},
            updateTime = NOW()
        WHERE
            uaaId = #{uaaId}
    </update>
</mapper>
