<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace名字必须与Mapper接口名一致 -->

<mapper namespace="com.jmt.dao.OperatorDao">
    <sql id="Base_Column_List">
        `id`,
        `uaaId`,
        `operatorName`,
        `operatorNo`,
        `headImg`,
        `contractNo`,
        `dutyName`,
        `telPhone`,
        `isProfitLimit`,
        `limitAmount`,
        `contractPicUrl`,
        `licensePicUrl`,
        `auditStatus`,
        `reason`,
        `isFirstLogin`,
        `createTime`,
        `updateTime`,
        `isDelete`,
        `country`,
        `province`,
        `city`,
        `area`,
        `address`,
        `startTime`,
        `endTime`
    </sql>
    <select id="getInfo" parameterType="java.lang.Long" resultType="com.jmt.model.jhh.vo.OperatorVo">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            jhh_operator_info
        WHERE
            id = #{id} AND isDelete = 0
    </select>
    <select id="getPage" parameterType="com.jmt.model.jhh.dto.OperatorInfoQueryDto" resultType="com.jmt.model.jhh.vo.OperatorVo">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            jhh_operator_info
        WHERE
            isDelete = 0
            <if test="address!=null">
                and address LIKE CONCAT('%',#{address},'%')
            </if>
            <if test="operatorNo!=null">
                and operatorNo LIKE CONCAT('%',#{operatorNo},'%')
            </if>
            <if test="operatorName!=null">
                and operatorName LIKE CONCAT('%',#{operatorName},'%')
            </if>
    </select>
    <select id="getOperatorSelect" parameterType="java.lang.String" resultType="com.jmt.model.jhh.vo.OperatorSelectVo">
        SELECT
            id,operatorNo,operatorName
        FROM
            jhh_operator_info
        WHERE
            isDelete = 0
            <if test="kn!=null">
                AND (
                    operatorNo LIKE CONCAT('%',#{kn},'%')
                    OR
                    operatorName LIKE CONCAT('%',#{kn},'%')
                )
            </if>
        ORDER BY
            operatorNo DESC
        LIMIT 10
    </select>
    <select id="getByOperatorNo" parameterType="java.lang.String" resultType="com.jmt.model.jhh.vo.OperatorVo">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            jhh_operator_info
        WHERE
            operatorNo = #{operatorNo} AND isDelete = 0 LIMIT 1
    </select>
    <select id="getByUaaId" parameterType="java.lang.Long" resultType="com.jmt.model.jhh.vo.OperatorVo">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            jhh_operator_info
        WHERE
            uaaId = #{uaaId} AND isDelete = 0 LIMIT 1
    </select>
    <insert id="add" parameterType="com.jmt.model.jhh.dto.OperatorInfo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO jhh_operator_info (
            `uaaId`,
            `operatorName`,
            `operatorNo`,
            `headImg`,
            `contractNo`,
            `dutyName`,
            `telPhone`,
            `isProfitLimit`,
            `limitAmount`,
            `contractPicUrl`,
            `licensePicUrl`,
            `auditStatus`,
            `reason`,
            `isFirstLogin`,
            `createTime`,
            `updateTime`,
            `isDelete`,
            `country`,
            `province`,
            `city`,
            `area`,
            `address`,
            `startTime`,
            `endTime`
        )
        VALUES
        (
            #{uaaId},
            #{operatorName},
            #{operatorNo},
            #{headImg},
            #{contractNo},
            #{dutyName},
            #{telPhone},
            #{isProfitLimit},
            #{limitAmount},
            #{contractPicUrl},
            #{licensePicUrl},
            #{auditStatus},
            #{reason},
            1,
            NOW(),
            NOW(),
            0,
            #{country},
            #{province},
            #{city},
            #{area},
            #{address},
            #{startTime},
            #{endTime}
        )
    </insert>
    <update id="update" parameterType="com.jmt.model.jhh.dto.OperatorInfo">
        UPDATE jhh_operator_info SET
                operatorName = #{operatorName},
                headImg = #{headImg},
                contractNo = #{contractNo},
                dutyName = #{dutyName},
                isProfitLimit = #{isProfitLimit},
                limitAmount = #{limitAmount},
                contractPicUrl = #{contractPicUrl},
                licensePicUrl = #{licensePicUrl},
                country = #{country},
                province = #{province},
                city = #{city},
                area = #{area},
                address = #{address},
                startTime = #{startTime},
                endTime = #{endTime},
                updateTime = NOW()
        WHERE
            id = #{id} AND isDelete = 0
    </update>
    <update id="delete" parameterType="java.lang.Long">
        UPDATE jhh_operator_info SET isDelete= 1 WHERE id = #{id} AND isDelete = 0
    </update>

    <update id="freeze">
        UPDATE jhh_operator_info SET auditStatus= #{auditStatus} WHERE id = #{id} AND isDelete = 0
    </update>

    <update id="setupInfo" parameterType="com.jmt.model.jhh.dto.JhhUserInfoDto">
        UPDATE jhh_operator_info SET
            operatorName = #{userName},
            headImg = #{headImg},
            address = #{address},
            updateTime = NOW()
        WHERE
            uaaId = #{uaaId}
    </update>

    <select id="getByTelPhone" parameterType="java.lang.String" resultType="com.jmt.model.jhh.vo.OperatorVo">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            jhh_operator_info
        WHERE
            telPhone = #{telPhone} AND isDelete = 0
        LIMIT 1
    </select>
</mapper>
