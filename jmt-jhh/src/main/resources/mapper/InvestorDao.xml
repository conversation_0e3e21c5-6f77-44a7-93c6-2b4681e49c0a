<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace名字必须与Mapper接口名一致 -->

<mapper namespace="com.jmt.dao.InvestorDao">
    <sql id="Base_Column_List">
        `id`,
        `uaaId`,
        `investorName`,
        `investorNo`,
        `headImg`,
        `contractNo`,
        `dutyName`,
        `investAmount`,
        `eqNum`,
        `telPhone`,
        `nature`,
        `isProfitLimit`,
        `limitAmount`,
        `contractPicUrl`,
        `licensePicUrl`,
        `creditCode`,
        `idFront`,
        `idBack`,
        `idNo`,
        `auditStatus`,
        `reason`,
        `auditUser`,
        `auditUserName`,
        `auditTime`,
        `isFirstLogin`,
        `createTime`,
        `updateTime`,
        `isDelete`,
        `country`,
        `province`,
        `city`,
        `area`,
        `address`,
        `operatorNo`,
        `startTime`,
        `endTime`
    </sql>
    <insert id="add" parameterType="com.jmt.model.jhh.dto.InvestorInfo">
        INSERT INTO jhh_investor_info(
            `uaaId`,
            `investorName`,
            `investorNo`,
            `headImg`,
            `contractNo`,
            `dutyName`,
            `investAmount`,
            `eqNum`,
            `telPhone`,
            `nature`,
            `isProfitLimit`,
            `limitAmount`,
            `contractPicUrl`,
            `licensePicUrl`,
            `creditCode`,
            `idFront`,
            `idBack`,
            `idNo`,
            `auditStatus`,
            `reason`,
            `isFirstLogin`,
            `createTime`,
            `updateTime`,
            `isDelete`,
            `country`,
            `province`,
            `city`,
            `area`,
            `address`,
            `operatorNo`,
            `startTime`,
            `endTime`
        )
        VALUES
        (
            #{uaaId},
            #{investorName},
            #{investorNo},
            #{headImg},
            #{contractNo},
            #{dutyName},
            #{investAmount},
            #{eqNum},
            #{telPhone},
            #{nature},
            #{isProfitLimit},
            #{limitAmount},
            #{contractPicUrl},
            #{licensePicUrl},
            #{creditCode},
            #{idFront},
            #{idBack},
            #{idNo},
            0,
            #{reason},
            0,
            NOW(),
            NOW(),
            0,
            #{country},
            #{province},
            #{city},
            #{area},
            #{address},
            #{operatorNo},
            #{startTime},
            #{endTime}
        )
    </insert>

    <update id="delete">
        UPDATE jhh_investor_info SET isDelete = 1 WHERE id = #{id}
    </update>

    <select id="getPage" parameterType="com.jmt.model.jhh.dto.InvestorPageQueryDto" resultType="com.jmt.model.jhh.vo.InvestorVo">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            jhh_investor_info
        WHERE
            isDelete = 0
            <if test="investorNo!=null">
                AND investorNo LIKE CONCAT('%',#{investorNo},'%')
            </if>
            <if test="investorName!=null">
                AND investorName LIKE CONCAT('%',#{investorName},'%')
            </if>
            <if test="telPhone!=null">
                AND telPhone LIKE CONCAT('%',#{telPhone},'%')
            </if>
            <if test="operatorNo!=null">
                AND operatorNo = #{operatorNo}
            </if>
            <if test="auditStatus!=null">
                AND auditStatus = #{auditStatus}
            </if>
    </select>

    <select id="getInfo" resultType="com.jmt.model.jhh.vo.InvestorVo">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            jhh_investor_info
        WHERE
            id = #{id} AND isDelete = 0
    </select>

    <select id="getByInvestorNo" resultType="com.jmt.model.jhh.vo.InvestorVo">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            jhh_investor_info
        WHERE
            investorNo = #{investorNo} AND isDelete = 0
    </select>
    <select id="getInvestorSelect" resultType="com.jmt.model.jhh.vo.InvestorSelectVo">
        SELECT
            id,investorNo,investorName
        FROM
            jhh_investor_info
        WHERE
            isDelete = 0 AND auditStatus = 1
        ORDER BY
            investorName ASC
    </select>
    <select id="getByUaaId" resultType="com.jmt.model.jhh.vo.InvestorVo">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            jhh_investor_info
        WHERE
            uaaId = #{uaaId} AND isDelete = 0
    </select>
    <select id="getCountByOperatorNo" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM jhh_investor_info WHERE operatorNo = #{operatorNo} AND isDelete = 0
    </select>
    <update id="update" parameterType="com.jmt.model.jhh.dto.InvestorInfo">
        UPDATE jhh_investor_info SET
            investorName = #{investorName},
            contractNo = #{contractNo},
            nature = #{nature},
            investAmount = #{investAmount},
            dutyName = #{dutyName},
            isProfitLimit = #{isProfitLimit},
            limitAmount = #{limitAmount},
            contractPicUrl = #{contractPicUrl},
            licensePicUrl = #{licensePicUrl},
            creditCode = #{creditCode},
            idFront = #{idFront},
            idBack = #{idBack},
            idNo = #{idNo},
            eqNum = #{eqNum},
            headImg = #{headImg},
            country = #{country},
            province = #{province},
            city = #{city},
            area = #{area},
            address = #{address},
            startTime = #{startTime},
            endTime = #{endTime},
            updateTime = NOW()
        WHERE
            id = #{id} AND isDelete = 0
    </update>

    <update id="setupInfo" parameterType="com.jmt.model.jhh.dto.JhhUserInfoDto">
        UPDATE jhh_investor_info SET
            investorName = #{userName},
            address = #{address},
            headImg = #{headImg},
            updateTime = NOW()
        WHERE
            uaaId = #{uaaId}
    </update>

    <update id="freeze">
        UPDATE jhh_investor_info SET auditStatus= #{auditStatus} WHERE id = #{id} AND isDelete = 0
    </update>

    <update id="audit">
        UPDATE jhh_investor_info SET
            auditStatus = #{auditStatus},
            auditUser = #{auditUser},
            reason = #{reason},
            auditTime = NOW(),
            updateTime = NOW()
        WHERE
            id = #{id}

    </update>

    <update id="setInvestorUaaId">
        UPDATE jhh_investor_info SET
            uaaId = #{uaaId},
            updateTime = NOW()
        WHERE
            id = #{id}
    </update>
</mapper>
