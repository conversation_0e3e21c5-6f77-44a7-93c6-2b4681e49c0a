server:
  port: 8080
spring:
  redis:
    port: 6379
    host: r-bp1gzx5dw9ip4x11yppd.redis.rds.aliyuncs.com
    password: jmt#9901#JMT
    database: 7
  cloud:
    gateway:
      routes:
        #统一身份认证服务
        - id: jmt-uaa
          uri: lb://jmt-uaa
          predicates:
            - Path=/uaa/**
        #公共服务
        - id: jmt-common
          uri: lb://jmt-common
          predicates:
            - Path=/common/**
        #后台服务
        - id: jmt-admin
          uri: lb://jmt-admin
          predicates:
            - Path=/admin/**
        #聚合伙服务
        - id: jmt-jhh
          uri: lb://jmt-jhh
          predicates:
            - Path=/jhh/**
        #筷圣餐谋服务
        - id: jmt-cm
          uri: lb://jmt-cm
          predicates:
            - Path=/cm/**
        #收益服务
        - id: jmt-profit
          uri: lb://jmt-profit
          predicates:
            - Path=/profit/**
        #设备服务
        - id: jmt-eq
          uri: lb://jmt-eq
          predicates:
            - Path=/eq/**
        #配送服务
        - id: jmt-zg
          uri: lb://jmt-zg
          predicates:
            - Path=/zg/**
        #投广服务
        - id: jmt-tg
          uri: lb://jmt-tg
          predicates:
            - Path=/tg/**
        #补电服务
        - id: jmt-bd
          uri: lb://jmt-bd
          predicates:
            - Path=/bd/**