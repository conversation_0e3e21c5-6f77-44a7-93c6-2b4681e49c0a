package com.jmt.auth.handler;

import cn.hutool.core.map.MapUtil;
import com.jmt.util.GsonUtil;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * 当未登录或者token失效访问接口时，自定义的返回结果
 * Created by macro on 2018/5/14.
 */
@Component
public class JmtAuthenticationEntryPoint implements AuthenticationEntryPoint {
    /**
     * 身份认证失败编码
     */
    private static final String SERVICE_RESPONSE_FAIL_AUTH_CODE = "301";

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException authException) throws IOException, ServletException {
        Map<Object, Object> resMap = MapUtil.builder().map();
        resMap.put("code",SERVICE_RESPONSE_FAIL_AUTH_CODE);
        resMap.put("msg","认证失败");
        resMap.put("data","");

        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json");
        response.getWriter().println(GsonUtil.toJson(resMap));
        response.getWriter().flush();
    }
}
