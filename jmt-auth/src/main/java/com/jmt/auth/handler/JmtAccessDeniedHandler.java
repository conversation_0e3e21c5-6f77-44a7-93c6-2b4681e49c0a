package com.jmt.auth.handler;

import cn.hutool.core.map.MapUtil;
import com.jmt.util.GsonUtil;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * 当访问接口没有权限时，自定义的返回结果
 * Created by macro on 2018/4/26.
 */
@Component
public class JmtAccessDeniedHandler implements AccessDeniedHandler {
    /**
     * 无访问权限编码
     */
    private static final String SERVICE_RESPONSE_NO_ACCESS_CODE = "403";

    @Override
    public void handle(HttpServletRequest request,HttpServletResponse response,AccessDeniedException e) throws IOException {
        Map<Object, Object> resMap = MapUtil.builder().map();
        resMap.put("code",SERVICE_RESPONSE_NO_ACCESS_CODE);
        resMap.put("msg","无权限访问");
        resMap.put("data","");
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json");
        response.getWriter().println(GsonUtil.toJson(resMap));
        response.getWriter().flush();
    }
}
