server:
  port: 8085
  tomcat:
    uri-encoding: UTF-8
  servlet:
    encoding:
      enabled: true
      force: true
      charset: UTF-8
spring:
  servlet:
    multipart:
      max-file-size: 1024MB
      max-request-size: 20MB
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: ***********************************************************************************************************************************************************************************
    username: jmt-cm
    password: Jmt@18020875751
    driver-class-name: com.mysql.cj.jdbc.Driver
  redis:
    port: 6379
    host: r-bp1gzx5dw9ip4x11yppd.redis.rds.aliyuncs.com
    password: jmt#9901#JMT
    database: 7
  main:
    allow-bean-definition-overriding: true
  task:
    execution:
      thread-name-prefix: ${mesh.application.name}-task-
      pool:
        core-size: 2 # 核心异步任务池数量
        max-size: 50 # 最大数量
        queue-capacity: 10000
    scheduling:
      thread-name-prefix: ${mesh.application.name}-scheduling-
      pool:
        size: 2 # 定时任务线程数量
  thymeleaf:
    mode: HTML
  jackson:
    # 忽略null字段 不返回前端
    defaultPropertyInclusion: NON_NULL
    # 忽略未知字段
    deserialization:
      FAIL_ON_UNKNOWN_PROPERTIES: false
    parser:
      ALLOW_UNQUOTED_CONTROL_CHARS: true
    serialization:
      WRITE_DATES_AS_TIMESTAMPS: false
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
#业务系统相关初始化参数
secure:
  ignored:
    urls: #安全路径白名单
      - /cm/user/v1/add
      - /cm/supplyReqInfo/v1/selectIndexAllPage
      - /cm/user/v1/getCmUserNo
