<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.CmBoxBookDao">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, boxNo, userId, diningDate, diningPeriod, diningPeople,
        linkman, telPhone, reservationStatus, remarks,
        createTime, updateTime, isDelete
    </sql>

    <insert id="insert" parameterType="com.jmt.model.cm.entity.CmBoxBook">
        INSERT INTO cm_box_book (
        boxNo,
        userId,
        diningDate,
        diningPeriod,
        diningPeople,
        linkman,
        telPhone,
        reservationStatus,
        remarks,
        createTime,
        updateTime,
        isDelete
        ) VALUES (
        #{boxNo},
        #{userId},
        #{diningDate},
        #{diningPeriod},
        #{diningPeople},
        #{linkman},
        #{telPhone},
        #{reservationStatus},
        #{remarks},
        NOW(),
        NOW(),
        0
        )
    </insert>

    <!-- 根据ID查询预定记录 -->
    <select id="selectById" parameterType="java.lang.Long" resultType="com.jmt.model.cm.entity.CmBoxBook">
        SELECT
        <include refid="Base_Column_List"/>
        FROM cm_box_book
        WHERE id = #{id} AND isDelete = 0
    </select>

    <!-- 根据条件查询预定记录列表 -->
<!--    <select id="selectByCondition" parameterType="com.jmt.model.cm.entity.CmBoxBook" resultType="com.jmt.model.cm.entity.CmBoxBook">-->
<!--        SELECT-->
<!--        <include refid="Base_Column_List"/>-->
<!--        FROM cm_box_book-->
<!--        WHERE is_delete = 0-->
<!--        <if test="boxNo != null and boxNo != ''">-->
<!--            AND box_no = #{boxNo}-->
<!--        </if>-->
<!--        <if test="userId != null">-->
<!--            AND user_id = #{userId}-->
<!--        </if>-->
<!--        <if test="diningDate != null">-->
<!--            AND dining_date = #{diningDate}-->
<!--        </if>-->
<!--        <if test="diningPeriod != null">-->
<!--            AND dining_period = #{diningPeriod}-->
<!--        </if>-->
<!--        <if test="reservationStatus != null">-->
<!--            AND reservation_status = #{reservationStatus}-->
<!--        </if>-->
<!--        ORDER BY create_time DESC-->
<!--    </select>-->

    <!-- 更新预定记录 -->
    <update id="update" parameterType="com.jmt.model.cm.entity.CmBoxBook">
        UPDATE cm_box_book
        SET
        boxNo = #{boxNo},
        userId = #{userId},
        diningDate = #{diningDate},
        diningPeriod = #{diningPeriod},
        diningPeople = #{diningPeople},
        linkman = #{linkman},
        telPhone = #{telPhone},
        reservationStatus = #{reservationStatus},
        remarks = #{remarks},
        updateTime = NOW()
        WHERE id = #{id} AND isDelete = 0
    </update>



    <!-- 逻辑删除预定记录 -->
    <update id="deleteById" parameterType="java.lang.Long">
        UPDATE cm_box_book
        SET
        isDelete = 1,
        updateTime = NOW()
        WHERE id = #{id}
    </update>




</mapper>