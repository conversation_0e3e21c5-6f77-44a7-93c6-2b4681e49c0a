<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.CmBusinessMemberDao">

    <sql id="Base_Column_List">
        id, uaaId, busNo, memName,sex, telPhone, country, province, city, area,
        address, longitude, latiude, memStatus, createTime, updateTime, isDelete
    </sql>

    <!-- 新增 -->
    <insert id="insert" parameterType="com.jmt.model.cm.entity.CmBusinessMember" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO cm_business_member(
        uaaId, busNo, memName, sex, telPhone,
        country, province, city, area, address,
        longitude, latiude, memStatus, createTime, updateTime, isDelete
        ) VALUES (
        #{uaaId}, #{busNo}, #{memName}, #{sex}, #{telPhone},
        #{country}, #{province}, #{city}, #{area}, #{address},
        #{longitude}, #{latiude}, #{memStatus},#{createTime}, #{updateTime}, #{isDelete}
        )
    </insert>


    <update id="delete" parameterType="java.lang.Long">
        UPDATE cm_business_member
        SET isDelete = 1, updateTime = NOW()
        WHERE id = #{id} AND isDelete = 0
    </update>


    <!-- 更新 -->
    <update id="update" parameterType="com.jmt.model.cm.entity.CmBusinessMember">
        UPDATE cm_business_member
        <set>
            <if test="busNo != null">busNo = #{busNo,jdbcType=VARCHAR},</if>
            <if test="memName != null">memName = #{memName,jdbcType=VARCHAR},</if>
            <if test="sex != null">sex = #{sex,jdbcType=INTEGER},</if>
            <if test="telPhone != null">telPhone = #{telPhone,jdbcType=VARCHAR},</if>
            <if test="country != null">country = #{country,jdbcType=VARCHAR},</if>
            <if test="province != null">province = #{province,jdbcType=VARCHAR},</if>
            <if test="city != null">city = #{city,jdbcType=VARCHAR},</if>
            <if test="area != null">area = #{area,jdbcType=VARCHAR},</if>
            <if test="address != null">address = #{address,jdbcType=VARCHAR},</if>
            <if test="longitude != null">longitude = #{longitude,jdbcType=VARCHAR},</if>
            <if test="latiude != null">latiude = #{latiude,jdbcType=VARCHAR},</if>
            <if test="memStatus != null">memStatus = #{memStatus,jdbcType=INTEGER},</if>
            <if test="createTime != null">createTime = #{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">updateTime = #{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="isDelete != null">isDelete = #{isDelete,jdbcType=INTEGER},</if>
        </set>
        WHERE id = #{id} AND isDelete = 0
    </update>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultType="com.jmt.model.cm.entity.CmBusinessMember">
        SELECT <include refid="Base_Column_List"/>
        FROM cm_business_member
        WHERE id = #{id} AND isDelete = 0
    </select>

    <!-- 查询所有 -->
    <select id="selectAll" resultType="com.jmt.model.cm.entity.CmBusinessMember">
        SELECT <include refid="Base_Column_List"/> FROM cm_business_member
        WHERE isDelete = 0
    </select>

    <!-- 条件查询 -->
    <select id="selectByBusNo" parameterType="java.lang.String" resultType="com.jmt.model.cm.entity.CmBusinessMember">
        SELECT <include refid="Base_Column_List"/>
        FROM cm_business_member
        WHERE busNo = #{busNo} AND isDelete = 0 AND memStatus = 0
    </select>

    <select id="selectByCondition" parameterType="com.jmt.model.cm.dto.CmBusinessMemberDTO" resultType="com.jmt.model.cm.dto.CmBusinessMemberDTO">
        SELECT <include refid="Base_Column_List"/>
        FROM cm_business_member
        <where>
            isDelete = 0
            <if test="busNo != null and busNo != ''">AND busNo LIKE CONCAT('%', #{busNo}, '%')</if>
            <if test="memName != null and memName != ''">AND memName LIKE CONCAT('%', #{memName}, '%')</if>
            <if test="sex != null">AND sex = #{sex}</if>
            <if test="telPhone != null and telPhone != ''">AND telPhone = #{telPhone}</if>
            <if test="country != null and country != ''">AND country = #{country}</if>
            <if test="province != null and province != ''">AND province = #{province}</if>
            <if test="city != null and city != ''">AND city = #{city}</if>
            <if test="area != null and area != ''">AND area = #{area}</if>
            <if test="address != null and address != ''">AND address LIKE CONCAT('%', #{address}, '%')</if>
            <if test="longitude != null and longitude != ''">AND longitude = #{longitude,jdbcType=VARCHAR}</if>
            <if test="latiude != null and latiude != ''">AND latiude = #{latiude,jdbcType=VARCHAR}</if>
            <if test="memStatus != null">AND memStatus = #{memStatus}</if>
        </where>
        ORDER BY createTime
    </select>

</mapper>