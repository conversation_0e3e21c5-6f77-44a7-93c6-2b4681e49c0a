<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.CmBusinessShopDao">
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, userId, busNo, shopNo, shopName, linkman, sex, telPhone,
        country, province, city, area, address, longitude, latiude,
        memStatus, createTime, updateTime, isDelete, operatorNo,
        refereeNo, devloperNo, shopPhoto, shopLicense, categoryId,
        measure, ydeskNum, fdeskNum, comsumeAvg, openTime, visitorAvg,
        busStar, remark, auditStatus, reason,creditCode,legalPerson
    </sql>

    <!-- 新增 -->
    <insert id="insert" parameterType="com.jmt.model.cm.entity.CmBusinessShop" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO cm_business_shop (
        userId, busNo, shopNo, shopName, linkman, sex, telPhone,
        country, province, city, area, address, longitude, latiude,
        memStatus, createTime, updateTime, isDelete, operatorNo,
        refereeNo, devloperNo, shopPhoto, shopLicense, categoryId,
        measure, ydeskNum, fdeskNum, comsumeAvg, openTime, visitorAvg,
        busStar, remark, auditStatus, reason,creditCode,legalPerson
        ) VALUES (
        #{userId}, #{busNo}, #{shopNo}, #{shopName}, #{linkman}, #{sex}, #{telPhone},
        #{country}, #{province}, #{city}, #{area}, #{address}, #{longitude}, #{latiude},
        #{memStatus}, #{createTime}, #{updateTime}, #{isDelete}, #{operatorNo},
        #{refereeNo}, #{devloperNo}, #{shopPhoto}, #{shopLicense}, #{categoryId},
        #{measure}, #{ydeskNum}, #{fdeskNum}, #{comsumeAvg}, #{openTime}, #{visitorAvg},
        #{busStar}, #{remark}, #{auditStatus}, #{reason}, #{creditCode}, #{legalPerson}
        )
    </insert>

    <!-- 根据ID删除 -->
    <update id="delete" parameterType="java.lang.Long">
        UPDATE cm_business_shop
        SET isDelete = 1, updateTime = NOW()
        WHERE id = #{id} AND isDelete = 0
    </update>

    <update id="updateByEqApply" parameterType="com.jmt.model.cm.entity.CmBusinessShop">
        UPDATE cm_business_shop
        <set>
            <if test="measure != null">measure = #{measure},</if>
            <if test="comsumeAvg != null">comsumeAvg = #{comsumeAvg},</if>
            <if test="openTime != null">openTime = #{openTime},</if>
            <if test="visitorAvg != null">visitorAvg = #{visitorAvg},</if>
            <if test="updateTime != null">updateTime = #{updateTime},</if>
            <if test="shopInteriorPhoto != null">shopInteriorPhoto = #{shopInteriorPhoto},</if>
        </set>
        WHERE shopNo = #{shopNo} AND isDelete = 0
    </update>

    <!-- 更新 -->
    <update id="updateById" parameterType="com.jmt.model.cm.entity.CmBusinessShop">
        UPDATE cm_business_shop
        <set>
            <if test="userId != null">userId = #{userId},</if>
            <if test="busNo != null">busNo = #{busNo},</if>
            <if test="shopNo != null">shopNo = #{shopNo},</if>
            <if test="shopName != null">shopName = #{shopName},</if>
            <if test="linkman != null">linkman = #{linkman},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="telPhone != null">telPhone = #{telPhone},</if>
            <if test="country != null">country = #{country},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="area != null">area = #{area},</if>
            <if test="address != null">address = #{address},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latiude != null">latiude = #{latiude},</if>
            <if test="memStatus != null">memStatus = #{memStatus},</if>
            <if test="createTime != null">createTime = #{createTime},</if>
            <if test="updateTime != null">updateTime = #{updateTime},</if>
            <if test="isDelete != null">isDelete = #{isDelete},</if>
            <if test="operatorNo != null">operatorNo = #{operatorNo},</if>
            <if test="refereeNo != null">refereeNo = #{refereeNo},</if>
            <if test="devloperNo != null">devloperNo = #{devloperNo},</if>
            <if test="shopPhoto != null">shopPhoto = #{shopPhoto},</if>
            <if test="shopLicense != null">shopLicense = #{shopLicense},</if>
            <if test="categoryId != null">categoryId = #{categoryId},</if>
            <if test="measure != null">measure = #{measure},</if>
            <if test="ydeskNum != null">ydeskNum = #{ydeskNum},</if>
            <if test="fdeskNum != null">fdeskNum = #{fdeskNum},</if>
            <if test="comsumeAvg != null">comsumeAvg = #{comsumeAvg},</if>
            <if test="openTime != null">openTime = #{openTime},</if>
            <if test="visitorAvg != null">visitorAvg = #{visitorAvg},</if>
            <if test="busStar != null">busStar = #{busStar},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="auditStatus != null">auditStatus = #{auditStatus},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="creditCode != null">creditCode = #{creditCode},</if>
            <if test="legalPerson != null">legalPerson = #{legalPerson},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultType="com.jmt.model.cm.entity.CmBusinessShop">
        SELECT
        <include refid="Base_Column_List"/>
        FROM cm_business_shop
        WHERE id = #{id} AND isDelete = 0
    </select>

    <!-- 根据参谋用户ID查询 -->
    <select id="selectByUserId" parameterType="java.lang.Long" resultType="com.jmt.model.cm.entity.CmBusinessShop">
        SELECT
        <include refid="Base_Column_List"/>
        FROM cm_business_shop
        WHERE userId = #{userId} AND isDelete = 0
    </select>

    <select id="selectListByBusNo" parameterType="java.lang.String" resultType="com.jmt.model.cm.entity.CmBusinessShop">
        SELECT
        <include refid="Base_Column_List"/>
        FROM cm_business_shop
        WHERE busNo = #{busNo} AND isDelete = 0
        ORDER BY createTime DESC
    </select>

    <select id="getBusinessShopListByBusNo" parameterType="java.lang.String" resultType="com.jmt.model.cm.vo.CmBusinessShopVO">
        SELECT id,userId,shopNo,shopName,country,province,city,area,createTime,refereeNo,shopPhoto, busStar,auditStatus
        FROM cm_business_shop
        WHERE busNo = #{busNo} AND isDelete = 0
        ORDER BY createTime DESC
    </select>



    <select id="countByBusNo" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM cm_business_shop WHERE shopNo = #{shopNo} and isDelete = 0
    </select>

    <select id="selectBusNameByUserId" resultType="java.lang.String">
        SELECT shopName FROM cm_business_shop
        WHERE userId = #{userId} AND isDelete = 0
    </select>

    <select id="selectBusInfoByUserId" parameterType="java.lang.Long" resultType="com.jmt.model.cm.vo.PubBusInfoVO">
        SELECT  userId, shopName AS busName,shopNo AS busNo,telPhone,busStar, country, province, city, area, address,shopPhoto AS busPhoto,categoryId
        FROM cm_business_shop
        WHERE userId = #{userId}
        AND isDelete = 0
    </select>

    <select id="getShopByNo" parameterType="java.lang.String" resultType="com.jmt.model.cm.vo.CmBusinessShopVO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM cm_business_shop
        WHERE shopNo = #{shopNo} AND isDelete = 0
    </select>

    <select id="selectShopById" resultType="com.jmt.model.cm.vo.CmBusinessShopDetailVO">
        SELECT
        id, userId, busNo, shopNo, shopName, telPhone,linkman,
        createTime, refereeNo, shopPhoto, shopLicense,
        categoryId, creditCode, legalPerson ,province ,city,area,address
        FROM cm_business_shop
        WHERE id = #{id}
    </select>

    <select id="selectShopBaseInfoById" resultType="com.jmt.model.cm.vo.ShopBaseInfo">
        SELECT
        openTime ,measure,comsumeAvg,visitorAvg, shopPhoto, shopInteriorPhoto
        FROM cm_business_shop
        WHERE id = #{id}
    </select>

    <select id="getUserIdByCreditCode" parameterType="java.lang.String" resultType="java.lang.Long">
        SELECT userId
        FROM cm_business_shop
        WHERE creditCode = #{creditCode}
        AND isDelete = 0
        LIMIT 1
    </select>
</mapper>
