<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.CmBoxInfoDetailDao">

    <sql id="Base_Column_List">
        id,boxNo,dataType,textLabel,createTime,updateTime,isDelete
    </sql>


    <insert id="insert" parameterType="com.jmt.model.cm.entity.CmBoxInfoDetail" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO cm_box_info_detail (
        boxNo,dataType,textLabel,createTime,updateTime,isDelete
        ) VALUES (
        #{boxNo}, #{dataType}, #{textLabel}, #{createTime}, #{updateTime}, #{isDelete}
        )
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO cm_box_info_detail (
        boxNo,dataType,textLabel,createTime,updateTime,isDelete
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
              #{item.boxNo}, #{item.dataType}, #{item.textLabel}, #{item.createTime}, #{item.updateTime}, #{item.isDelete}
            )
        </foreach>
    </insert>

    <update id="delete" parameterType="java.lang.Integer">
        UPDATE cm_box_info_detail SET isDelete = 1 WHERE id = #{id}
    </update>

    <update id="deleteByBoxNo" parameterType="java.lang.String">
        UPDATE cm_box_info_detail SET isDelete = 1 WHERE boxNo = #{boxNo}
    </update>



    <update id="updateById" parameterType="com.jmt.model.cm.entity.CmBoxInfoDetail">
        UPDATE cm_box_info_detail
        <set>
            <if test="boxNo != null">boxNo = #{boxNo},</if>
            <if test="dataType != null">dataType = #{dataType},</if>
            <if test="textLabel != null">textLabel = #{textLabel},</if>
            updateTime = NOW()
        </set>
        WHERE id = #{id} AND isDelete = 0
    </update>

    <select id="selectById" parameterType="java.lang.Long" resultType="com.jmt.model.cm.entity.CmBoxInfoDetail">
        SELECT <include refid="Base_Column_List"/>
        FROM cm_box_info_detail WHERE id = #{id} AND isDelete = 0
    </select>

    <select id="selectByBoxNo" parameterType="java.lang.String" resultType="com.jmt.model.cm.entity.CmBoxInfoDetail">
        SELECT <include refid="Base_Column_List"/>
        FROM cm_box_info_detail
        WHERE boxNo = #{boxNo} AND isDelete = 0
        ORDER BY createTime DESC
    </select>

    <select id="selectByBoxNos" resultType="com.jmt.model.cm.entity.CmBoxInfoDetail">
        SELECT <include refid="Base_Column_List"/> FROM cm_box_info_detail
        WHERE boxNo IN
        <foreach collection="list" item="boxNo" open="(" separator="," close=")">
            #{boxNo}
        </foreach>
        AND isDelete = 0
        ORDER BY boxNo, createTime
    </select>






</mapper>