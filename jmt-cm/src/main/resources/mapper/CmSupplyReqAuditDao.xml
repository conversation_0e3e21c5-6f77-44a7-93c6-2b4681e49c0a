<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.CmSupplyReqAuditDao">

    <sql id="Base_Column_List">
        id, auditUser, infoId, auditStatus, reason, createTime, updateTime, isDelete
    </sql>

    <insert id="insert" parameterType="com.jmt.model.cm.entity.CmSupplyReqAudit" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO cm_supply_req_audit (
        auditUser, infoId, auditStatus, reason,
        createTime, updateTime, isDelete
        ) VALUES (
        #{auditUser}, #{infoId}, #{auditStatus}, #{reason},
        #{createTime}, #{updateTime}, #{isDelete}
        )
    </insert>


    <update id="update" parameterType="com.jmt.model.cm.entity.CmSupplyReqAudit">
        UPDATE cm_supply_req_audit
        <set>
            <if test="auditUser != null">auditUser = #{auditUser},</if>
            <if test="infoId != null">infoId = #{infoId},</if>
            <if test="auditStatus != null">auditStatus = #{auditStatus},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="updateTime != null">updateTime = #{updateTime},</if>
            <if test="isDelete != null">isDelete = #{isDelete},</if>
        </set>
        WHERE id = #{id}
    </update>


    <update id="logicalDeleteById">
        UPDATE cm_supply_req_audit
        SET isDelete = 1,
        updateTime = NOW()
        WHERE id = #{id} AND isDelete = 0
    </update>


    <select id="selectById" parameterType="java.lang.Long" resultType="com.jmt.model.cm.entity.CmSupplyReqAudit">
        SELECT
        <include refid="Base_Column_List"/>
        FROM cm_supply_req_audit
        WHERE id = #{id}
    </select>


    <select id="selectByInfoId" parameterType="java.lang.Long" resultType="com.jmt.model.cm.entity.CmSupplyReqAudit">
        SELECT
        <include refid="Base_Column_List"/>
        FROM cm_supply_req_audit
        WHERE infoId = #{infoId} AND isDelete = 0
    </select>

    <!-- 根据审核状态查询 -->
    <select id="selectByAuditStatus" parameterType="java.lang.Integer" resultType="com.jmt.model.cm.entity.CmSupplyReqAudit">
        SELECT
        <include refid="Base_Column_List"/>
        FROM cm_supply_req_audit
        WHERE auditStatus = #{auditStatus} AND isDelete = 0
        ORDER BY createTime DESC
    </select>




</mapper>