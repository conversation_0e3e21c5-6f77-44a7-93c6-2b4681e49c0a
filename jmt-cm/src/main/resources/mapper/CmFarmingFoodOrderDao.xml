<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.CmFarmingFoodOrderDao">
    <sql id="Base_Column_List">
        id, infoId,userId, orderNo, orderName, busNo, payType, payAmount, orderStatus, reason, createTime, updateTime, isDelete
    </sql>

    <insert id="insert" parameterType="com.jmt.model.cm.entity.CmFarmingFoodOrder" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO cm_farming_food_order (
        infoId,
        userId,
        orderNo,
        orderName,
        busNo,
        payType,
        payAmount,
        orderStatus,
        reason,
        createTime,
        updateTime,
        isDelete
        ) VALUES (
        #{infoId},
        #{userId},
        #{orderNo},
        #{orderName},
        #{busNo},
        #{payType},
        #{payAmount},
        #{orderStatus},
        #{reason},
        #{createTime},
        #{updateTime},
        #{isDelete}
        )
    </insert>

    <!-- 根据订单 ID 查询订单 -->
    <select id="selectById" parameterType="long" resultType="com.jmt.model.cm.entity.CmFarmingFoodOrder">
        SELECT  <include refid="Base_Column_List"/>
        FROM cm_farming_food_order WHERE id = #{id} AND isDelete = 0
    </select>

<!--    根据订单编号查询订单-->
    <select id="selectByOrderNo" parameterType="java.lang.String" resultType="com.jmt.model.cm.entity.CmFarmingFoodOrder">
        SELECT  <include refid="Base_Column_List"/>
        FROM cm_farming_food_order WHERE orderNo = #{orderNo} AND isDelete = 0
    </select>

    <!-- 更新订单状态 -->
    <update id="update" parameterType="com.jmt.model.cm.entity.CmFarmingFoodOrder">
        UPDATE cm_farming_food_order
        SET
        orderStatus = #{orderStatus},
        updateTime = #{updateTime},
        reason = #{reason}
        WHERE id = #{id} AND isDelete = 0
    </update>

    <update id="delete">
        UPDATE cm_farming_food_order
        SET isDelete = 1,updateTime = NOW()
        WHERE id = #{id}
    </update>

    <select id="countByOrderNo" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM cm_farming_food_order WHERE orderNo = #{orderNo} and isDelete = 0
    </select>
</mapper>