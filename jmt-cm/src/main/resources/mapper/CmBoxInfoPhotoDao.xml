<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.CmBoxInfoPhotoDao">

    <sql id="Base_Column_List">
        id,boxNo,shopNo,photoType,photoUrl,createTime,updateTime,isDelete
    </sql>


    <insert id="insert" parameterType="com.jmt.model.cm.entity.CmBoxInfoPhoto" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO cm_box_info_photo (
        boxNo, shopNo, photoType, photoUrl,createTime,updateTime,isDelete
        ) VALUES (
        #{boxNo}, #{shopNo}, #{photoType}, #{photoUrl}, #{createTime}, #{updateTime}, #{isDelete}
        )
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO cm_box_info_photo (
        boxNo, shopNo, photoType, photoUrl,createTime,updateTime,isDelete
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.boxNo}, #{item.shopNo}, #{item.photoType}, #{item.photoUrl},#{item.createTime}, #{item.updateTime}, #{item.isDelete}
            )
        </foreach>
    </insert>

    <update id="delete" parameterType="java.lang.Integer">
        UPDATE cm_box_info_photo SET isDelete = 1 WHERE id = #{id}
    </update>

    <update id="deleteByBoxNo" parameterType="java.lang.String">
        UPDATE cm_box_info_photo SET isDelete = 1 WHERE boxNo = #{boxNo}
    </update>

    <update id="update" parameterType="com.jmt.model.cm.entity.CmBoxInfoPhoto">
        UPDATE cm_box_info_photo
        <set>
        <if test="boxNo != null">boxNo = #{boxNo},</if>
        <if test="shopNo != null">shopNo = #{shopNo},</if>
        <if test="photoType != null">photoType = #{photoType},</if>
        <if test="photoUrl != null">photoUrl = #{photoUrl},</if>
        updateTime = NOW()
        </set>
        WHERE id = #{id} AND isDelete = 0
    </update>

    <select id="selectById" parameterType="java.lang.Long" resultType="com.jmt.model.cm.entity.CmBoxInfoPhoto">
        SELECT <include refid="Base_Column_List"/>
        FROM cm_box_info_photo WHERE id = #{id} AND isDelete = 0
    </select>

    <select id="selectByBoxNo" parameterType="java.lang.String" resultType="com.jmt.model.cm.entity.CmBoxInfoPhoto">
        SELECT <include refid="Base_Column_List"/>
        FROM cm_box_info_photo
        WHERE boxNo = #{boxNo} AND isDelete = 0
        ORDER BY createTime DESC
    </select>



</mapper>