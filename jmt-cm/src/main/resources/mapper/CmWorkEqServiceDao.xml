<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.CmWorkEqServiceDao">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, workNo, busNo, busName, linkman, telPhone, country, province, city, area, address,
        longitude, latitude, operatorNo, refereeNo, deployerNo, workStatus, reason,
        createTime, updateTime, isDelete,shopNo,shopName
    </sql>


    <!-- 部署工单(cm_work_eq_deploy)的列集合 -->
    <sql id="Base_Column_List_Deploy">
        id, workNo, busNo, busName,shopNo,shopName, linkman, telPhone, country, province, city, area, address, longitude,
        latitude, operatorNo, refereeNo, workStatus, reason, createTime, updateTime, isDelete,
        NULL AS eqTotalNum, NULL AS isProxy, NULL AS remark, applyWorkNo, deployerNo,
        'deploy' AS tableSource
    </sql>

    <!-- 撤回工单(cm_work_eq_recall)的列集合 -->
    <sql id="Base_Column_List_Recall">
        id, workNo, busNo, busName,shopNo,shopName, linkman, telPhone, country, province, city, area, address, longitude, latitude,
        operatorNo, refereeNo, workStatus, reason, createTime, updateTime, isDelete, NULL AS eqTotalNum,
        NULL AS isProxy, NULL AS remark, NULL AS applyWorkNo, deployerNo, 'recall' AS tableSource
    </sql>

    <!-- 维护工单(cm_work_eq_service)的列集合 -->
    <sql id="Base_Column_List_Service">
        id, workNo, busNo, busName,shopNo,shopName, linkman, telPhone, country, province, city, area, address, longitude, latitude,
        operatorNo, refereeNo, workStatus, reason, createTime, updateTime, isDelete, NULL AS eqTotalNum,
        NULL AS isProxy, NULL AS remark, NULL AS applyWorkNo, deployerNo, 'service' AS tableSource
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.jmt.model.cm.entity.CmWorkEqService" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO cm_work_eq_service (
        workNo, busNo, busName, linkman, telPhone, country, province, city, area, address,
        longitude, latitude, operatorNo, refereeNo, deployerNo, workStatus, reason,
        createTime, updateTime, isDelete,shopNo,shopName
        ) VALUES (
        #{workNo}, #{busNo}, #{busName}, #{linkman}, #{telPhone}, #{country}, #{province}, #{city}, #{area}, #{address},
        #{longitude}, #{latitude}, #{operatorNo}, #{refereeNo}, #{deployerNo}, #{workStatus}, #{reason},
        #{createTime}, #{updateTime}, #{isDelete},#{shopNo}, #{shopName}
        )
    </insert>



    <!-- 逻辑删除 -->
    <update id="delete" parameterType="java.lang.Long">
        UPDATE cm_work_eq_service
        SET isDelete = 1, updateTime = NOW()
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultType="com.jmt.model.cm.entity.CmWorkEqService">
        SELECT <include refid="Base_Column_List" />
        FROM cm_work_eq_service
        WHERE id = #{id,jdbcType=BIGINT} AND isDelete = 0
    </select>

    <!-- 更新记录 -->
    <update id="update" parameterType="com.jmt.model.cm.entity.CmWorkEqService">
        UPDATE cm_work_eq_service
        <set>
            <if test="workNo != null">workNo = #{workNo},</if>
            <if test="busNo != null">busNo = #{busNo},</if>
            <if test="busName != null">busName = #{busName},</if>
            <if test="shopNo != null">shopNo = #{shopNo},</if>
            <if test="shopName != null">shopName = #{shopName},</if>
            <if test="linkman != null">linkman = #{linkman},</if>
            <if test="telPhone != null">telPhone = #{telPhone},</if>
            <if test="country != null">country = #{country},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="area != null">area = #{area},</if>
            <if test="address != null">address = #{address},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="operatorNo != null">operatorNo = #{operatorNo},</if>
            <if test="refereeNo != null">refereeNo = #{refereeNo},</if>
            <if test="deployerNo != null">deployerNo = #{deployerNo},</if>
            <if test="workStatus != null">workStatus = #{workStatus},</if>
            <if test="reason != null">reason = #{reason},</if>
            updateTime = NOW()
        </set>
        WHERE id = #{id,jdbcType=BIGINT} AND isDelete = 0
    </update>

    <!-- 条件查询 -->
    <select id="selectByCondition" resultType="com.jmt.model.cm.dto.CmWorkEqServiceDTO">
        SELECT <include refid="Base_Column_List" />
        FROM cm_work_eq_service
        WHERE isDelete = 0
        AND operatorNo = #{operatorNo}
        <if test="workStatus != null">AND workStatus = #{workStatus,jdbcType=INTEGER}</if>
        <include refid="TimeScope_Condition"/>
        ORDER BY createTime DESC
    </select>
<!--    <select id="selectByCondition" parameterType="com.jmt.model.cm.dto.CmWorkEqServiceDTO" resultType="com.jmt.model.cm.dto.CmWorkEqServiceDTO">-->
<!--        SELECT <include refid="Base_Column_List" />-->
<!--        FROM cm_work_eq_service-->
<!--        <where>-->
<!--            isDelete = 0-->
<!--            <if test="id != null">AND id = #{id,jdbcType=BIGINT}</if>-->
<!--            <if test="workNo != null and workNo != ''">AND workNo = #{workNo,jdbcType=VARCHAR}</if>-->
<!--            <if test="busNo != null and busNo != ''">AND busNo = #{busNo,jdbcType=VARCHAR}</if>-->
<!--            <if test="busName != null and busName != ''">AND busName LIKE CONCAT('%', #{busName,jdbcType=VARCHAR}, '%')</if>-->
<!--            <if test="linkman != null and linkman != ''">AND linkman LIKE CONCAT('%', #{linkman,jdbcType=VARCHAR}, '%')</if>-->
<!--            <if test="telPhone != null and telPhone != ''">AND telPhone = #{telPhone,jdbcType=VARCHAR}</if>-->
<!--            <if test="country != null and country != ''">AND country = #{country,jdbcType=VARCHAR}</if>-->
<!--            <if test="province != null and province != ''">AND province = #{province,jdbcType=VARCHAR}</if>-->
<!--            <if test="city != null and city != ''">AND city = #{city,jdbcType=VARCHAR}</if>-->
<!--            <if test="area != null and area != ''">AND area = #{area,jdbcType=VARCHAR}</if>-->
<!--            <if test="address != null and address != ''">AND address LIKE CONCAT('%', #{address,jdbcType=VARCHAR}, '%')</if>-->
<!--            <if test="longitude != null and longitude != ''">AND longitude = #{longitude,jdbcType=VARCHAR}</if>-->
<!--            <if test="latitude != null and latitude != ''">AND latitude = #{latitude,jdbcType=VARCHAR}</if>-->
<!--            <if test="operatorNo != null and operatorNo != ''">AND operatorNo = #{operatorNo,jdbcType=VARCHAR}</if>-->
<!--            <if test="refereeNo != null and refereeNo != ''">AND refereeNo = #{refereeNo,jdbcType=VARCHAR}</if>-->
<!--            <if test="reason != null and reason != ''">AND reason LIKE CONCAT('%', #{reason,jdbcType=VARCHAR}, '%')</if>-->
<!--            <if test="deployerNo != null and deployerNo != ''">AND deployerNo = #{deployerNo,jdbcType=VARCHAR}</if>-->
<!--            <if test="workStatus != null">AND workStatus = #{workStatus,jdbcType=INTEGER}</if>-->
<!--        </where>-->
<!--        ORDER BY createTime-->
<!--    </select>-->
    <select id="getWorkEqServiceNum" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM cm_work_eq_service
        WHERE OperatorNo = #{operatorNo}
        AND isDelete = 0
    </select>



    <select id="selectAllWorkOrdersByStatus" parameterType="map" resultType="com.jmt.model.cm.vo.CmWorkOrderVO">
        (SELECT <include refid="Base_Column_List_Deploy"/>
        FROM cm_work_eq_deploy
        WHERE isDelete = 0
        AND operatorNo = #{operatorNo}
        <if test="workStatus != null">AND workStatus = #{workStatus,jdbcType=INTEGER}</if>
        <include refid="TimeScope_Condition"/>)

        UNION ALL

        (SELECT <include refid="Base_Column_List_Recall"/>
        FROM cm_work_eq_recall
        WHERE isDelete = 0
        AND operatorNo = #{operatorNo}
        <if test="workStatus != null">AND workStatus = #{workStatus,jdbcType=INTEGER}</if>
        <include refid="TimeScope_Condition"/>)

        UNION ALL

        (SELECT <include refid="Base_Column_List_Service"/>
        FROM cm_work_eq_service
        WHERE isDelete = 0
        AND operatorNo = #{operatorNo}
        <if test="workStatus != null">AND workStatus = #{workStatus,jdbcType=INTEGER}</if>
        <include refid="TimeScope_Condition"/>)

        ORDER BY createTime DESC
    </select>

    <!-- 时间范围条件SQL片段 -->
    <sql id="TimeScope_Condition">
        <choose>
            <when test="timeScope == 0"> <!-- 当日创建 -->
                AND DATE(createTime) = CURDATE()
            </when>
            <when test="timeScope == 1"> <!-- 昨日创建 -->
                AND DATE(createTime) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)
            </when>
            <when test="timeScope == 2"> <!-- 本月创建 -->
                AND YEAR(createTime) = YEAR(CURDATE())
                AND MONTH(createTime) = MONTH(CURDATE())
            </when>
            <when test="timeScope == 3"> <!-- 上月创建 -->
                AND YEAR(createTime) = YEAR(DATE_SUB(CURDATE(), INTERVAL 1 MONTH))
                AND MONTH(createTime) = MONTH(DATE_SUB(CURDATE(), INTERVAL 1 MONTH))
            </when>
            <when test="timeScope == 4"> <!-- 历史全部 -->
                <!-- 不添加时间条件 -->
            </when>
            <otherwise> <!-- 默认不添加时间条件 -->

            </otherwise>
        </choose>
    </sql>


</mapper>