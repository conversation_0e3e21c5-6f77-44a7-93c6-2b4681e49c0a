<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.CmCategoryDao">

    <sql id="Base_Column_List">
        id,parentId, categoryNo, categoryName, isNeedEq, isNeedTeam, isShareProfit, createTime, updateTime, isDelete
    </sql>

    <!-- 新增 -->
    <insert id="insert" parameterType="com.jmt.model.cm.entity.CmCategory" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO cm_category (
        parentId, categoryNo, categoryName,
        isNeedEq, isNeedTeam, isShareProfit,
        createTime, updateTime, isDelete
        ) VALUES (
        #{parentId}, #{categoryNo}, #{categoryName},
        #{isNeedEq}, #{isNeedTeam}, #{isShareProfit},
        #{createTime}, #{updateTime}, #{isDelete}
        )
    </insert>

    <!-- 根据ID删除 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        UPDATE cm_category
        SET isDelete = 1, updateTime = NOW()
        WHERE id = #{id} AND isDelete = 0
    </delete>

    <!-- 更新 -->
    <update id="update" parameterType="com.jmt.model.cm.entity.CmCategory">
        UPDATE cm_category
        <set>
            <if test="parentId != null">parentId = #{parentId,jdbcType=BIGINT},</if>
            <if test="categoryNo != null">categoryNo = #{categoryNo,jdbcType=VARCHAR},</if>
            <if test="categoryName != null">categoryName = #{categoryName,jdbcType=VARCHAR},</if>
            <if test="isNeedEq != null">isNeedEq = #{isNeedEq,jdbcType=INTEGER},</if>
            <if test="isNeedTeam != null">isNeedTeam = #{isNeedTeam,jdbcType=INTEGER},</if>
            <if test="isShareProfit != null">isShareProfit = #{isShareProfit,jdbcType=INTEGER},</if>
            <if test="updateTime != null">updateTime = #{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="isDelete != null">isDelete = #{isDelete,jdbcType=INTEGER},</if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT} AND isDelete = 0
    </update>



    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultType="com.jmt.model.cm.entity.CmCategory">
        SELECT <include refid="Base_Column_List"/>
        FROM cm_category WHERE id = #{id} AND isDelete = 0
    </select>

    <!-- 查询所有 -->
    <select id="selectAll" resultType="com.jmt.model.cm.entity.CmCategory">
        SELECT <include refid="Base_Column_List"/> FROM cm_category  WHERE isDelete = 0
    </select>

    <select id="selectNameById" parameterType="java.lang.Long" resultType="java.lang.String">
        SELECT categoryName FROM cm_category WHERE id = #{id} AND isDelete = 0
    </select>

</mapper>