<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.CmBoxInfoDao">

    <sql id="Base_Column_List">
        id,boxNo,shopNo,shopName,measure,capacity,busStar,cuisineType,comsumeAvg,servicePhone,shopPhoto,country,
        province,city,area,address,longitude,latiude,scope,createTime,updateTime,isDelete
    </sql>


    <insert id="insert" parameterType="com.jmt.model.cm.entity.CmBoxInfo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO cm_box_info (
        boxNo,shopNo,shopName,measure,capacity,busStar,cuisineType,comsumeAvg,servicePhone,shopPhoto,country,
        province,city,area,address,longitude,latiude,scope,createTime,updateTime,isDelete
        ) VALUES (
        #{boxNo}, #{shopNo}, #{shopName}, #{measure}, #{capacity}, #{busStar},
        #{cuisineType}, #{comsumeAvg}, #{servicePhone}, #{shopPhoto},
        #{country}, #{province}, #{city}, #{area}, #{address}, #{longitude}, #{latiude},#{scope}, #{createTime}, #{updateTime}, #{isDelete}
        )
    </insert>



    <update id="delete" parameterType="java.lang.Long">
        UPDATE cm_box_info SET isDelete = 1 WHERE id = #{id}
    </update>

    <update id="updateById" parameterType="com.jmt.model.cm.entity.CmBoxInfo">
        UPDATE cm_box_info
        <set>
            <if test="boxNo != null">boxNo = #{boxNo},</if>
            <if test="shopNo != null">shopNo = #{shopNo},</if>
            <if test="shopName != null">shopName = #{shopName},</if>
            <if test="country != null">country = #{country},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="area != null">area = #{area},</if>
            <if test="address != null">address = #{address},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latiude != null">latiude = #{latiude},</if>
            <if test="measure != null">measure = #{measure},</if>
            <if test="capacity != null">capacity = #{capacity},</if>
            <if test="busStar != null">busStar = #{busStar},</if>
            <if test="cuisineType != null">cuisineType = #{cuisineType},</if>
            <if test="comsumeAvg != null">comsumeAvg = #{comsumeAvg},</if>
            <if test="servicePhone != null">servicePhone = #{servicePhone},</if>
            <if test="shopPhoto != null">shopPhoto = #{shopPhoto},</if>
            <if test="scope != null">scope = #{scope},</if>
            updateTime = NOW()
        </set>
        WHERE id = #{id} AND isDelete = 0
    </update>

    <select id="selectById" parameterType="java.lang.Long" resultType="com.jmt.model.cm.entity.CmBoxInfo">
        SELECT <include refid="Base_Column_List"/>
        FROM cm_box_info WHERE id = #{id} AND isDelete = 0
    </select>

    <select id="selectByBoxNo" parameterType="java.lang.String" resultType="com.jmt.model.cm.entity.CmBoxInfo">
        SELECT <include refid="Base_Column_List"/>
        FROM cm_box_info WHERE boxNo = #{boxNo} AND isDelete = 0
    </select>

    <select id="selectAll" resultType="com.jmt.model.cm.entity.CmBoxInfo">
        SELECT <include refid="Base_Column_List"/>
        FROM cm_box_info WHERE isDelete = 0 ORDER BY createTime DESC
    </select>


    <select id="selectByCondition" parameterType="com.jmt.model.cm.entity.CmBoxInfo" resultType="com.jmt.model.cm.vo.CmBoxInfoVO">
        SELECT
        b.id, b.boxNo, b.shopName, b.measure, b.capacity, b.busStar, b.cuisineType, b.comsumeAvg, b.servicePhone, b.scope,
        (SELECT p.photoUrl
        FROM cm_box_info_photo p
        WHERE p.boxNo = b.boxNo
        AND p.photoType = 1
        AND p.isDelete = 0
        ORDER BY p.createTime
        LIMIT 1) as boxPhoto
        FROM cm_box_info b
        WHERE b.isDelete = 0
        <if test="scope != null">
            AND b.scope = #{scope}
        </if>
        <if test="shopNo != null">
            AND b.shopNo = #{shopNo}
        </if>
        <if test="cuisineType != null and cuisineType != ''">
            AND b.cuisineType = #{cuisineType}
        </if>
        ORDER BY b.createTime DESC
    </select>





</mapper>