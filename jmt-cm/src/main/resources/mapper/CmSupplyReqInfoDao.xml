<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.CmSupplyReqInfoDao">

    <sql id="Base_Column_List">
        id,userId, busNo, busName, linkman, telPhone, country, province, city, area, address,
        longitude, latitude, title, content, picture, video, scope, infoType,visitCount, contactCount, beginTime, endTime, auditStatus,
        reason, createTime, updateTime, isDelete,messageCount,dealCount
    </sql>


    <!-- Insert -->
    <insert id="insert" parameterType="com.jmt.model.cm.entity.CmSupplyReqInfo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO cm_supply_req_info (
        userId,busNo, busName, linkman, telPhone, country, province, city, area, address,
        longitude, latitude, title, content, picture, video, scope, infoType,visitCount, contactCount, beginTime, endTime,
        auditStatus, reason, createTime, updateTime, isDelete,messageCount,dealCount
        ) VALUES (
        #{userId},#{busNo}, #{busName}, #{linkman}, #{telPhone}, #{country}, #{province}, #{city}, #{area}, #{address},
        #{longitude}, #{latitude}, #{title}, #{content}, #{picture}, #{video}, #{scope}, #{infoType},#{visitCount}, #{contactCount}, #{beginTime}, #{endTime},
        #{auditStatus}, #{reason}, #{createTime}, #{updateTime}, #{isDelete}, #{messageCount}, #{dealCount}
        )
    </insert>

    <!-- Batch Insert -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO cm_supply_req_info (
        userId,busNo, busName, linkman, telPhone, country, province, city, area, address,
        longitude, latitude, title, content, picture, video, scope, infoType,visitCount, contactCount, beginTime, endTime,
        auditStatus, reason, createTime, updateTime, isDelete,messageCount,dealCount
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.userId}, #{item.busNo}, #{item.busName}, #{item.linkman}, #{item.telPhone}, #{item.country}, #{item.province},
            #{item.city}, #{item.area}, #{item.address}, #{item.longitude}, #{item.latitude}, #{item.title},
            #{item.content}, #{item.picture}, #{item.video}, #{item.scope}, #{item.infoType},#{item.visitCount}, #{item.contactCount}, #{item.beginTime}, #{item.endTime},
            #{item.auditStatus}, #{item.reason}, #{item.createTime}, #{item.updateTime}, #{item.isDelete}, #{item.messageCount}, #{item.dealCount}
            )
        </foreach>
    </insert>

    <!-- Update -->
    <update id="update" parameterType="com.jmt.model.cm.entity.CmSupplyReqInfo">
        UPDATE cm_supply_req_info
        <set>
            <if test="userId != null">userId = #{userId},</if>
            <if test="busNo != null">busNo = #{busNo},</if>
            <if test="busName != null">busName = #{busName},</if>
            <if test="linkman != null">linkman = #{linkman},</if>
            <if test="telPhone != null">telPhone = #{telPhone},</if>
            <if test="country != null">country = #{country},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="area != null">area = #{area},</if>
            <if test="address != null">address = #{address},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="title != null">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="picture != null">picture = #{picture},</if>
            <if test="video != null">video = #{video},</if>
            <if test="scope != null">scope = #{scope},</if>
            <if test="infoType != null">infoType = #{infoType},</if>
            <if test="visitCount != null">visitCount = #{visitCount},</if>
            <if test="contactCount != null">contactCount = #{contactCount},</if>
            <if test="beginTime != null">beginTime = #{beginTime},</if>
            <if test="endTime != null">endTime = #{endTime},</if>
            <if test="auditStatus != null">auditStatus = #{auditStatus},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="updateTime != null">updateTime = #{updateTime},</if>
            <if test="isDelete != null">isDelete = #{isDelete},</if>
            <if test="messageCount != null">messageCount = #{messageCount},</if>
            <if test="dealCount != null">dealCount = #{dealCount},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- Update Audit Status -->
    <update id="updateAuditStatus">
        UPDATE cm_supply_req_info
        SET auditStatus = #{auditStatus},
        reason = #{reason},
        updateTime = #{updateTime}
        WHERE id = #{id}
    </update>

    <!-- Logical Delete -->
    <update id="delete">
        UPDATE cm_supply_req_info
        SET isDelete = 1,updateTime = NOW()
        WHERE id = #{id}
    </update>



    <select id="selectById" parameterType="java.lang.Long" resultType="com.jmt.model.cm.entity.CmSupplyReqInfo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM cm_supply_req_info
        WHERE id = #{id} AND isDelete = 0
    </select>

    <select id="selectByCondition" parameterType="com.jmt.model.cm.dto.CmSupplyReqInfoDTO" resultType="com.jmt.model.cm.dto.CmSupplyReqInfoDTO">
        SELECT <include refid="Base_Column_List"/>
        FROM cm_supply_req_info
        <where>
            isDelete = 0
            <if test="userId != null and userId != ''">AND userId = #{userId}</if>
            <if test="busNo != null and busNo != ''">AND busNo LIKE CONCAT('%', #{busNo}, '%')</if>
            <if test="busName != null and busName != ''">AND busName LIKE CONCAT('%', #{busName}, '%')</if>
            <if test="linkman != null and linkman != ''">AND linkman LIKE CONCAT('%', #{linkman}, '%')</if>
            <if test="telPhone != null and telPhone != ''">AND telPhone = #{telPhone}</if>
            <if test="country != null and country != ''">AND country = #{country}</if>
            <if test="province != null and province != ''">AND province = #{province}</if>
            <if test="city != null and city != ''">AND city = #{city}</if>
            <if test="area != null and area != ''">AND area = #{area}</if>
            <if test="address != null and address != ''">AND address LIKE CONCAT('%', #{address}, '%')</if>
            <if test="longitude != null and longitude != ''">AND longitude = #{longitude,jdbcType=VARCHAR}</if>
            <if test="latitude != null and latitude != ''">AND latitude = #{latitude,jdbcType=VARCHAR}</if>
            <if test="title != null and title != ''">AND title LIKE CONCAT('%', #{title}, '%')</if>
            <if test="scope != null">AND scope = #{scope}</if>
            <if test="infoType != null">AND infoType = #{infoType}</if>
            <!-- ：1=当天，2=一周内，3=一个月内，4=三个月内） -->
            <if test="timeScope != null">
                <choose>
                    <when test="timeScope == 1">
                        AND createTime &gt;= DATE_FORMAT(CURDATE(), '%Y-%m-%d 00:00:00')
                        AND createTime &lt;= DATE_FORMAT(CURDATE(), '%Y-%m-%d 23:59:59')
                    </when>
                    <!-- 一周内 -->
                    <when test="timeScope == 2">
                        AND createTime &gt;= DATE_SUB(NOW(), INTERVAL 7 DAY)
                        AND createTime &lt;= NOW()
                    </when>
                    <!-- 一个月内 -->
                    <when test="timeScope == 3">
                        AND createTime &gt;= DATE_SUB(NOW(), INTERVAL 1 MONTH)
                        AND createTime &lt;= NOW()
                    </when>
                    <!-- 三个月内 -->
                    <when test="timeScope == 4">
                        AND createTime &gt;= DATE_SUB(NOW(), INTERVAL 3 MONTH)
                        AND createTime &lt;= NOW()
                    </when>
                </choose>
            </if>
        </where>
        ORDER BY createTime DESC
    </select>

    <select id="selectDtoById" parameterType="java.lang.Long" resultType="com.jmt.model.cm.dto.CmSupplyReqInfoDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM cm_supply_req_info
        WHERE id = #{id} AND isDelete = 0
    </select>

    <select id="selectListByUserId" parameterType="java.lang.Long" resultType="com.jmt.model.cm.dto.CmSupplyReqInfoDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM cm_supply_req_info
        WHERE userId = #{userId} AND isDelete = 0
    </select>

<!--    查首页供应最新两条数据-->
    <select id="selectIndexSupplyPage"  resultType="com.jmt.model.cm.dto.CmIndexSupplyDTO">
        SELECT id,title,infoType
        FROM cm_supply_req_info
        WHERE infoType = 1 AND isDelete = 0 ORDER BY createTime DESC
        LIMIT 2;
    </select>

    <!--    查首页需求最新两条数据-->
    <select id="selectIndexNeedPage"  resultType="com.jmt.model.cm.dto.CmIndexSupplyDTO">
        SELECT id,title,infoType
        FROM cm_supply_req_info
        WHERE infoType = 2 AND isDelete = 0 ORDER BY createTime DESC
        LIMIT 2;
    </select>

    <update id="incrementVisitCount" parameterType="java.lang.Long">
        UPDATE cm_supply_req_info
        SET visitCount = visitCount + 1,
        updateTime = NOW()
        WHERE id = #{id} AND isDelete = 0
    </update>


    <select id="selectAllInfo" resultType="com.jmt.model.cm.vo.CmInfoVo">
        SELECT * FROM (
        (SELECT
        id, title, createTime,
        IFNULL(visitCount, 0) as visitCount,
        IFNULL(messageCount, 0) as messageCount,
        IFNULL(dealCount, 0) as dealCount,
        'supply' as sourceType
        FROM cm_supply_req_info
        WHERE isDelete = 0
        <if test="userId != null">
            AND userId = #{userId}
        </if>)

        UNION ALL

        (SELECT
        id, title, createTime,
        IFNULL(visitCount, 0) as visitCount,
        IFNULL(messageCount, 0) as messageCount,
        IFNULL(dealCount, 0) as dealCount,
        'farming' as sourceType
        FROM cm_farming_food_info
        WHERE isDelete = 0
        <if test="userId != null">
            AND userId = #{userId}
        </if>)

        UNION ALL

        (SELECT
        id, title, createTime,
        IFNULL(visitCount, 0) as visitCount,
        IFNULL(messageCount, 0) as messageCount,
        IFNULL(dealCount, 0) as dealCount,
        'sellout' as sourceType
        FROM cm_sellout_info
        WHERE isDelete = 0
        <if test="userId != null">
            AND userId = #{userId}
        </if>)
        ) AS combined_result
        WHERE 1=1
        <if test="sourceType != null and sourceType != ''">
            AND sourceType = #{sourceType}
        </if>
        ORDER BY createTime DESC
    </select>





</mapper>