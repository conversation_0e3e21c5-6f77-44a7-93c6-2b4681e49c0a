<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.CmBusinessInfoDao">


    <sql id="Base_Column_List">
        id, userId, busNo, busName, linkman, telPhone, country, province, city, area,
        address, longitude, latitude, operatorNo, refereeNo, devloperNo, busPhoto,
        busLicense, identityCardFront, identityCardBack, categoryId, measure,
        ydeskNum, fdeskNum, comsumeAvg, openTime, visitorAvg, busStar, remark,
        auditStatus, reason, createTime, updateTime, isDelete,creditCode,legalPerson
    </sql>

    <!-- 插入商家信息 -->
    <insert id="insert" parameterType="com.jmt.model.cm.entity.CmBusinessInfo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO cm_business_info (
        userId, busNo, busName, linkman, telPhone, country, province, city, area,
        address, longitude, latitude, operatorNo, refereeNo, devloperNo, busPhoto,
        busLicense, identityCardFront, identityCardBack, categoryId, measure,
        ydeskNum, fdeskNum, comsumeAvg, openTime, visitorAvg, busStar, remark,
        auditStatus, reason, createTime, updateTime, isDelete,creditCode,legalPerson
        ) VALUES (
        #{userId}, #{busNo}, #{busName}, #{linkman}, #{telPhone}, #{country}, #{province}, #{city}, #{area},
        #{address}, #{longitude}, #{latitude}, #{operatorNo}, #{refereeNo}, #{devloperNo}, #{busPhoto},
        #{busLicense}, #{identityCardFront}, #{identityCardBack}, #{categoryId}, #{measure},
        #{ydeskNum}, #{fdeskNum}, #{comsumeAvg}, #{openTime}, #{visitorAvg}, #{busStar}, #{remark},
        #{auditStatus}, #{reason}, #{createTime}, #{updateTime}, #{isDelete}, #{creditCode}, #{legalPerson}
        )
    </insert>

    <!-- 更新商家信息 -->
    <update id="updateById" parameterType="com.jmt.model.cm.entity.CmBusinessInfo">
        UPDATE cm_business_info
        <set>
            <if test="userId != null">userId = #{userId},</if>
            <if test="busNo != null">busNo = #{busNo},</if>
            <if test="busName != null">busName = #{busName},</if>
            <if test="linkman != null">linkman = #{linkman},</if>
            <if test="telPhone != null">telPhone = #{telPhone},</if>
            <if test="country != null">country = #{country},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="area != null">area = #{area},</if>
            <if test="address != null">address = #{address},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="operatorNo != null">operatorNo = #{operatorNo},</if>
            <if test="refereeNo != null">refereeNo = #{refereeNo},</if>
            <if test="devloperNo != null">devloperNo = #{devloperNo},</if>
            <if test="busPhoto != null">busPhoto = #{busPhoto},</if>
            <if test="busLicense != null">busLicense = #{busLicense},</if>
            <if test="identityCardFront != null">identityCardFront = #{identityCardFront},</if>
            <if test="identityCardBack != null">identityCardBack = #{identityCardBack},</if>
            <if test="categoryId != null">categoryId = #{categoryId},</if>
            <if test="measure != null">measure = #{measure},</if>
            <if test="ydeskNum != null">ydeskNum = #{ydeskNum},</if>
            <if test="fdeskNum != null">fdeskNum = #{fdeskNum},</if>
            <if test="comsumeAvg != null">comsumeAvg = #{comsumeAvg},</if>
            <if test="openTime != null">openTime = #{openTime},</if>
            <if test="visitorAvg != null">visitorAvg = #{visitorAvg},</if>
            <if test="busStar != null">busStar = #{busStar},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="auditStatus != null">auditStatus = #{auditStatus},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="isDelete != null">isDelete = #{isDelete},</if>
            <if test="creditCode != null">creditCode = #{creditCode},</if>
            <if test="legalPerson != null">legalPerson = #{legalPerson},</if>
            updateTime = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <update id="delete">
        UPDATE cm_business_info
        SET isDelete = 1, updateTime = NOW()
        WHERE id = #{id}
    </update>

    <!-- 根据ID查询商家信息 -->
    <select id="selectById" resultType="com.jmt.model.cm.entity.CmBusinessInfo">
        SELECT <include refid="Base_Column_List"/>
        FROM cm_business_info
        WHERE id = #{id} AND isDelete = 0
    </select>

    <select id="selectByUserId" resultType="com.jmt.model.cm.entity.CmBusinessInfo">
        SELECT <include refid="Base_Column_List"/>
        FROM cm_business_info
        WHERE userId = #{userId} AND isDelete = 0
    </select>


    <select id="selectBusNameByUserId" resultType="java.lang.String">
        SELECT busName FROM cm_business_info
        WHERE userId = #{userId} AND isDelete = 0
    </select>


    <!-- 查询所有商家信息 -->
<!--    <select id="selectAll" resultMap="BaseResultMap">-->
<!--        SELECT <include refid="Base_Column_List"/>-->
<!--        FROM cm_business_info-->
<!--        WHERE is_delete = 0-->
<!--    </select>-->

    <select id="selectByCondition" parameterType="com.jmt.model.cm.dto.BusinessInfoQueryDTO" resultType="com.jmt.model.cm.vo.CmBusinessInfoVO">
        SELECT id,busNo,busName,province,city,area,refereeNo,busPhoto,busStar,auditStatus, createTime
        FROM cm_business_info
        WHERE isDelete = 0
        <if test="auditStatus != null">AND auditStatus = #{auditStatus,jdbcType=INTEGER}</if>
        <if test="province != null and province != ''">AND province = #{province,jdbcType=VARCHAR}</if>
        <if test="city != null and city != ''">AND city = #{city,jdbcType=VARCHAR}</if>
        <if test="area != null and area != ''">AND area = #{area,jdbcType=VARCHAR}</if>
        <if test="operatorNo != null and operatorNo != ''">AND operatorNo = #{operatorNo,jdbcType=VARCHAR}</if>
        <include refid="TimeScope_Condition"/>
        ORDER BY createTime DESC
    </select>
    <!-- 条件查询商家信息 -->
<!--    <select id="selectByCondition" parameterType="com.jmt.model.cm.entity.CmBusinessInfo" resultType="com.jmt.model.cm.entity.CmBusinessInfo">-->
<!--        SELECT <include refid="Base_Column_List"/>-->
<!--        FROM cm_business_info-->
<!--        <where>-->
<!--            isDelete = 0-->
<!--            <if test="userId != null">AND userId = #{userId}</if>-->
<!--            <if test="busNo != null and busNo != ''">AND busNo LIKE CONCAT('%', #{busNo}, '%')</if>-->
<!--            <if test="busName != null and busName != ''">AND busName LIKE CONCAT('%', #{busName}, '%')</if>-->
<!--            <if test="linkman != null and linkman != ''">AND linkman LIKE CONCAT('%', #{linkman}, '%')</if>-->
<!--            <if test="telPhone != null and telPhone != ''">AND telPhone = #{telPhone}</if>-->
<!--            <if test="country != null and country != ''">AND country = #{country}</if>-->
<!--            <if test="province != null and province != ''">AND province = #{province}</if>-->
<!--            <if test="city != null and city != ''">AND city = #{city}</if>-->
<!--            <if test="area != null and area != ''">AND area = #{area}</if>-->
<!--            <if test="address != null and address != ''">AND address LIKE CONCAT('%', #{address}, '%')</if>-->
<!--            <if test="operatorNo != null and operatorNo != ''">AND operatorNo = #{operatorNo}</if>-->
<!--            <if test="refereeNo != null and refereeNo != ''">AND refereeNo = #{refereeNo}</if>-->
<!--            <if test="devloperNo != null and devloperNo != ''">AND devloperNo = #{devloperNo}</if>-->
<!--            <if test="categoryId != null">AND categoryId = #{categoryId}</if>-->
<!--            <if test="auditStatus != null">AND auditStatus = #{auditStatus}</if>-->
<!--        </where>-->
<!--        ORDER BY createTime-->
<!--    </select>-->
    <select id="selectByBusinessNo" resultType="com.jmt.model.cm.entity.CmBusinessInfo">
        SELECT <include refid="Base_Column_List"/>
        FROM cm_business_info
        WHERE busNo = #{businessNo} AND isDelete = 0
    </select>
    <select id="getNumberByProvince" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM cm_business_info
        WHERE province = #{province}
        AND isDelete = 0
    </select>
    <select id="getBusinessInfoByProvince" resultType="com.jmt.model.cm.entity.CmBusinessInfo">
        SELECT <include refid="Base_Column_List"/>
        FROM cm_business_info
        WHERE province = #{province}
        AND isDelete = 0
    </select>
    <select id="getUnAuditBusinessNum" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM cm_business_info
        WHERE
        OperatorNo = #{operatorNo}
        AND auditStatus = 0
        AND isDelete = 0
    </select>
    <select id="getAuditBusinessNum" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM cm_business_info
        WHERE OperatorNo = #{operatorNo}
        AND auditStatus = 1
        AND isDelete = 0
    </select>
    <select id="getBusinessNum" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM cm_business_info
        WHERE OperatorNo = #{operatorNo}
        AND isDelete = 0
    </select>


    <select id="selectBusInfoByUserId" parameterType="java.lang.Long" resultType="com.jmt.model.cm.vo.PubBusInfoVO">
        SELECT  userId, busName,busNo,telPhone,busStar, country, province, city, area, address,busPhoto,categoryId
        FROM cm_business_info
        WHERE userId = #{userId}
        AND isDelete = 0
    </select>


    <select id="countByBusNo" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM cm_business_info WHERE busNo = #{busNo} and isDelete = 0
    </select>
    <select id="getBusinessList" resultType="com.jmt.model.cm.entity.CmBusinessInfo">
        SELECT <include refid="Base_Column_List"/>
        FROM cm_business_info
        <where>
            <if test="cmBusinessMange != null and cmBusinessMange.address!=null">
                AND address LIKE CONCAT('%',#{cmBusinessMange.address},'%')
            </if>
            <if test="cmBusinessMange != null and cmBusinessMange.operatorNo!=null">
                AND operatorNo = #{cmBusinessMange.operatorNo}
            </if>
            <if test="cmBusinessMange != null and cmBusinessMange.createTime!=null">
                AND createTime > #{cmBusinessMange.createTime}
            </if>
            <if test="true">
                AND isDelete = 0
            </if>
        </where>
    </select>
    <select id="getAreaDropdown" resultType="java.lang.String">
        SELECT DISTINCT address FROM cm_business_info
        WHERE operatorNo = #{operatorNo}
        AND isDelete = 0
        ORDER BY area
    </select>
    <select id="getTimeDropdown" resultType="java.time.LocalDate">
        SELECT DISTINCT createTime FROM cm_business_info
        WHERE operatorNo = #{operatorNo}
        AND isDelete = 0
        ORDER BY createTime
    </select>

<!--    聚合伙 商家列表条件选择-->
    <sql id="TimeScope_Condition">
        <choose>
            <when test="timeScope == 0"> <!-- 当日创建 -->
                AND DATE(createTime) = CURDATE()
            </when>
            <when test="timeScope == 1"> <!-- 昨日创建 -->
                AND DATE(createTime) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)
            </when>
            <when test="timeScope == 2"> <!-- 本月创建 -->
                AND YEAR(createTime) = YEAR(CURDATE())
                AND MONTH(createTime) = MONTH(CURDATE())
            </when>
            <when test="timeScope == 3"> <!-- 上月创建 -->
                AND YEAR(createTime) = YEAR(DATE_SUB(CURDATE(), INTERVAL 1 MONTH))
                AND MONTH(createTime) = MONTH(DATE_SUB(CURDATE(), INTERVAL 1 MONTH))
            </when>
            <when test="timeScope == 4"> <!-- 历史全部 -->
                <!-- 不添加时间条件 -->
            </when>
            <otherwise> <!-- 默认不添加时间条件 -->

            </otherwise>
        </choose>
    </sql>

    <select id="getUserIdByCreditCode" parameterType="java.lang.String" resultType="java.lang.Long">
        SELECT userId
        FROM cm_business_info
        WHERE creditCode = #{creditCode}
        AND isDelete = 0
        LIMIT 1
    </select>

</mapper>