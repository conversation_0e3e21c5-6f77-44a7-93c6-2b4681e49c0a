package com.jmt.dao;

import com.jmt.model.cm.entity.CmUser;
import com.jmt.model.cm.vo.CmUserVO;
import com.jmt.model.jhh.dto.TransferSalesmanEqDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface CmUserDao {

    CmUser selectById(@Param("id") Long id);

    Integer insert(CmUser user);

    Integer updateByUaaId(CmUser user);
    Integer updateById(CmUser user);

    Integer deleteById(@Param("id") Long id);
    CmUser selectByUaaId(@Param("uaaId") Long uaaId);
    /**
     * 获得游客列表
     * @param operatorNo
     * @return
     */
    List<CmUser> selectByOperatorNo(String operatorNo);
    List<CmUserVO> selectVoByOperatorNo(String operatorNo);


    Integer getUserNum(String operatorNo);

    String getUserAddress(Long uaaId);

    CmUser selectByPhone(@Param("telPhone")String telPhone);

    Integer transferSalesman(TransferSalesmanEqDto transferSalesmanEqDto);
}
