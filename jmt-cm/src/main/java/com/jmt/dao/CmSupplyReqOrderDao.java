package com.jmt.dao;

import com.jmt.model.cm.entity.CmSupplyReqOrder;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface CmSupplyReqOrderDao {
    Integer insert(CmSupplyReqOrder record);
    Integer update(CmSupplyReqOrder record);

    Integer delete(@Param("id") Long id);
    CmSupplyReqOrder selectById(Long id);

    Integer countByOrderNo(String orderNo);
}
