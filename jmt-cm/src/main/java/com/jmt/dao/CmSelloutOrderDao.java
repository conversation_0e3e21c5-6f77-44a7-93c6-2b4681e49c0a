package com.jmt.dao;

import com.jmt.model.cm.entity.CmSelloutOrder;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface CmSelloutOrderDao {
    Integer insert(CmSelloutOrder record);
    Integer update(CmSelloutOrder record);

    Integer delete(@Param("id") Long id);
    CmSelloutOrder selectById(Long id);


    /**
     * 统计相同订单编号的数量
     * @param orderNo 订单编号
     * @return 数量
     */
    Integer countByOrderNo(String orderNo);
}
