package com.jmt.dao;

import com.jmt.model.cm.entity.CmFarmingFoodAudit;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
@Repository
public interface CmFarmingFoodAuditDao {
    Integer insert(CmFarmingFoodAudit record);


    Integer update(CmFarmingFoodAudit record);

    Integer logicalDeleteById(@Param("id") Long id);


    CmFarmingFoodAudit selectById(Long id);


    CmFarmingFoodAudit selectByInfoId(Long infoId);

    List<CmFarmingFoodAudit> selectByAuditStatus(Integer auditStatus);
}
