package com.jmt.dao;

import com.jmt.model.cm.dto.CmBusinessMemberDTO;
import com.jmt.model.cm.entity.CmBusinessMember;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CmBusinessMemberDao {
    Integer insert(CmBusinessMember record);
    CmBusinessMember selectById(Long id);
    Integer delete(Long id);
    Integer update(CmBusinessMember member);
    List<CmBusinessMember> selectAll();
    List<CmBusinessMember> selectByBusNo(String busNo);

    List<CmBusinessMemberDTO> selectByCondition(CmBusinessMemberDTO demoDto);
}
