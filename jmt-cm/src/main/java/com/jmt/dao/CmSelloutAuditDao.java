package com.jmt.dao;

import com.jmt.model.cm.entity.CmSelloutAudit;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CmSelloutAuditDao {
    Integer insert(CmSelloutAudit record);


    Integer update(CmSelloutAudit record);

    Integer logicalDeleteById(@Param("id") Long id);


    CmSelloutAudit selectById(Long id);


    CmSelloutAudit selectByInfoId(Long infoId);

    List<CmSelloutAudit> selectByAuditStatus(Integer auditStatus);
}
