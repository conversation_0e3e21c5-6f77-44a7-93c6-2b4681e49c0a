package com.jmt.dao;

import com.jmt.model.cm.dto.CmSupplyReqInfoDTO;
import com.jmt.model.cm.dto.CmIndexSupplyDTO;
import com.jmt.model.cm.entity.CmSupplyReqInfo;
import com.jmt.model.cm.vo.CmInfoVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CmSupplyReqInfoDao {
    Long insert(CmSupplyReqInfo record);

    Integer batchInsert(List<CmSupplyReqInfo> list);

    Integer update(CmSupplyReqInfo record);


    Integer delete(@Param("id") Long id);


    CmSupplyReqInfo selectById(Long id);


    List<CmSupplyReqInfoDTO> selectByCondition(CmSupplyReqInfoDTO condition);

    CmSupplyReqInfoDTO selectDtoById(Long id);

    List<CmSupplyReqInfoDTO> selectListByUserId(Long id);

    int incrementVisitCount(Long id);

    // 添加sourceType参数
    List<CmInfoVo> selectAllInfo(@Param("userId") Long userId,
                                 @Param("sourceType") String sourceType);

    List<CmIndexSupplyDTO> selectIndexSupplyPage();
    List<CmIndexSupplyDTO> selectIndexNeedPage();
}
