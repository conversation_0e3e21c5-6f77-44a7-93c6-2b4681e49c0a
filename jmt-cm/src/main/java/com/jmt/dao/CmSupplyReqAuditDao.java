package com.jmt.dao;

import com.jmt.model.cm.entity.CmSupplyReqAudit;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CmSupplyReqAuditDao {
    Integer insert(CmSupplyReqAudit record);


    Integer update(CmSupplyReqAudit record);

    Integer logicalDeleteById(@Param("id") Long id);


    CmSupplyReqAudit selectById(Long id);


    CmSupplyReqAudit selectByInfoId(Long infoId);

    List<CmSupplyReqAudit> selectByAuditStatus(Integer auditStatus);


}
