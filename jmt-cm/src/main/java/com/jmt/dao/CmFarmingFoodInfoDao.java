package com.jmt.dao;

import com.jmt.model.cm.dto.CmFarmingFoodInfoDTO;
import com.jmt.model.cm.entity.CmFarmingFoodInfo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CmFarmingFoodInfoDao {
    Long insert(CmFarmingFoodInfo record);

    Integer batchInsert(List<CmFarmingFoodInfo> list);

    Integer update(CmFarmingFoodInfo record);


    Integer delete(@Param("id") Long id);


    CmFarmingFoodInfo selectById(Long id);


    List<CmFarmingFoodInfoDTO> selectByCondition(CmFarmingFoodInfoDTO condition);

    CmFarmingFoodInfoDTO selectDtoById(Long id);


    List<CmFarmingFoodInfoDTO> selectListByUserId(Long id);

    int incrementVisitCount(Long id);
}
