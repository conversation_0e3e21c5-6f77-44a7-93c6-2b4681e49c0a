package com.jmt.dao;

import com.jmt.model.cm.entity.CmBoxInfoDetail;

import java.util.List;

public interface CmBoxInfoDetailDao {
    int insert(CmBoxInfoDetail record);
    int batchInsert(List<CmBoxInfoDetail> records);

    int delete(Long id);

    int deleteByBoxNo(String boxNo);
    int updateById(CmBoxInfoDetail record);

    CmBoxInfoDetail selectById(Long id);
    List<CmBoxInfoDetail> selectByBoxNo(String boxNo);

    // 添加批量查询方法
    List<CmBoxInfoDetail> selectByBoxNos(List<String> boxNos);


}
