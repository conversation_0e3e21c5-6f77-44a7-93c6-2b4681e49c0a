package com.jmt.dao;

import com.jmt.model.cm.entity.CmCategory;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CmCategoryDao {
    Integer insert(CmCategory cmCategory);
    Integer deleteById(Long id);
    Integer update(CmCategory cmCategory);
    CmCategory selectById(Long id);
    List<CmCategory> selectAll();

    String selectNameById(Long id);
}
