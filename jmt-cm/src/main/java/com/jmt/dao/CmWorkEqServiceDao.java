package com.jmt.dao;

import com.jmt.model.cm.dto.CmWorkEqServiceDTO;
import com.jmt.model.cm.entity.CmWorkEqService;
import com.jmt.model.cm.vo.CmWorkOrderVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
@Repository
public interface CmWorkEqServiceDao {
    Integer insert(CmWorkEqService record);
    Integer update(CmWorkEqService record);

    Integer delete(Long id);

    CmWorkEqService selectById(@Param("id") Long id);

//    List<CmWorkEqServiceDTO> selectByCondition(CmWorkEqServiceDTO cmWorkEqServiceDTO);

    List<CmWorkEqServiceDTO> selectByCondition(@Param("workStatus") Integer workStatus,
                                               @Param("timeScope") Integer timeScope,
                                               @Param("operatorNo")String operatorNo);

    Integer getWorkEqServiceNum(String operatorNo);


    /**
     * 根据工单状态分页查询所有表的工单
     */
    List<CmWorkOrderVO> selectAllWorkOrdersByStatus(@Param("workStatus") Integer workStatus,
                                                    @Param("timeScope") Integer timeScope,
                                                    @Param("operatorNo")String operatorNo);
}
