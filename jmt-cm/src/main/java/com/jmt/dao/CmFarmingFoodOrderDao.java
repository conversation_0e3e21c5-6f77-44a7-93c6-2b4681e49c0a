package com.jmt.dao;

import com.jmt.model.cm.entity.CmFarmingFoodOrder;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface CmFarmingFoodOrderDao {
    Integer insert(CmFarmingFoodOrder record);
    Integer update(CmFarmingFoodOrder record);

    Integer delete(@Param("id") Long id);
    CmFarmingFoodOrder selectById(Long id);

    CmFarmingFoodOrder selectByOrderNo(String orderNo);

    Integer countByOrderNo(String orderNo);
}
