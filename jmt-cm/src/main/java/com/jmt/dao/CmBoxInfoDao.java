package com.jmt.dao;

import com.jmt.model.cm.entity.CmBoxInfo;
import com.jmt.model.cm.vo.CmBoxInfoVO;

import java.util.List;

public interface CmBoxInfoDao {
    Long insert(CmBoxInfo record);
    int delete(Long id);
    int updateById(CmBoxInfo record);
    CmBoxInfo selectById(Long id);
    CmBoxInfo selectByBoxNo(String boxNo);
    List<CmBoxInfo> selectAll();

    List<CmBoxInfoVO> selectByCondition(CmBoxInfo condition);


}
