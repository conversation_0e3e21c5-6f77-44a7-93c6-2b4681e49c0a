package com.jmt.dao;

import com.jmt.model.cm.entity.CmBusinessShop;
import com.jmt.model.cm.vo.CmBusinessShopDetailVO;
import com.jmt.model.cm.vo.CmBusinessShopVO;
import com.jmt.model.cm.vo.PubBusInfoVO;
import com.jmt.model.cm.vo.ShopBaseInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CmBusinessShopDao {
    Integer insert(CmBusinessShop record);
    CmBusinessShop selectById(Long id);
    Integer delete(Long id);
    Integer updateById(CmBusinessShop member);

    Integer updateByEqApply(CmBusinessShop member);

    CmBusinessShop selectByUserId(Long userId);
    List<CmBusinessShop> selectListByBusNo(String busNo);

    Integer countByBusNo(String busNo);

    String selectBusNameByUserId(@Param("userId") Long userId);

    PubBusInfoVO selectBusInfoByUserId(@Param("userId") Long userId);

    List<CmBusinessShopVO> getBusinessShopListByBusNo(String busNo);

    CmBusinessShopVO getShopByNo(String shopNo);

    CmBusinessShopDetailVO selectShopById(Long id);

    ShopBaseInfo selectShopBaseInfoById(Long id);

    Long getUserIdByCreditCode(String creditCode);
    // List<CmBusinessMemberDTO> selectByCondition(CmBusinessMemberDTO demoDto);
}
