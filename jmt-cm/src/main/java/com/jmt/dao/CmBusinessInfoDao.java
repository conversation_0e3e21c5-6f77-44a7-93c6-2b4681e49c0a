package com.jmt.dao;

import com.jmt.model.cm.dto.BusinessInfoQueryDTO;
import com.jmt.model.cm.entity.CmBusinessInfo;
import com.jmt.model.cm.entity.CmBusinessMange;
import com.jmt.model.cm.vo.CmBusinessInfoVO;
import com.jmt.model.cm.vo.PubBusInfoVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface CmBusinessInfoDao {
    Integer insert(CmBusinessInfo cmBusinessInfo);
    Integer updateById(CmBusinessInfo cmBusinessInfo);
    Integer delete(@Param("id") Long id);
    CmBusinessInfo selectById(@Param("id") Long id);
    CmBusinessInfo selectByUserId(@Param("userId") Long userId);

    String selectBusNameByUserId(@Param("userId") Long userId);

//    List<CmBusinessInfo> selectByCondition(CmBusinessInfo condition);
    List<CmBusinessInfoVO> selectByCondition(BusinessInfoQueryDTO condition);

    CmBusinessInfo selectByBusinessNo(String businessNo);

    PubBusInfoVO selectBusInfoByUserId(@Param("userId") Long userId);
    /**
     * 根据省获取数量
     * @param province
     * @return
     */
    Integer getNumberByProvince(String province);
    /**
     * 根据省获取业务信息
     * @param province
     * @return
     */
    List<CmBusinessInfo> getBusinessInfoByProvince(String province);

    Integer getUnAuditBusinessNum(String operatorNo);

    Integer getAuditBusinessNum(String operatorNo);

    Integer getBusinessNum(String operatorNo);

    Integer countByBusNo(String busNo);

    List<CmBusinessInfo> getBusinessList(CmBusinessMange cmBusinessMange);

    List<String> getAreaDropdown(String operatorNo);

    List<LocalDate> getTimeDropdown(String operatorNo);

    Long getUserIdByCreditCode(String creditCode);
}
