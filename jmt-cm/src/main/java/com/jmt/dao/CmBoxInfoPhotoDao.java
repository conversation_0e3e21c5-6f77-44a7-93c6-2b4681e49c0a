package com.jmt.dao;

import com.jmt.model.cm.entity.CmBoxInfoPhoto;

import java.util.List;

public interface CmBoxInfoPhotoDao {
    int insert(CmBoxInfoPhoto record);

    int batchInsert(List<CmBoxInfoPhoto> records);
    int delete(Long id);

    int deleteByBoxNo(String boxNo);

    int update(CmBoxInfoPhoto record);

    CmBoxInfoPhoto selectById(Long id);
    List<CmBoxInfoPhoto> selectByBoxNo(String boxNo);

}
