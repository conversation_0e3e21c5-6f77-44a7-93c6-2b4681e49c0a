package com.jmt.dao;

import com.jmt.model.cm.entity.CmWorkEqApply;
import com.jmt.model.cm.vo.CmApplyEqShopVO;
import com.jmt.model.cm.vo.CmWorkEqApplyDetailVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
@Repository
public interface CmWorkEqApplyDao {
    Integer insert(CmWorkEqApply record);
    Integer update(CmWorkEqApply record);

    CmWorkEqApply selectById(@Param("id") Long id);
    List<CmWorkEqApply> selectAll();
    List<CmWorkEqApply> selectByWorkStatus(@Param("workStatus") Integer workStatus);

    Integer updateWorkStatus(Long id, Integer workStatus, String reason);
    Integer delete(Long id);

    List<CmWorkEqApply> selectByCondition(CmWorkEqApply cmWorkEqApply);
    /**
     * 获取指定省份的申请数量
     * @param province
     * @return
     */
    Integer getNumberByProvince(@Param("province") String province);

    Integer getNumberByOperatorNo(@Param("operatorNo") String operatorNo);
    /**
     * 获取指定操作员的未审核数量
     * @param operatorNo
     * @return
     */
    Integer getUnAuditBusinessNum(String operatorNo);


    List<String> selectShopNosByOperator(String operatorNo);

    List<String> selectShopNosByInvestorNo(String investorNo);


    List<CmApplyEqShopVO> selectShopDetailsByShopNos(@Param("shopNos") List<String> shopNos, @Param("operatorNo") String operatorNo);


    CmWorkEqApplyDetailVO selectWorkNoByShopNo(String shopNo);
}
