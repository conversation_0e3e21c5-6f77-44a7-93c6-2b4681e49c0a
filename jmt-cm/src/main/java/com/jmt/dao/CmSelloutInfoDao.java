package com.jmt.dao;

import com.jmt.model.cm.dto.CmSelloutInfoDTO;
import com.jmt.model.cm.entity.CmSelloutInfo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CmSelloutInfoDao {
    Long insert(CmSelloutInfo record);

    Integer batchInsert(List<CmSelloutInfo> list);

    Integer update(CmSelloutInfo record);


    Integer delete(@Param("id") Long id);


    CmSelloutInfo selectById(Long id);
    CmSelloutInfoDTO selectDtoById(Long id);


   List<CmSelloutInfoDTO> selectByCondition(CmSelloutInfoDTO condition);

    List<CmSelloutInfoDTO> selectPcByCondition(CmSelloutInfoDTO condition);

    List<CmSelloutInfoDTO> selectListByUserId(Long userId);

    int incrementVisitCount(Long id);
}
