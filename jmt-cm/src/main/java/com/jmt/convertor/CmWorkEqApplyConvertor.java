package com.jmt.convertor;

import com.jmt.model.cm.dto.CmWorkEqApplyDTO;
import com.jmt.model.cm.entity.CmWorkEqApply;
import com.jmt.model.cm.vo.CmWorkEqApplyVO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface CmWorkEqApplyConvertor {
    // 实体类 -> DTO
    CmWorkEqApplyDTO toDto(CmWorkEqApply cmWorkEqApply);

    // DTO -> 实体类
    CmWorkEqApply toEntity(CmWorkEqApplyDTO dto);

    // 列表转换
    List<CmWorkEqApplyDTO> toDtoList(List<CmWorkEqApply> list);

    List<CmWorkEqApply> toEntityList(List<CmWorkEqApplyDTO> dtoList);

    CmWorkEqApplyVO toVo(CmWorkEqApply entity);

    // VO -> Entity
    CmWorkEqApply toEntity(CmWorkEqApplyVO vo);

    List<CmWorkEqApplyVO> toVoList(List<CmWorkEqApply> list);
    List<CmWorkEqApply> voToEntityList(List<CmWorkEqApplyVO> voList);

}
