package com.jmt.convertor;

import com.jmt.model.cm.dto.CmWorkEqServiceDTO;
import com.jmt.model.cm.entity.CmWorkEqService;
import com.jmt.model.cm.vo.CmWorkEqServiceVO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface CmWorkEqServiceConvertor {
    // 实体类 -> DTO
    CmWorkEqServiceDTO toDto(CmWorkEqService cmWorkEqService);

    // DTO -> 实体类
    CmWorkEqService toEntity(CmWorkEqServiceDTO dto);

    // 列表转换
    List<CmWorkEqServiceDTO> toDtoList(List<CmWorkEqService> list);

    List<CmWorkEqService> toEntityList(List<CmWorkEqServiceDTO> dtoList);

    CmWorkEqServiceVO toVo(CmWorkEqService entity);

    // VO -> Entity
    CmWorkEqService toEntity(CmWorkEqServiceVO vo);

    List<CmWorkEqServiceVO> toVoList(List<CmWorkEqService> list);
    List<CmWorkEqService> voToEntityList(List<CmWorkEqServiceVO> voList);
}
