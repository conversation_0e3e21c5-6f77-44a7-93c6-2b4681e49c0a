package com.jmt.convertor;

import com.jmt.model.cm.dto.CmSelloutInfoDTO;
import com.jmt.model.cm.entity.CmSelloutInfo;
import com.jmt.model.cm.vo.CmSelloutInfoVO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface CmSelloutInfoConvertor {
    // 实体类 -> DTO
    CmSelloutInfoDTO toDto(CmSelloutInfo cmSupplyReqInfo);

    // DTO -> 实体类
    CmSelloutInfo toEntity(CmSelloutInfoDTO dto);

    // 列表转换
    List<CmSelloutInfoDTO> toDtoList(List<CmSelloutInfo> list);

    List<CmSelloutInfo> toEntityList(List<CmSelloutInfoDTO> dtoList);

    CmSelloutInfoVO toVo(CmSelloutInfo entity);

    // VO -> Entity
    CmSelloutInfo toEntity(CmSelloutInfoVO vo);

    List<CmSelloutInfoVO> toVoList(List<CmSelloutInfo> list);
    List<CmSelloutInfo> voToEntityList(List<CmSelloutInfoVO> voList);
}
