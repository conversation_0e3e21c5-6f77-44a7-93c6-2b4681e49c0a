package com.jmt.convertor;

import com.jmt.model.cm.dto.CmFarmingFoodInfoDTO;
import com.jmt.model.cm.entity.CmFarmingFoodInfo;
import com.jmt.model.cm.vo.CmFarmingFoodInfoVO;
import org.mapstruct.Mapper;

import java.util.List;
@Mapper(componentModel = "spring")
public interface CmFarmingFoodInfoConvertor {
    // 实体类 -> DTO
    CmFarmingFoodInfoDTO toDto(CmFarmingFoodInfo cmSupplyReqInfo);

    // DTO -> 实体类
    CmFarmingFoodInfo toEntity(CmFarmingFoodInfoDTO dto);

    // 列表转换
    List<CmFarmingFoodInfoDTO> toDtoList(List<CmFarmingFoodInfo> list);

    List<CmFarmingFoodInfo> toEntityList(List<CmFarmingFoodInfoDTO> dtoList);

    CmFarmingFoodInfoVO toVo(CmFarmingFoodInfo entity);

    // VO -> Entity
    CmFarmingFoodInfo toEntity(CmFarmingFoodInfoVO vo);

    List<CmFarmingFoodInfoVO> toVoList(List<CmFarmingFoodInfo> list);
    List<CmFarmingFoodInfo> voToEntityList(List<CmFarmingFoodInfoVO> voList);
}
