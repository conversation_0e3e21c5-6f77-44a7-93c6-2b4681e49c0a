package com.jmt.convertor;

import com.jmt.model.cm.dto.CmSupplyReqInfoDTO;
import com.jmt.model.cm.entity.CmSupplyReqInfo;
import com.jmt.model.cm.vo.CmSupplyReqInfoVO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface CmSupplyReqInfoConvertor {

    // 实体类 -> DTO
    CmSupplyReqInfoDTO toDto(CmSupplyReqInfo cmSupplyReqInfo);

    // DTO -> 实体类
    CmSupplyReqInfo toEntity(CmSupplyReqInfoDTO dto);

    // 列表转换
    List<CmSupplyReqInfoDTO> toDtoList(List<CmSupplyReqInfo> list);

    List<CmSupplyReqInfo> toEntityList(List<CmSupplyReqInfoDTO> dtoList);

    CmSupplyReqInfoVO toVo(CmSupplyReqInfo entity);

    // VO -> Entity
    CmSupplyReqInfo toEntity(CmSupplyReqInfoVO vo);

    List<CmSupplyReqInfoVO> toVoList(List<CmSupplyReqInfo> list);
    List<CmSupplyReqInfo> voToEntityList(List<CmSupplyReqInfoVO> voList);
}
