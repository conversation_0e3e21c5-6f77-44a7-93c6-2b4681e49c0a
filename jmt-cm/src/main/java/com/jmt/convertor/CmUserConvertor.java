package com.jmt.convertor;

import com.jmt.model.cm.dto.CmUserDTO;
import com.jmt.model.cm.entity.CmUser;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface CmUserConvertor {
    // 实体类 -> DTO
    CmUserDTO toDto(CmUser cmUser);

    // DTO -> 实体类
    CmUser toEntity(CmUserDTO dto);

    // 列表转换
    List<CmUserDTO> toDtoList(List<CmUser> list);

    List<CmUser> toEntityList(List<CmUserDTO> dtoList);

}
