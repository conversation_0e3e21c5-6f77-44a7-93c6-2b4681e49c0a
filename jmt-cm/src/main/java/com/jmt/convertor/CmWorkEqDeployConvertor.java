package com.jmt.convertor;

import com.jmt.model.cm.dto.CmWorkEqDeployDTO;
import com.jmt.model.cm.entity.CmWorkEqDeploy;
import com.jmt.model.cm.vo.CmWorkEqDeployVO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface CmWorkEqDeployConvertor {
    // 实体类 -> DTO
    CmWorkEqDeployDTO toDto(CmWorkEqDeploy cmWorkEqDeploy);

    // DTO -> 实体类
    CmWorkEqDeploy toEntity(CmWorkEqDeployDTO dto);

    // 列表转换
    List<CmWorkEqDeployDTO> toDtoList(List<CmWorkEqDeploy> list);

    List<CmWorkEqDeploy> toEntityList(List<CmWorkEqDeployDTO> dtoList);

    CmWorkEqDeployVO toVo(CmWorkEqDeploy entity);

    // VO -> Entity
    CmWorkEqDeploy toEntity(CmWorkEqDeployVO vo);

    List<CmWorkEqDeployVO> toVoList(List<CmWorkEqDeploy> list);
    List<CmWorkEqDeploy> voToEntityList(List<CmWorkEqDeployVO> voList);
}
