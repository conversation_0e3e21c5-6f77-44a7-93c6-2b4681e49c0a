package com.jmt.convertor;

import com.jmt.model.cm.dto.CmWorkEqApplyDetailDTO;
import com.jmt.model.cm.entity.CmWorkEqApplyDetail;
import org.mapstruct.Mapper;

import java.util.List;
@Mapper(componentModel = "spring")
public interface CmWorkEqApplyDetailConvertor {
    // 实体类 -> DTO
    CmWorkEqApplyDetailDTO toDto(CmWorkEqApplyDetail cmWorkEqApplyDetail);

    // DTO -> 实体类
    CmWorkEqApplyDetail toEntity(CmWorkEqApplyDetailDTO dto);

    // 列表转换
    List<CmWorkEqApplyDetailDTO> toDtoList(List<CmWorkEqApplyDetail> list);

    List<CmWorkEqApplyDetail> toEntityList(List<CmWorkEqApplyDetailDTO> dtoList);
}
