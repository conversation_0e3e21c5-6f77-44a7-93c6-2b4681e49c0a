package com.jmt.convertor;

import com.jmt.model.cm.dto.CmWorkEqRecallDTO;
import com.jmt.model.cm.entity.CmWorkEqRecall;
import com.jmt.model.cm.vo.CmWorkEqRecallVO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface CmWorkEqRecallConvertor {
    // 实体类 -> DTO
    CmWorkEqRecallDTO toDto(CmWorkEqRecall cmWorkEqRecall);

    // DTO -> 实体类
    CmWorkEqRecall toEntity(CmWorkEqRecallDTO dto);

    // 列表转换
    List<CmWorkEqRecallDTO> toDtoList(List<CmWorkEqRecall> list);

    List<CmWorkEqRecall> toEntityList(List<CmWorkEqRecallDTO> dtoList);

    CmWorkEqRecallVO toVo(CmWorkEqRecall entity);

    // VO -> Entity
    CmWorkEqRecall toEntity(CmWorkEqRecallVO vo);

    List<CmWorkEqRecallVO> toVoList(List<CmWorkEqRecall> list);
    List<CmWorkEqRecall> voToEntityList(List<CmWorkEqRecallVO> voList);
}
