package com.jmt.convertor;

import com.jmt.model.cm.dto.CmBusinessMemberDTO;
import com.jmt.model.cm.entity.CmBusinessMember;
import com.jmt.model.cm.vo.CmBusinessMemberVO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface CmBusinessMemberConvertor {
    // 实体类 -> DTO
    CmBusinessMemberDTO toDto(CmBusinessMember cmBusinessMember);

    // DTO -> 实体类
    CmBusinessMember toEntity(CmBusinessMemberDTO dto);

    // 列表转换
    List<CmBusinessMemberDTO> toDtoList(List<CmBusinessMember> list);

    List<CmBusinessMember> toEntityList(List<CmBusinessMemberDTO> dtoList);

    CmBusinessMemberVO toVo(CmBusinessMember entity);

    // VO -> Entity
    CmBusinessMember toEntity(CmBusinessMemberVO vo);

    List<CmBusinessMemberVO> toVoList(List<CmBusinessMember> list);
    List<CmBusinessMember> voToEntityList(List<CmBusinessMemberVO> voList);
}
