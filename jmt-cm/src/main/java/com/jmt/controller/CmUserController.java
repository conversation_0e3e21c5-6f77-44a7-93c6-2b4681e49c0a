package com.jmt.controller;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.google.gson.reflect.TypeToken;
import com.jmt.base.BaseController;
import com.jmt.client.EqFeignClient;
import com.jmt.client.JhhOperatorFeignClient;
import com.jmt.client.UaaFeignClient;
import com.jmt.enums.UserTypeEnum;
import com.jmt.model.cm.dto.CmUserDTO;
import com.jmt.model.cm.dto.CmUserInfoDto;
import com.jmt.model.cm.entity.CmBusinessInfo;
import com.jmt.model.cm.entity.CmBusinessShop;
import com.jmt.model.cm.entity.CmUser;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.cm.vo.CmUserBaseVO;
import com.jmt.model.cm.vo.CmUserNoVo;
import com.jmt.model.cm.vo.CmUserVO;
import com.jmt.model.eq.vo.EqListVo;
import com.jmt.model.jhh.dto.TransferSalesmanEqDto;
import com.jmt.model.jhh.vo.OperatorVo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.uaa.UaaUser;
import com.jmt.model.uaa.dto.UaaUserDTO;
import com.jmt.service.CmBusinessInfoService;
import com.jmt.service.CmBusinessShopService;
import com.jmt.service.CmUserService;
import com.jmt.util.GsonUtil;
import com.jmt.util.LoginUserUtil;
import com.jmt.util.ResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/cm/user")
public class CmUserController extends BaseController {
    @Resource
    private CmUserService cmUserService;

    @Resource
    private CmBusinessInfoService cmBusinessInfoService;

    @Resource
    private UaaFeignClient uaaFeignClient;

    @Resource
    private CmBusinessShopService cmBusinessShopService;

    @Resource
    private JhhOperatorFeignClient jhhOperatorFeignClient;

    @Resource
    private EqFeignClient eqFeignClient;

    @GetMapping("/v1/getCmUserNo")
    public String getCmUserNo(@RequestParam Long uaaId) {
        CmUserNoVo cmUserNoVo = new CmUserNoVo();
        CmUser cmUser = cmUserService.getUserByUaaId(uaaId);
        if (cmUser != null ) {
            CmBusinessShop shop = cmBusinessShopService.getShopByUserId(cmUser.getId());
            if (shop != null) {
                cmUserNoVo.setShopNo(shop.getShopNo());
            }
            CmBusinessInfo bus = cmBusinessInfoService.selectByUserId(cmUser.getId());
            if (bus != null) {
                cmUserNoVo.setBusNo(bus.getBusNo());
            }
        }
        return super.responseSuccess(cmUserNoVo,"查询成功");
    }

    /**
     * 新增
     * @param userDTO
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/v1/add")
    public String add(@RequestBody UaaUserDTO userDTO ) {
        try {
            String response = uaaFeignClient.registerUser(userDTO);
            Map<String, Object> responseMap = GsonUtil.fromJsonMap(response);
            String code = Convert.toStr(responseMap.get("code"));
            if (!"200".equals(code)) {
                String errorMsg = Convert.toStr(responseMap.get("msg"));
                return super.responseFail("UAA服务错误: " + errorMsg);
            }

            Object dataElement = responseMap.get("data");
            if (ObjectUtil.isEmpty(dataElement)) {
                return super.responseFail("UAA服务返回数据为空");
            }

            UaaUser user = Convert.convert(UaaUser.class,dataElement);
            if (user == null || user.getUaaId() == null) {
                return super.responseFail("用户注册失败: 无效的用户数据");
            }

            CmUserDTO cmUserDTO = new CmUserDTO();
            cmUserDTO.setUaaId(user.getUaaId());
            cmUserDTO.setUserName(user.getLoginName());
            cmUserDTO.setTelPhone(userDTO.getTelPhone());
            cmUserDTO.setRefereeNo(userDTO.getRefereeNo());

            CmUser cmUser= cmUserService.createUser(cmUserDTO);
            return super.responseSuccess(cmUser, "用户新增成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 修改
     * @param cmUser
     * @return
     */
    @PostMapping("/v1/update")
    @ResponseBody
    public String update(@RequestBody CmUser cmUser,HttpServletRequest req) {
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        cmUser.setUaaId(loginUser.getUaaId());
        cmUserService.updateByUaaId(cmUser);
        return super.responseSuccess("修改成功");
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @PostMapping("/v1/delete/{id}")
    public String delete(@PathVariable Long id) {
        try {
            cmUserService.deleteUser(id);
            return super.responseSuccess("删除成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 根据id查询
     * @param id
     * @return
     */
    @ResponseBody
    @GetMapping("/v1/{id}")
    public String getById(@PathVariable Long id) {
        CmUser cmUser = cmUserService.getUserById(id);
        if(cmUser == null){
            return super.responseFail("不存在该用户");
        }
        return super.responseSuccess(cmUser,"查询成功");
    }

    /**
     * 根据uaaId查询
     * @param
     * @return
     */
    @ResponseBody
    @GetMapping("/v1/uaa")
    public String getByUaaId( HttpServletRequest request) {
        try {
            LoginUaaUser loginUser = LoginUserUtil.get(request);
            CmUserBaseVO cmUser = cmUserService.getUserVoByUaaId(loginUser.getUaaId());
            if (cmUser == null) {
                return super.responseFail("用户不存在");
            }
            return super.responseSuccess(cmUser, "查询成功");
        } catch (Exception e) {
            return super.responseFail("查询失败: " + e.getMessage());
        }
    }
    /**
     * 查询运营商下的游客
     *
     * @param
     * @return
     */
    @ResponseBody
    @GetMapping(value = "/v1/getUserList")
    public String getUserList(@RequestParam String operatorNo){
        List<CmUser> cmUsers = cmUserService.getUserList(operatorNo);
        return super.responseSuccess(cmUsers, "查询成功");
    }

    /**
     * 获取当前登录运营商下的游客
     * @param req
     * @return
     */
//    @GetMapping(value = "/v1/getMyUserList")
//    public String getUserListByOperatorNo(HttpServletRequest req){
//        LoginUaaUser loginUser = LoginUserUtil.get(req);
//        List<CmUserVO> cmUsers;
//        //运营商
//        if(loginUser.getUserType() == 0){
//            String info = jhhOperatorFeignClient.getOperatorInfoByUaaId(loginUser.getUaaId());
//            OperatorInfo operatorInfo= GsonUtil.extractDataFieldFromJson(
//                    info,
//                    new TypeToken<OperatorInfo>(){}
//            );
//            cmUsers = cmUserService.selectVoByOperatorNo(operatorInfo.getOperatorNo());
//            return super.responseSuccess(cmUsers, "查询成功");
//        }
//
//        return super.responseSuccess("暂无游客");
//    }

    @PostMapping("/v1/getMyUserList")
    public String getPageList(@RequestBody(required = false) PageQuery pageQuery, HttpServletRequest req){
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
            // 设置默认页码和页大小，根据你的业务需求调整
            pageQuery.setPageNo(1);
            pageQuery.setPageSize(10);
        }
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        //运营商
        if(loginUser.getUserType() == 0){
            String info = jhhOperatorFeignClient.getOperatorInfoByUaaId(loginUser.getUaaId());
            OperatorVo operatorInfo= GsonUtil.extractDataFieldFromJson(
                    info,
                    new TypeToken<OperatorVo>(){}
            );
            PageResult<CmUserVO> pageResult = cmUserService.getPageByOperatorNo(pageQuery,operatorInfo.getOperatorNo());
            return super.responseSuccess(pageResult, "查询成功");
        }

        return super.responseSuccess("无数据");
    }
    /**
     * 获取运营商下的游客数量
     *
     * @return
     */
    @ResponseBody
    @GetMapping(value = "/v1/getUserNum")
    public String getUserNum(@RequestParam String operatorNo){
        Integer userNum = cmUserService.getUserNum(operatorNo);
        return super.responseSuccess(userNum, "查询成功");
    }
    /**
     * 获取运营商地址
     * @param uaaId
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/v1/getUserAddress")
    public String getUserAddress(@RequestParam Long uaaId) {
        String address = cmUserService.getUserAddress(uaaId);
        return super.responseSuccess(address, "修改成功");
    }

    @GetMapping(value = "/v1/getEqListByShopNo")
    public String getEqListByShopNo(HttpServletRequest req,@RequestParam(required = false) String shopNo){
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        CmUser cmUser = cmUserService.getUserByUaaId(loginUser.getUaaId());
        if(cmUser == null){
            return super.responseFail("用户不存在");
        }
        if(cmUser.getRoleType() == 1){
            String info = eqFeignClient.getInfoByShopNo(shopNo);
            List<EqListVo> eqListVo = ResponseUtil.getDataList(info, EqListVo.class);
            return super.responseSuccess(eqListVo,"查询成功");
        }
        if(cmUser.getRoleType() == 2){
            CmBusinessShop cmBusinessShop=cmBusinessShopService.selectByUserId(cmUser.getId());
            String info = eqFeignClient.getInfoByShopNo(cmBusinessShop.getShopNo());
            List<EqListVo> eqListVo = ResponseUtil.getDataList(info, EqListVo.class);
            return super.responseSuccess(eqListVo,"查询成功");
        }
        return super.responseSuccess("暂无设备");
    }

    @PostMapping(value = "/v1/transferSalesman")
    public String transferSalesman(@RequestBody TransferSalesmanEqDto transferSalesmanEqDto) {
        cmUserService.transferSalesman(transferSalesmanEqDto);
        return super.responseSuccess("迁移成功");
    }

    /**
     * 获取当前登录用户信息
     */
    @ResponseBody
    @GetMapping(value = "/v1/userInfo")
    public String userInfo() {
        LoginUaaUser loginUaaUser = LoginUserUtil.get();
        assert loginUaaUser != null;
        Integer userType = loginUaaUser.getUserType();
        return super.responseSuccess("查询成功");
    }

    /**
     * 设置个人信息
     * @param cmUserInfoDto 设置
     * @return String
     */
    @ResponseBody
    @PostMapping(value = "/v1/setup")
    public String setup(@RequestBody CmUserInfoDto cmUserInfoDto) {
        logger.info("设置用户信息",cmUserInfoDto);
        Integer r;
        LoginUaaUser loginUaaUser = LoginUserUtil.get();
        Integer userType = loginUaaUser.getUserType();
        return super.responseSuccess("设置成功");
    }
}
