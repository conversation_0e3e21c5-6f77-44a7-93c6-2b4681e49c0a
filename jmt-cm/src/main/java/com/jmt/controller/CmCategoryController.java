package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.cm.entity.CmCategory;
import com.jmt.service.CmCategoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/cm/category")
public class CmCategoryController extends BaseController {
    @Resource
    private CmCategoryService cmCategoryService;
    /**
     * 新增
     * @param cmCategory
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/v1/add")
    public String add(@RequestBody CmCategory cmCategory) {
        try {
            cmCategoryService.add(cmCategory);
            return super.responseSuccess("新增成功");
        } catch (Exception e) {
            log.error("新增团队成员失败", e);
            return super.responseFail("系统错误: " + e.getMessage());
        }
    }

    /**
     * 修改
     * @param cmCategory
     * @return
     */
    @PostMapping("/v1/update")
    @ResponseBody
    public String update(@RequestBody CmCategory cmCategory) {
        try {
            cmCategoryService.update(cmCategory);
            return super.responseSuccess("修改成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @PostMapping("/v1/delete/{id}")
    public String delete(@PathVariable Long id) {
        try {
            cmCategoryService.delete(id);
            return super.responseSuccess("删除成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 根据id查询
     * @param id
     * @return
     */
    @ResponseBody
    @GetMapping("/v1/{id}")
    public String getById(@PathVariable Long id) {
        CmCategory cmCategory = cmCategoryService.getById(id);
        if(cmCategory == null){
            return super.responseFail("不存在该类别");
        }
        return super.responseSuccess(cmCategory,"查询成功");
    }

    @ResponseBody
    @GetMapping("/v1/getAll")
    public String getAll() {
        List<CmCategory> categoryList = cmCategoryService.getAll();
        return super.responseSuccess(categoryList,"查询成功");
    }





}
