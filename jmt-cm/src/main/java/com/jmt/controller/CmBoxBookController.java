package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.cm.entity.CmBoxBook;
import com.jmt.model.cm.entity.CmUser;
import com.jmt.service.CmBoxBookService;
import com.jmt.service.CmUserService;
import com.jmt.util.LoginUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Slf4j
@RestController
@RequestMapping("/cm/boxBook")
public class CmBoxBookController extends BaseController {
    @Resource
    private CmBoxBookService cmBoxBookService;

    @Resource
    private CmUserService cmUserService;

    @PostMapping(value = "/v1/add")
    public String add(@RequestBody CmBoxBook cmBoxBook, HttpServletRequest req) {
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        CmUser cmUser=cmUserService.getUserByUaaId(loginUser.getUaaId());
        cmBoxBook.setUserId(cmUser.getId());
        try {
            cmBoxBookService.createBooking(cmBoxBook);
            return super.responseSuccess("创建成功");
        } catch (Exception e) {
            return super.responseFail("创建失败: " + e.getMessage());
        }
    }

    @PostMapping("/v1/update")
    public String update(@RequestBody CmBoxBook cmBoxBook) {
        cmBoxBookService.updateBooking(cmBoxBook);
        return super.responseSuccess("修改成功");
    }

    @PostMapping("/v1/delete/{id}")
    public String delete(@PathVariable Long id) {
        try {
            cmBoxBookService.deleteBooking(id);
            return super.responseSuccess("删除成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    @GetMapping("/v1/{id}")
    public String getById(@PathVariable Long id) {
        CmBoxBook cmBoxBook = cmBoxBookService.getBookingById(id);
        if(cmBoxBook == null){
            return super.responseFail("不存在该预定信息");
        }
        return super.responseSuccess(cmBoxBook,"查询成功");
    }
}
