package com.jmt.controller;

import com.google.gson.reflect.TypeToken;
import com.jmt.base.BaseController;
import com.jmt.client.JhhOperatorFeignClient;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.cm.dto.WorkOrderDTO;
import com.jmt.model.cm.vo.CmWorkOrderVO;
import com.jmt.model.jhh.vo.OperatorVo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.cm.dto.CmWorkEqServiceDTO;
import com.jmt.model.cm.vo.CmWorkEqServiceVO;
import com.jmt.service.CmWorkEqSvService;
import com.jmt.util.GsonUtil;
import com.jmt.util.LoginUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Slf4j
@RestController
@RequestMapping("/cm/workEqService")
public class CmWorkEqServiceController extends BaseController {
    @Resource
    private CmWorkEqSvService cmWorkEqSvService;
    @Resource
    private JhhOperatorFeignClient jhhOperatorFeignClient;

    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    @PostMapping("/v1/getPageList")
    public String getPageList(@RequestBody(required = false) PageQuery<WorkOrderDTO> pageQuery, HttpServletRequest req){
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
            // 设置默认页码和页大小，根据你的业务需求调整
            pageQuery.setPageNo(1);
            pageQuery.setPageSize(10);
        }

        LoginUaaUser loginUser = LoginUserUtil.get(req);
        //运营商
        if(loginUser.getUserType() == 0){
            String info = jhhOperatorFeignClient.getOperatorInfoByUaaId(loginUser.getUaaId());
            OperatorVo operatorInfo= GsonUtil.extractDataFieldFromJson(
                    info,
                    new TypeToken<OperatorVo>(){}
            );
            PageResult<CmWorkEqServiceDTO> pageResult = cmWorkEqSvService.getPage(pageQuery,operatorInfo.getOperatorNo());
            if(pageResult == null){
                return super.responseSuccess("暂无工单");
            }
            return super.responseSuccess(pageResult,"查询成功");

        }

        return super.responseSuccess("暂无工单");
    }

    /**
     * 获取所有工单列表
     * @param pageQuery
     * @return
     */
    @PostMapping("/v1/getWorkOrderList")
    public String getAllPage(@RequestBody(required = false) PageQuery<WorkOrderDTO> pageQuery, HttpServletRequest req){
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
            // 设置默认页码和页大小，根据你的业务需求调整
            pageQuery.setPageNo(1);
            pageQuery.setPageSize(10);
        }
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        //运营商
        if(loginUser.getUserType() == 0){
            String info = jhhOperatorFeignClient.getOperatorInfoByUaaId(loginUser.getUaaId());
            OperatorVo operatorInfo= GsonUtil.extractDataFieldFromJson(
                    info,
                    new TypeToken<OperatorVo>(){}
            );
            PageResult<CmWorkOrderVO> pageResult = cmWorkEqSvService.getAllPage(pageQuery,operatorInfo.getOperatorNo());

            if(pageResult == null){
                return super.responseSuccess("暂无工单");
            }
            return super.responseSuccess(pageResult,"查询成功");

        }


        return super.responseSuccess("暂无工单");
    }

    /**
     * 新增
     * @param cmWorkEqServiceDTO
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/v1/add")
    public String add(@RequestBody CmWorkEqServiceDTO cmWorkEqServiceDTO) {
        try {
            cmWorkEqSvService.create(cmWorkEqServiceDTO);
            return super.responseSuccess("新增成功");
        } catch (Exception e) {
            log.error("新增工单失败", e);
            return super.responseFail("系统错误: " + e.getMessage());
        }
    }

    /**
     * 修改
     * @param dto
     * @return
     */
    @PostMapping("/v1/update")
    @ResponseBody
    public String update(@RequestBody CmWorkEqServiceDTO dto) {
        try {
            cmWorkEqSvService.update(dto);
            return super.responseSuccess("修改成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @PostMapping("/v1/delete/{id}")
    public String delete(@PathVariable Long id) {
        try {
            cmWorkEqSvService.delete(id);
            return super.responseSuccess("删除成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 根据id查询
     * @param id
     * @return
     */
    @ResponseBody
    @GetMapping("/v1/{id}")
    public String getById(@PathVariable Long id) {
        CmWorkEqServiceVO cmWorkEqServiceVO = cmWorkEqSvService.getById(id);
        if(cmWorkEqServiceVO == null){
            return super.responseFail("不存在该工单");
        }
        return super.responseSuccess(cmWorkEqServiceVO,"查询成功");
    }


    /**
     * 审核申请工单
     * @param id
     * @param auditStatus
     * @return
     */
    @PostMapping("/v1/audit/{id}")
    public String auditBusiness(@PathVariable Long id,
                                @RequestParam Integer auditStatus, @RequestParam String reason) {
        try {
            cmWorkEqSvService.auditBusiness(id, auditStatus,reason);
            return super.responseSuccess("审核成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }

    }
    /**
     * 获取运营商旗下维护工单数
     * @param operatorNo
     * @return
     */
    @GetMapping("/v1/getWorkEqServiceNum")
    public String getWorkEqServiceNum(@RequestParam String operatorNo) {
        try {
            Integer workEqServiceNum = cmWorkEqSvService.getWorkEqServiceNum(operatorNo);
            return super.responseSuccess(workEqServiceNum,"查询成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }


}
