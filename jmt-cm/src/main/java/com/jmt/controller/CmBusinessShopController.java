package com.jmt.controller;

import com.google.gson.reflect.TypeToken;
import com.jmt.base.BaseController;
import com.jmt.client.JhhInvestorFeignClient;
import com.jmt.client.JhhOperatorFeignClient;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.cm.entity.CmBusinessInfo;
import com.jmt.model.cm.entity.CmBusinessShop;
import com.jmt.model.cm.entity.CmUser;
import com.jmt.model.cm.vo.CmApplyEqShopVO;
import com.jmt.model.cm.vo.CmBusinessShopDetailVO;
import com.jmt.model.cm.vo.CmBusinessShopVO;
import com.jmt.model.jhh.vo.OperatorVo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.service.CmBusinessInfoService;
import com.jmt.service.CmBusinessShopService;
import com.jmt.service.CmUserService;
import com.jmt.util.GsonUtil;
import com.jmt.util.LoginUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/cm/businessShop")
public class CmBusinessShopController extends BaseController {
    @Resource
    private CmBusinessShopService cmBusinessShopService;

    @Resource
    private CmUserService cmUserService;

    @Resource
    private CmBusinessInfoService cmBusinessInfoService;

    @Resource
    private JhhOperatorFeignClient jhhOperatorFeignClient;

    @Resource
    private JhhInvestorFeignClient jhhInvestorFeignClient;

    /**
     * 新增
     * @param cmBusinessShop
     * @return
     */
    @PostMapping(value = "/v1/add")
    public String add(@RequestBody CmBusinessShop cmBusinessShop, HttpServletRequest  req) {
        try {
            LoginUaaUser loginUser = LoginUserUtil.get(req);
            //登录商家的推荐码就是其以下门店的推荐码
            CmUser cmUser = cmUserService.getUserByUaaId(loginUser.getUaaId());
            if(cmUser.getRoleType() != 1){
                return super.responseFail("当前为游客 请升级为商家");
            }
            CmBusinessInfo cmBusinessInfo = cmBusinessInfoService.selectByUserId(cmUser.getId());
            cmBusinessShop.setBusNo(cmBusinessInfo.getBusNo());
            cmBusinessShopService.create(cmBusinessShop,cmUser);
            return super.responseSuccess("新增成功");
        } catch (Exception e) {
            log.error("新增团队成员失败", e);
            return super.responseFail("系统错误: " + e.getMessage());
        }
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @PostMapping("/v1/delete/{id}")
    public String delete(@PathVariable Long id) {
        try {
            cmBusinessShopService.deleteBusinessShop(id);
            return super.responseSuccess("删除成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 修改
     * @param cmBusinessShop
     * @return
     */
    @PostMapping("/v1/update")
    public String update(@RequestBody CmBusinessShop cmBusinessShop) {
        cmBusinessShopService.updateBusinessShop(cmBusinessShop);
        return super.responseSuccess("修改成功");
    }


    /**
     * 根据id查询
     * @param id
     * @return
     */
    @ResponseBody
    @GetMapping("/v1/{id}")
    public String getById(@PathVariable Long id) {
        CmBusinessShop cmBusinessShop = cmBusinessShopService.getBusinessShopById(id);
        if(cmBusinessShop == null){
            return super.responseFail("不存在该门店");
        }
        return super.responseSuccess(cmBusinessShop,"查询成功");
    }

    @ResponseBody
    @GetMapping("/v1/getMyShopInfo")
    public String getMyShopInfo(HttpServletRequest req) {
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        CmUser cmUser=cmUserService.getUserByUaaId(loginUser.getUaaId());
        CmBusinessShop cmBusinessShop = cmBusinessShopService.getShopByUserId(cmUser.getId());
        if(cmBusinessShop == null){
            return super.responseFail("不存在该门店");
        }
        return super.responseSuccess(cmBusinessShop,"查询成功");
    }

    /**
     * 聚合伙查看门店详情
     * @param id
     * @return
     */
    @GetMapping("/v1/applyDatail/{id}")
    public String getShopDetailById(@PathVariable Long id) {
        CmBusinessShopDetailVO cmBusinessShopDetailVO = cmBusinessShopService.getShopDetailById(id);
        if(cmBusinessShopDetailVO == null){
            return super.responseFail("不存在该商家");
        }
        return super.responseSuccess(cmBusinessShopDetailVO,"查询成功");
    }

    /**
     * 根据当前登录的门店负责人获取门店信息
     * @param req
     * @return
     */
    @GetMapping("/v1/getMyShopDetail")
    @ResponseBody
    public String getMyShopDetail(HttpServletRequest req) {
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        CmUser cmUser = cmUserService.getUserByUaaId(loginUser.getUaaId());
        if(cmUser.getRoleType() == 2){
            CmBusinessShop cmBusinessShop = cmBusinessShopService.selectByUserId(cmUser.getId());
            if(cmBusinessShop == null){
                return super.responseSuccess("暂无门店");
            }
            return super.responseSuccess(cmBusinessShop,"查询成功");
        }
        return super.responseSuccess("当前登录不为门店负责人");
    }

    /**
     * 获取当前登录用户的门店列表
     * @param req
     * @return
     */
    @GetMapping("/v1/list")
    public String getMyBusinessShopList(HttpServletRequest req) {
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        CmUser cmUser = cmUserService.getUserByUaaId(loginUser.getUaaId());
        if(cmUser.getRoleType() != 1){
            return super.responseFail("当前为游客 请升级为商家");
        }
        CmBusinessInfo cmBusinessInfo = cmBusinessInfoService.selectByUserId(cmUser.getId());
        List<CmBusinessShop> cmBusinessShop = cmBusinessShopService.getMyBusinessShopList(cmBusinessInfo.getBusNo());
        if(cmBusinessShop == null){
            return super.responseSuccess("暂无门店");
        }
        return super.responseSuccess(cmBusinessShop,"查询成功");
    }

    /**
     * 登录运营商查看待部署设备门店列表
     * @param pageQuery
     * @param req
     * @return
     */
    @PostMapping("/v1/getApplyEqShopList")
    public String getApplyEqShopList(@RequestBody(required = false) PageQuery pageQuery,HttpServletRequest req) {
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        //运营商
        if(loginUser.getUserType() == 0){
            String info = jhhOperatorFeignClient.getOperatorInfoByUaaId(loginUser.getUaaId());
            OperatorVo operatorInfo= GsonUtil.extractDataFieldFromJson(
                    info,
                    new TypeToken<OperatorVo>(){}
            );
            PageResult<CmApplyEqShopVO> pageResult = cmBusinessShopService.getApplyEqShopListByOperator(pageQuery,operatorInfo.getOperatorNo());
            if(pageResult == null){
                return super.responseSuccess("暂无门店");
            }
            return super.responseSuccess(pageResult,"查询成功");

        } //设备受益人
//        else if(loginUser.getUserType() == 1){
//            String info = jhhInvestorFeignClient.getInvestorInfoByUaaId(loginUser.getUaaId());
//            InvestorInfo investorInfo= GsonUtil.extractDataFieldFromJson(
//                    info,
//                    new TypeToken<InvestorInfo>(){}
//            );
//            PageResult<CmApplyEqShopVO> pageResult = cmBusinessShopService.getApplyEqShopListByInvestor(pageQuery,investorInfo.getInvestorNo());
//            if(pageResult == null){
//                return super.responseSuccess("暂无门店");
//            }
//            return super.responseSuccess(pageResult,"查询成功");
//         //业务员
//        }else if(loginUser.getUserType() == 2){
//            String info = jhhOperatorFeignClient.getOperatorInfoByUaaId(loginUser.getUaaId());
//            OperatorVo operatorInfo= GsonUtil.extractDataFieldFromJson(
//                    info,
//                    new TypeToken<OperatorVo>(){}
//            );
//            PageResult<CmApplyEqShopVO> pageResult = cmBusinessShopService.getApplyEqShopListByOperator(pageQuery,operatorInfo.getOperatorNo());
//            if(pageResult == null){
//                return super.responseSuccess("暂无门店");
//            }
//            return super.responseSuccess(pageResult,"查询成功");
//        }

        return super.responseSuccess("暂无门店");
    }

    @GetMapping("/v1/listByBusNo")
    public String getBusinessShopListByBusNo(@RequestParam String busNo) {
        List<CmBusinessShopVO> cmBusinessShop = cmBusinessShopService.getBusinessShopListByBusNo(busNo);
        if(cmBusinessShop == null){
            return super.responseSuccess("暂无门店");
        }
        return super.responseSuccess(cmBusinessShop,"查询成功");
    }

    @GetMapping("/v1/getShopByNo")
    public String getShopByNo(@RequestParam String shopNo) {
        CmBusinessShopVO cmBusinessShop = cmBusinessShopService.getShopByNo(shopNo);
        if(cmBusinessShop == null){
            return super.responseSuccess("暂无门店");
        }
        return super.responseSuccess(cmBusinessShop,"查询成功");
    }




}
