package com.jmt.controller;

import com.google.gson.reflect.TypeToken;
import com.jmt.base.BaseController;
import com.jmt.client.JhhOperatorFeignClient;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.cm.dto.WorkOrderDTO;
import com.jmt.model.jhh.vo.OperatorVo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.cm.dto.CmWorkEqRecallDTO;
import com.jmt.model.cm.vo.CmWorkEqRecallVO;
import com.jmt.service.CmWorkEqRecallService;
import com.jmt.util.GsonUtil;
import com.jmt.util.LoginUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Slf4j
@RestController
@RequestMapping("/cm/workEqRecall")
public class CmWorkEqRecallController extends BaseController {
    @Resource
    private CmWorkEqRecallService cmWorkEqRecallService;

    @Resource
    private JhhOperatorFeignClient jhhOperatorFeignClient;


    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    @PostMapping("/v1/getPageList")
    public String getPageList(@RequestBody(required = false) PageQuery<WorkOrderDTO> pageQuery, HttpServletRequest req){
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
            // 设置默认页码和页大小，根据你的业务需求调整
            pageQuery.setPageNo(1);
            pageQuery.setPageSize(10);
        }

        LoginUaaUser loginUser = LoginUserUtil.get(req);
        //运营商
        if(loginUser.getUserType() == 0){
            String info = jhhOperatorFeignClient.getOperatorInfoByUaaId(loginUser.getUaaId());
            OperatorVo operatorInfo= GsonUtil.extractDataFieldFromJson(
                    info,
                    new TypeToken<OperatorVo>(){}
            );
            PageResult<CmWorkEqRecallDTO> pageResult = cmWorkEqRecallService.getPage(pageQuery,operatorInfo.getOperatorNo());

            if(pageResult == null){
                return super.responseSuccess("暂无工单");
            }
            return super.responseSuccess(pageResult,"查询成功");

        }

        return super.responseSuccess("暂无工单");
    }

    /**
     * 新增
     * @param cmWorkEqRecallDTO
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/v1/add")
    public String add(@RequestBody CmWorkEqRecallDTO cmWorkEqRecallDTO) {
        try {
            cmWorkEqRecallService.create(cmWorkEqRecallDTO);
            return super.responseSuccess("新增成功");
        } catch (Exception e) {
            log.error("新增工单失败", e);
            return super.responseFail("系统错误: " + e.getMessage());
        }
    }

    /**
     * 修改
     * @param dto
     * @return
     */
    @PostMapping("/v1/update")
    @ResponseBody
    public String update(@RequestBody CmWorkEqRecallDTO dto) {
        try {
            cmWorkEqRecallService.update(dto);
            return super.responseSuccess("修改成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @PostMapping("/v1/delete/{id}")
    public String delete(@PathVariable Long id) {
        try {
            cmWorkEqRecallService.delete(id);
            return super.responseSuccess("删除成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 根据id查询
     * @param id
     * @return
     */
    @ResponseBody
    @GetMapping("/v1/{id}")
    public String getById(@PathVariable Long id) {
        CmWorkEqRecallVO cmWorkEqRecallVO = cmWorkEqRecallService.getById(id);
        if(cmWorkEqRecallVO == null){
            return super.responseFail("不存在该工单");
        }
        return super.responseSuccess(cmWorkEqRecallVO,"查询成功");
    }


    /**
     * 审核申请工单
     * @param id
     * @param auditStatus
     * @return
     */
    @PostMapping("/v1/audit/{id}")
    public String auditBusiness(@PathVariable Long id,
                                @RequestParam Integer auditStatus, @RequestParam String reason) {
        try {
            cmWorkEqRecallService.auditBusiness(id, auditStatus,reason);
            return super.responseSuccess("审核成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

}
