package com.jmt.controller;

import com.google.gson.reflect.TypeToken;
import com.jmt.base.BaseController;
import com.jmt.client.JhhOperatorFeignClient;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.cm.dto.BusinessInfoQueryDTO;
import com.jmt.model.cm.entity.CmBusinessInfo;
import com.jmt.model.cm.entity.CmBusinessMange;
import com.jmt.model.cm.entity.CmUser;
import com.jmt.model.cm.vo.CmBusinessInfoVO;
import com.jmt.model.jhh.vo.OperatorVo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.service.CmBusinessInfoService;
import com.jmt.service.CmUserService;
import com.jmt.util.GsonUtil;
import com.jmt.util.LoginUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/cm/business")
public class CmBusinessInfoController extends BaseController {
    @Resource
    private CmBusinessInfoService cmBusinessInfoService;
    @Resource
    private CmUserService cmUserService;

    @Resource
    private JhhOperatorFeignClient jhhOperatorFeignClient;


    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    @PostMapping("/v1/getPageList")
    public String getPageList(@RequestBody(required = false) PageQuery<BusinessInfoQueryDTO> pageQuery,HttpServletRequest req){
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
            // 设置默认页码和页大小，根据你的业务需求调整
            pageQuery.setPageNo(1);
            pageQuery.setPageSize(10);
        }
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        //运营商
        if(loginUser.getUserType() == 0){
            String info = jhhOperatorFeignClient.getOperatorInfoByUaaId(loginUser.getUaaId());
            OperatorVo operatorInfo= GsonUtil.extractDataFieldFromJson(
                    info,
                    new TypeToken<OperatorVo>(){}
            );
            pageQuery.getQueryData().setOperatorNo(operatorInfo.getOperatorNo());
        }

        PageResult<CmBusinessInfoVO> pageResult = cmBusinessInfoService.getPage(pageQuery);
        return super.responseSuccess(pageResult,"查询成功");
    }
     /**
     * 升级商家
     * @param cmBusinessInfo
     * @return
     */
     @PostMapping(value = "/v1/add")
    public String add(@RequestBody CmBusinessInfo cmBusinessInfo, HttpServletRequest req) {
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        CmUser cmUser = cmUserService.getUserByUaaId(loginUser.getUaaId());
        if(cmUser.getRoleType() == 1){
            return super.responseFail("已经是商家,不可再升级");
        }
        try {
            cmBusinessInfoService.createBusinessInfo(cmBusinessInfo,cmUser);
            return super.responseSuccess("升级商家成功");
        } catch (Exception e) {
            log.error("新增商家信息失败", e);
            return super.responseFail("系统错误: " + e.getMessage());
        }
    }

    /**
     * 修改
     * @param cmBusinessInfo
     * @return
     */
    @PostMapping("/v1/update")
    @ResponseBody
    public String update(@RequestBody CmBusinessInfo cmBusinessInfo) {
        cmBusinessInfoService.updateBusinessInfo(cmBusinessInfo);
        return super.responseSuccess("修改成功");
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @PostMapping("/v1/delete/{id}")
    public String delete(@PathVariable Long id) {
        try {
            cmBusinessInfoService.deleteBusinessInfo(id);
            return super.responseSuccess("删除成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 根据id查询
     * @param id
     * @return
     */
    @ResponseBody
    @GetMapping("/v1/{id}")
    public String getById(@PathVariable Long id) {
        CmBusinessInfo cmBusinessInfo = cmBusinessInfoService.getBusinessInfoById(id);
        if(cmBusinessInfo == null){
            return super.responseFail("不存在该商家");
        }
        return super.responseSuccess(cmBusinessInfo,"查询成功");
    }

    /**
     * 根据userId查询
     * @param userId
     * @return
     */
    @ResponseBody
    @GetMapping("/v1/user/{userId}")
    public String getByUaaId(@PathVariable Long userId) {
        CmBusinessInfo cmBusinessInfo = cmBusinessInfoService.selectByUserId(userId);
        if(cmBusinessInfo == null){
            return super.responseFail("不存在该商家");
        }
        return super.responseSuccess(cmBusinessInfo,"查询成功");
    }

    @ResponseBody
    @GetMapping("/v1/getMyInfo")
    public String getMyInfo(HttpServletRequest req) {
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        CmUser cmUser= cmUserService.getUserByUaaId(loginUser.getUaaId());
        CmBusinessInfo cmBusinessInfo = cmBusinessInfoService.selectByUserId(cmUser.getId());
        if(cmBusinessInfo == null){
            return super.responseFail("不存在该商家");
        }
        return super.responseSuccess(cmBusinessInfo,"查询成功");
    }

    /**
     * 审核商家
     */
    @PostMapping("/v1/audit/{id}")
    public String auditBusiness(@PathVariable Long id,
                                      @RequestParam Integer auditStatus,
                                      @RequestParam(required = false) String reason,
                                      @RequestParam(required = false)Integer busStar) {
        try {
             cmBusinessInfoService.auditBusiness(id, auditStatus, reason,busStar);
            return super.responseSuccess("审核成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }

    }
    @ResponseBody
    @GetMapping("/v1/getByBusinessNo")
    public String getByBusinessNo(@RequestParam String businessNo) {
        CmBusinessInfo cmBusinessInfo = cmBusinessInfoService.getBusinessInfoByBusinessNo(businessNo);
        if(cmBusinessInfo == null){
            return super.responseFail("不存在该商家");
        }
        return super.responseSuccess(cmBusinessInfo,"查询成功");
    }

    /**
     * 给商家分配运营商和部署业务员
     * @param id
     * @param operatorNo
     * @param salesmanNo
     * @return
     */
    @PostMapping("/v1/updateOperator/{id}")
    public String updateOperator(@PathVariable Long id,
                                 @RequestParam String operatorNo,@RequestParam String salesmanNo) {
        cmBusinessInfoService.updateOperator(id,operatorNo, salesmanNo);
        return super.responseSuccess("修改成功");
    }

    /**
     * 通过省份获取商家数量
     * @param province
     * @return
     */
    @GetMapping("/v1/getNumberByProvince")
    public String getNumberByProvince(@RequestParam String province) {
        Integer number = cmBusinessInfoService.getNumberByProvince(province);
        return super.responseSuccess(number, "查询成功");
    }

    /**
     * 获取同一省份的所有商家
     */
    @GetMapping("/v1/getBusinessInfoByProvince")
    public String getBusinessInfoByProvince(@RequestParam String province) {
        List<CmBusinessInfo> list = cmBusinessInfoService.getBusinessInfoByProvince(province);
        return super.responseSuccess(list, "查询成功");
    }

    /**
     * 按运营商编号查看未审商家数目
     */
    @GetMapping("/v1/getUnAuditBusinessNum")
    public String getUnAuditBusinessNum(@RequestParam String operatorNo) {
        Integer number = cmBusinessInfoService.getUnAuditBusinessNum(operatorNo);
        return super.responseSuccess(number, "查询成功");
    }

    /**
     * 按运营商编号查看已审商家数目
     */
    @GetMapping("/v1/getAuditBusinessNum")
    public String getAuditBusinessNum(@RequestParam String operatorNo) {
        Integer number = cmBusinessInfoService.getAuditBusinessNum(operatorNo);
        return super.responseSuccess(number, "查询成功");
    }
    /**
     * 按运营商编号获取商家数量
     */
    @GetMapping("/v1/getBusinessNum")
    public String getBusinessNum(@RequestParam String operatorNo) {
        Integer number = cmBusinessInfoService.getBusinessNum(operatorNo);
        return super.responseSuccess(number, "查询成功");
    }

    /**
     * 条件查询商家
     */
    @PostMapping("/v1/getBusinessList")
    public String getBusinessList(@RequestBody(required = false)CmBusinessMange cmBusinessMange) {
        List<CmBusinessInfo> cmBusinessInfos = cmBusinessInfoService.getBusinessList(cmBusinessMange);
        return super.responseSuccess(cmBusinessInfos, "查询成功");
    }

    /**
     * 获取运营商区域下拉框数据
     */
    @ResponseBody
    @GetMapping(value = "/getAreaDropdown")
    public String getAreaDropdown(@RequestParam String operatorNo) {
        List<String> list = cmBusinessInfoService.getAreaDropdown(operatorNo);
        return super.responseSuccess(list, "查询成功");
    }
    /**
     * 获取商家时间下拉框数据
     */
    @ResponseBody
    @GetMapping(value = "/getTimeDropdown")
    public String getTimeDropdown(@RequestParam String operatorNo) {
        List<String> list = cmBusinessInfoService.getTimeDropdown(operatorNo);
        return super.responseSuccess(list, "查询成功");
    }

    @PostMapping(value = "/v1/getByCreditCode")
    public String getByCreditCode(@RequestParam String creditCode) {
        CmUser getByCreditCode = cmBusinessInfoService.getByCreditCode(creditCode);
        return super.responseSuccess(getByCreditCode, "查询成功");
    }


}
