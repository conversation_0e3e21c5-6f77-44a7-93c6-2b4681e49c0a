package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.cm.dto.CmBoxInfoDTO;
import com.jmt.model.cm.entity.CmBoxInfo;
import com.jmt.model.cm.entity.CmUser;
import com.jmt.model.cm.vo.CmBoxInfoVO;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.service.CmBoxInfoService;
import com.jmt.service.CmUserService;
import com.jmt.util.LoginUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Slf4j
@RestController
@RequestMapping("/cm/box")
public class CmBoxInfoController extends BaseController {
    @Resource
    private CmBoxInfoService cmBoxInfoService;

    @Resource
    private CmUserService cmUserService;

    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    @PostMapping("/v1/getPageList")
    public String getPageList(@RequestBody(required = false) PageQuery<CmBoxInfo> pageQuery){
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
            pageQuery.setPageNo(1);
            pageQuery.setPageSize(10);
        }
        PageResult<CmBoxInfoVO> pageResult = cmBoxInfoService.getBoxInfoList(pageQuery);
        return super.responseSuccess(pageResult,"查询成功");
    }

    @PostMapping("/v1/getMyPageList")
    public String getMyPageList(@RequestBody(required = false) PageQuery<CmBoxInfo> pageQuery,HttpServletRequest req){
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
            pageQuery.setPageNo(1);
            pageQuery.setPageSize(10);
        }
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        CmUser cmUser = cmUserService.getUserByUaaId(loginUser.getUaaId());

        PageResult<CmBoxInfoVO> pageResult = cmBoxInfoService.getMyBoxInfoList(pageQuery,cmUser);
        return super.responseSuccess(pageResult,"查询成功");
    }

    /**
     * 创建包厢
     * @param createDTO
     * @return
     */
    @PostMapping(value = "/v1/add")
    public String add(@RequestBody CmBoxInfoDTO createDTO, HttpServletRequest req) {
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        try {
            cmBoxInfoService.createBoxInfo(createDTO,loginUser);
            return super.responseSuccess("创建成功");
        } catch (Exception e) {
            return super.responseFail("创建失败: " + e.getMessage());
        }
    }

    /**
     * 修改
     * @param updateDTO
     * @return
     */
    @PostMapping("/v1/update")
    @ResponseBody
    public String update(@RequestBody CmBoxInfoDTO updateDTO) {
        cmBoxInfoService.updateBoxInfo(updateDTO);
        return super.responseSuccess("修改成功");
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @PostMapping("/v1/delete/{id}")
    public String delete(@PathVariable Long id) {
        try {
            cmBoxInfoService.deleteBoxInfo(id);
            return super.responseSuccess("删除成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 根据id查询
     * @param id
     * @return
     */
    @ResponseBody
    @GetMapping("/v1/{id}")
    public String getById(@PathVariable Long id) {
        CmBoxInfoDTO cmBoxInfoDTO = cmBoxInfoService.getBoxInfoById(id);
        if(cmBoxInfoDTO == null){
            return super.responseFail("不存在该包厢");
        }
        return super.responseSuccess(cmBoxInfoDTO,"查询成功");
    }


}
