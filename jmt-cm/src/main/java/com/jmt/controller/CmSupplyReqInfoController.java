package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.cm.dto.CmIndexSupplyDTO;
import com.jmt.model.cm.dto.CmInfoQueryDataDTO;
import com.jmt.model.cm.vo.CmInfoVo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.cm.dto.CmSupplyReqInfoDTO;
import com.jmt.model.cm.entity.CmSupplyReqOrder;
import com.jmt.model.cm.vo.CmSupplyReqInfoVO;
import com.jmt.service.CmSupplyReqInfoService;
import com.jmt.util.LoginUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/cm/supplyReqInfo")
public class CmSupplyReqInfoController extends BaseController {
    @Resource
    private CmSupplyReqInfoService cmSupplyReqInfoService;

    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    @PostMapping("/v1/getPageList")
    public String getPageList(@RequestBody(required = false) PageQuery<CmSupplyReqInfoDTO> pageQuery){
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
            // 设置默认页码和页大小，根据你的业务需求调整
            pageQuery.setPageNo(1);
            pageQuery.setPageSize(10);
        }
        PageResult<CmSupplyReqInfoVO> pageResult = cmSupplyReqInfoService.getPage(pageQuery);
        return super.responseSuccess(pageResult,"查询成功");
    }

    @GetMapping("/v1/myList")
    public String getListByUaaId(HttpServletRequest req) {
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        List<CmSupplyReqInfoVO> cmSupplyReqInfoVO = cmSupplyReqInfoService.getListByUaaId(loginUser.getUaaId());
        return super.responseSuccess(cmSupplyReqInfoVO,"查询成功");
    }

    /**
     * 新增
     * @param cmSupplyReqInfoDTO
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/v1/add")
    public String add(@RequestBody CmSupplyReqInfoDTO cmSupplyReqInfoDTO,HttpServletRequest req) {
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        try {
            cmSupplyReqInfoService.create(cmSupplyReqInfoDTO,loginUser);
            return super.responseSuccess("新增成功");
        } catch (Exception e) {
            log.error("新增工单失败", e);
            return super.responseFail("系统错误: " + e.getMessage());
        }
    }

    /**
     * 修改
     * @param dto
     * @return
     */
    @PostMapping("/v1/update")
    @ResponseBody
    public String update(@RequestBody CmSupplyReqInfoDTO dto) {
        try {
            cmSupplyReqInfoService.update(dto);
            return super.responseSuccess("修改成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }


    /**
     * 删除
     * @param id
     * @return
     */
    @PostMapping("/v1/delete/{id}")
    public String delete(@PathVariable Long id) {
        try {
            cmSupplyReqInfoService.delete(id);
            return super.responseSuccess("删除成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 根据id查询
     * @param id
     * @return
     */
    @ResponseBody
    @GetMapping("/v1/{id}")
    public String getById(@PathVariable Long id) {
        CmSupplyReqInfoVO cmSupplyReqInfoVO = cmSupplyReqInfoService.getById(id);
        if(cmSupplyReqInfoVO == null){
            return super.responseFail("不存在该工单");
        }
        return super.responseSuccess(cmSupplyReqInfoVO,"查询成功");
    }

    @GetMapping("/v1/withVisit/{id}")
    public String getByWithVisitId(@PathVariable Long id) {
        CmSupplyReqInfoVO cmSupplyReqInfoVO = cmSupplyReqInfoService.getByWithVisitId(id);
        if(cmSupplyReqInfoVO == null){
            return super.responseFail("不存在该工单");
        }
        return super.responseSuccess(cmSupplyReqInfoVO,"查询成功");
    }
    /**
     * 审核申请工单
     * @param id
     * @param auditStatus
     * @return
     */
    @PostMapping("/v1/audit/{id}")
    public String auditBusiness(@PathVariable Long id,
                                @RequestParam Integer auditStatus, @RequestParam String reason, HttpServletRequest req) {
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        try {
            cmSupplyReqInfoService.auditBusiness(id, auditStatus,reason, loginUser.getUaaId());
            return super.responseSuccess("审核成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 生成订单
     * @param cmSupplyReqOrder
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/v1/addOrder")
    public String addOrder(@RequestBody CmSupplyReqOrder cmSupplyReqOrder) {
        try {
            cmSupplyReqInfoService.createOrder(cmSupplyReqOrder);
            return super.responseSuccess("新增成功");
        } catch (Exception e) {
            return super.responseFail("系统错误: " + e.getMessage());
        }
    }

    /**
     * 取消订单
     * @param id
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/v1/cancelOrder/{id}")
    public String cancelOrder(@PathVariable Long id) {
        try {
            cmSupplyReqInfoService.cancelOrder(id);
            return super.responseSuccess("取消成功");
        } catch (Exception e) {
            return super.responseFail("系统错误: " + e.getMessage());
        }
    }

    /**
     * 订单支付状态改变
     * @param id
     * @param auditStatus
     * @return
     */
    @PostMapping("/v1/auditOrder/{id}")
    public String auditOrder(@PathVariable Long id,
                                @RequestParam Integer auditStatus, @RequestParam String reason) {
        try {
            cmSupplyReqInfoService.auditOrder(id, auditStatus,reason);
            return super.responseSuccess("修改成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }

    }



    @PostMapping("/v1/getInfoList")
    public String getAllPage(@RequestBody(required = false) PageQuery<CmInfoQueryDataDTO> pageQuery, HttpServletRequest req){
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
            // 设置默认页码和页大小，根据你的业务需求调整
            pageQuery.setPageNo(1);
            pageQuery.setPageSize(10);
        }
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        PageResult<CmInfoVo> pageResult = cmSupplyReqInfoService.getAllPage(pageQuery,loginUser.getUaaId());
        return super.responseSuccess(pageResult,"查询成功");
    }

    @GetMapping("/v1/selectIndexAllPage")
    public String selectIndexAllPage() {
        List<CmIndexSupplyDTO> supplyDTOS = cmSupplyReqInfoService.selectIndexAllPage();
        if(supplyDTOS == null){
            return super.responseFail("暂无");
        }
        return super.responseSuccess(supplyDTOS,"返回最新两条数据");
    }

    @GetMapping("/v1/selectSupplyPage")
    public String selectIndexSupplyPage() {
        List<CmIndexSupplyDTO> supplyDTOS = cmSupplyReqInfoService.selectIndexSupplyPage();
        if(supplyDTOS == null){
            return super.responseFail("暂无");
        }
        return super.responseSuccess(supplyDTOS,"返回最新两条数据");
    }

    @GetMapping("/v1/selectNeedPage")
    public String selectIndexNeedPage() {
        List<CmIndexSupplyDTO> supplyDTOS = cmSupplyReqInfoService.selectIndexNeedPage();
        if(supplyDTOS == null){
            return super.responseFail("暂无");
        }
        return super.responseSuccess(supplyDTOS,"返回最新两条数据");
    }

}
