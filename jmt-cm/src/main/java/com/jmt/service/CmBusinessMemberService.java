package com.jmt.service;

import cn.hutool.core.bean.BeanUtil;
import com.github.pagehelper.PageHelper;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.jmt.base.BaseService;
import com.jmt.client.UaaFeignClient;
import com.jmt.constant.JmtConstant;
import com.jmt.convertor.CmBusinessMemberConvertor;
import com.jmt.dao.CmBusinessMemberDao;
import com.jmt.dao.CmBusinessShopDao;
import com.jmt.model.cm.dto.CmBusinessMemberDTO;
import com.jmt.model.cm.dto.CmUserDTO;
import com.jmt.model.cm.entity.CmBusinessMember;
import com.jmt.model.cm.entity.CmBusinessShop;
import com.jmt.model.cm.entity.CmUser;
import com.jmt.model.cm.vo.CmBusinessMemberVO;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.uaa.UaaUser;
import com.jmt.model.uaa.dto.UaaUserDTO;
import feign.FeignException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class CmBusinessMemberService extends BaseService {

    @Resource
    private CmBusinessMemberDao cmBusinessMemberDao;

    @Resource
    private CmBusinessMemberConvertor cmBusinessMemberConvertor;

    @Resource
    private UaaFeignClient uaaFeignClient;

    @Resource
    private CmUserService cmUserService;

    @Resource
    private CmBusinessShopDao cmBusinessShopDao;

    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    public PageResult<CmBusinessMemberDTO> getPage(PageQuery<CmBusinessMemberDTO> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        CmBusinessMemberDTO cmFarmingFoodInfoDTO = pageQuery.getQueryData();
        List<CmBusinessMemberDTO> list = cmBusinessMemberDao.selectByCondition(cmFarmingFoodInfoDTO);
        if (list != null) {
            return new PageResult<>(list);
        }
        return null;
    }

    private UaaUser registerUaaUser(CmBusinessShop dto) {
        try {
            UaaUserDTO uaaUserDTO = new UaaUserDTO();
            BeanUtil.copyProperties(dto,uaaUserDTO,false);
            uaaUserDTO.setLoginName(dto.getTelPhone());
            uaaUserDTO.setNickname(JmtConstant.DEFAULT_NICKNAME + dto.getTelPhone());
            uaaUserDTO.setPassword(JmtConstant.DEFAULT_PASSWORD);

            String registerUser = uaaFeignClient.registerUser(uaaUserDTO);
            Gson gson = new Gson();
            JsonObject jsonObject = JsonParser.parseString(registerUser).getAsJsonObject();
            JsonElement dataElement = jsonObject.get("data");
            UaaUser user = gson.fromJson(dataElement, UaaUser.class);
            return user;
        } catch (FeignException e) {
            throw new RuntimeException("用户注册服务调用失败: " + e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException("用户注册失败: " + e.getMessage());
        }
    }
    @Transactional
    public int create(CmBusinessShop dto,String referrerNo) {
        //判断cm_user表是否有该手机号的用户
        CmUser userByPhone = cmUserService.getUserByPhone(dto.getTelPhone());
        if(userByPhone != null){
            throw new RuntimeException("该手机号已使用，请更换负责人手机号");
        }
            //没有 新增uaa_user 和 cm_user
            UaaUser uaaUser = registerUaaUser(dto);


            CmUserDTO cmUserDTO = new CmUserDTO();
            cmUserDTO.setUaaId(uaaUser.getUaaId());
            cmUserDTO.setUserName(uaaUser.getLoginName());
            cmUserDTO.setTelPhone(dto.getTelPhone());
            cmUserDTO.setRefereeNo(referrerNo);

            cmUserService.createUser(cmUserDTO);

            //BeanUtil.copyProperties(uaaUser,cmBusinessMember,false);
            int result = cmBusinessShopDao.insert(dto);

//        UaaUser uaaUser = registerUaaUser(dto);
//
//        CmBusinessMember cmBusinessMember = cmBusinessMemberConvertor.toEntity(dto);
//
//        BeanUtil.copyProperties(uaaUser,cmBusinessMember,false);
//
        return result;
    }

    @Transactional
    public int update(CmBusinessMemberDTO dto) {
        CmBusinessMember cmBusinessMember = cmBusinessMemberDao.selectById(dto.getId());
        if (cmBusinessMember == null) {
            throw new RuntimeException("员工不存在");
        }
        if (cmBusinessMember.getMemStatus() != 0) {
            throw new RuntimeException("只有在职员工可以修改");
        }

        CmBusinessMember cmBusinessMember1 = cmBusinessMemberConvertor.toEntity(dto);

        cmBusinessMember1.setUpdateTime(new Date());
        int result = cmBusinessMemberDao.update(cmBusinessMember1);
        return result;
    }


    @Transactional
    public int delete(Long id) {
        CmBusinessMember cmBusinessMember = cmBusinessMemberDao.selectById(id);
        if (cmBusinessMember == null) {
            throw new RuntimeException("成员不存在");
        }
        return cmBusinessMemberDao.delete(id);
    }

    @Transactional
    public CmBusinessMemberVO getById(Long id) {
        return cmBusinessMemberConvertor.toVo(cmBusinessMemberDao.selectById(id));
    }

    @Transactional
    public List<CmBusinessMemberVO> getByBusNo(String busNo) {
        return cmBusinessMemberConvertor.toVoList(cmBusinessMemberDao.selectByBusNo(busNo));
    }

    /**
     * 启用/禁用团队成员
     * @param id
     * @param auditStatus
     * @return
     */
    @Transactional
    public int auditBusinessMem(Long id, Integer auditStatus) {
        CmBusinessMember cmBusinessMember = cmBusinessMemberDao.selectById(id);
        if (cmBusinessMember == null) {
            throw new RuntimeException("员工不存在");
        }

        CmBusinessMember update = new CmBusinessMember();
        update.setId(id);
        update.setMemStatus(auditStatus);
        update.setUpdateTime(new Date());
        return cmBusinessMemberDao.update(update);
    }

}
