package com.jmt.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jmt.base.BaseService;
import com.jmt.client.EqFeignClient;
import com.jmt.client.JhhOperatorFeignClient;
import com.jmt.client.UaaFeignClient;
import com.jmt.constant.JmtConstant;
import com.jmt.dao.CmBusinessShopDao;
import com.jmt.dao.CmWorkEqApplyDao;
import com.jmt.dao.CmWorkEqApplyDetailDao;
import com.jmt.dao.CmWorkEqDeployDao;
import com.jmt.model.cm.dto.CmUserDTO;
import com.jmt.model.cm.dto.CmWorkEqApplyDetailDTO;
import com.jmt.model.cm.entity.CmBusinessShop;
import com.jmt.model.cm.entity.CmUser;
import com.jmt.model.cm.entity.CmWorkEqDeploy;
import com.jmt.model.cm.vo.*;
import com.jmt.model.eq.vo.EqListVo;
import com.jmt.model.jhh.vo.ShareProjectProfitShopVo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.uaa.UaaUser;
import com.jmt.model.uaa.UaaUserWallet;
import com.jmt.model.uaa.dto.UaaUserDTO;
import com.jmt.util.ResponseUtil;
import com.jmt.util.SysCodeGenerateUtil;
import feign.FeignException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

@Service
@Slf4j
public class CmBusinessShopService extends BaseService {

    @Resource
    private UaaFeignClient uaaFeignClient;

    @Resource
    private CmUserService cmUserService;

    @Resource
    private CmBusinessShopDao cmBusinessShopDao;

    @Resource
    private CmWorkEqApplyDao cmWorkEqApplyDao;

    @Resource
    private CmWorkEqApplyDetailDao cmWorkEqApplyDetailDao;

    @Resource
    private CmWorkEqDeployDao cmWorkEqDeployDao;

    @Resource
    private EqFeignClient eqFeignClient;

    @Resource
    private JhhOperatorFeignClient jhhOperatorFeignClient;



    private UaaUser registerUaaUser(CmBusinessShop dto) {
        try {
            UaaUserDTO uaaUserDTO = new UaaUserDTO();
            BeanUtil.copyProperties(dto,uaaUserDTO,false);
            uaaUserDTO.setLoginName(dto.getTelPhone());
            uaaUserDTO.setNickname(JmtConstant.DEFAULT_NICKNAME + dto.getTelPhone());
            uaaUserDTO.setPassword(JmtConstant.DEFAULT_PASSWORD);

            String registerUser = uaaFeignClient.registerUser(uaaUserDTO);
            UaaUser uaaUser = ResponseUtil.getData(registerUser,UaaUser.class);
            return uaaUser;
        } catch (FeignException e) {
            throw new RuntimeException("用户注册服务调用失败: " + e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException("用户注册失败: " + e.getMessage());
        }
    }
    @Transactional
    public int create(CmBusinessShop dto,CmUser cmUser) {
        //判断cm_user表是否有该手机号的用户
        CmUser userByPhone = cmUserService.getUserByPhone(dto.getTelPhone());
        if(userByPhone != null){
            throw new RuntimeException("该手机号已使用，请更换负责人手机号");
        }
            //没有 新增uaa_user 和 cm_user
        UaaUser uaaUser = null;
        try {
            uaaUser = registerUaaUser(dto);
            CmUserDTO cmUserDTO = new CmUserDTO();
            cmUserDTO.setUaaId(uaaUser.getUaaId());
            cmUserDTO.setUserName(uaaUser.getLoginName());
            cmUserDTO.setTelPhone(dto.getTelPhone());
            cmUserDTO.setParentId(cmUser.getId());
            cmUserDTO.setRefereeNo(cmUser.getRefereeNo());
            CmUser user = cmUserService.createShopUser(cmUserDTO);

            dto.setCreateTime(new Date());
            dto.setUpdateTime(new Date());
            dto.setUserId(user.getId());
            String shopNo=null;
            do {
                shopNo = SysCodeGenerateUtil.generateUserCode("JMT-C2");
                int count = cmBusinessShopDao.countByBusNo(shopNo);
                if (count == 0) {
                    break;
                }
            } while (true);
            dto.setShopNo(shopNo);
            dto.setIsDelete(0);
            dto.setRefereeNo(cmUser.getRefereeNo());
            int result = cmBusinessShopDao.insert(dto);

            return result;

        }catch (Exception e){
            // 进行补偿
            if (uaaUser != null) {
                uaaFeignClient.deleteUaa(uaaUser.getUaaId());
                log.warn("本地事务失败，已补偿删除远程UAA用户: {}", uaaUser.getUaaId());
            }
            throw new RuntimeException("创建店铺失败", e);
        }

    }


    @Transactional
    public int deleteBusinessShop(Long id) {
        CmBusinessShop businessShop = cmBusinessShopDao.selectById(id);
        if (businessShop == null) {
            throw new RuntimeException("门店不存在");
        }
        return cmBusinessShopDao.delete(id);
    }
    @Transactional
    public int updateBusinessShop(CmBusinessShop cmBusinessShop) {
        if (cmBusinessShop.getId() == null) {
            throw new IllegalArgumentException("门店ID不能为空");
        }
        cmBusinessShop.setUpdateTime(new Date());
        return cmBusinessShopDao.updateById(cmBusinessShop);
    }

    public CmBusinessShop getBusinessShopById(Long id) {
        CmBusinessShop businessShop = cmBusinessShopDao.selectById(id);
        if (businessShop == null) {
            throw new RuntimeException("门店不存在");
        }
        return businessShop;
    }
    public CmBusinessShop getShopById(Long id) {
        return cmBusinessShopDao.selectById(id);
    }

    public CmBusinessShop getShopByUserId(Long id) {
        return cmBusinessShopDao.selectByUserId(id);
    }

    /**
     * 根据ID查询门店详情及工单信息
     * @param id
     * @return
     */
    public CmBusinessShopDetailVO getShopDetailById(Long id) {
        CmBusinessShopDetailVO shopDetail = new CmBusinessShopDetailVO();
        CmBusinessShop cmBusinessShop = cmBusinessShopDao.selectById(id);
        if (cmBusinessShop == null) {
            return null;
        }
        BeanUtils.copyProperties(cmBusinessShop,shopDetail);

        ShopBaseInfo shopBaseInfo = cmBusinessShopDao.selectShopBaseInfoById(id);

        CmWorkEqApplyDetailVO cmWorkEqApplyDetailVO = cmWorkEqApplyDao.selectWorkNoByShopNo(shopDetail.getShopNo());



        if (shopBaseInfo != null && cmWorkEqApplyDetailVO != null ) {
            cmWorkEqApplyDetailVO.setOpenTime(shopBaseInfo.getOpenTime());
            cmWorkEqApplyDetailVO.setMeasure(shopBaseInfo.getMeasure());
            cmWorkEqApplyDetailVO.setComsumeAvg(shopBaseInfo.getComsumeAvg());
            cmWorkEqApplyDetailVO.setVisitorAvg(shopBaseInfo.getVisitorAvg());
            cmWorkEqApplyDetailVO.setShopPhoto(shopBaseInfo.getShopPhoto());
            cmWorkEqApplyDetailVO.setShopInteriorPhoto(shopBaseInfo.getShopInteriorPhoto());
            List<CmWorkEqApplyDetailDTO> details = cmWorkEqApplyDetailDao.selectDetailByWorkNo(cmWorkEqApplyDetailVO.getWorkNo());
            cmWorkEqApplyDetailVO.setDetails(details);
            shopDetail.setCmWorkEqApplyDetailVO(cmWorkEqApplyDetailVO);


            //部署信息
            CmWorkEqDeploy cmWorkEqDeploy= cmWorkEqDeployDao.selectByShopNo(shopDetail.getShopNo());
            if (cmWorkEqDeploy != null && cmWorkEqDeploy.getWorkStatus() == 3) { //部署完成有数据
                CmEqDeployDetailVO cmEqDeployDetailVO =new CmEqDeployDetailVO();
                BeanUtils.copyProperties(cmWorkEqDeploy, cmEqDeployDetailVO);

                String info = eqFeignClient.getInfoByShopNo(shopDetail.getShopNo());
                List<EqListVo> eqListVo = ResponseUtil.getDataList(info, EqListVo.class);
                cmEqDeployDetailVO.setList(eqListVo);
                shopDetail.setCmEqDeployDetailVO(cmEqDeployDetailVO);
            }else{
                shopDetail.setCmEqDeployDetailVO(null);
            }

        } else {
            // 没有工单
            shopDetail.setCmEqDeployDetailVO(null);
        }


        String shopRatioByOPN = jhhOperatorFeignClient.getShopRatioByOPN(cmBusinessShop.getOperatorNo());
        List<ShareProjectProfitShopVo> shareProjectProfitShopVo = ResponseUtil.getDataList(shopRatioByOPN,ShareProjectProfitShopVo.class);
        shopDetail.setShareProjectProfitShopVoList(shareProjectProfitShopVo);

        MerchantPerformanceVO merchantPerformanceVO=new MerchantPerformanceVO();

        CmUser cmUser=cmUserService.getUserById(shopDetail.getUserId());
        String wallet = uaaFeignClient.getWalletByUaaId(cmUser.getUaaId());
        UaaUserWallet uaaUserWallet = ResponseUtil.getData(wallet, UaaUserWallet.class);

        merchantPerformanceVO.setPointsBalance(uaaUserWallet.getPoints());
        merchantPerformanceVO.setWithdrawalBalance(uaaUserWallet.getBalance());

        shopDetail.setMerchantPerformanceVO(merchantPerformanceVO);

        return shopDetail;
    }

    public CmBusinessShop selectByUserId(Long id) {
        CmBusinessShop businessShop = cmBusinessShopDao.selectByUserId(id);
        if (businessShop == null) {
            throw new RuntimeException("门店不存在");
        }
        return businessShop;
    }



    public List<CmBusinessShop> getMyBusinessShopList(String busNo) {
        List<CmBusinessShop> list = cmBusinessShopDao.selectListByBusNo(busNo);
        return list;

    }

    public List<CmBusinessShopVO> getBusinessShopListByBusNo(String busNo) {
        List<CmBusinessShopVO> list = cmBusinessShopDao.getBusinessShopListByBusNo(busNo);
        return list;

    }
    public PageResult<CmApplyEqShopVO> getApplyEqShopListByOperator(PageQuery pageQuery, String operatorNo) {
        // 分页的门店ID
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<String> shopNos = cmWorkEqApplyDao.selectShopNosByOperator(operatorNo);

        if (CollectionUtils.isEmpty(shopNos)) {
            return new PageResult<>(Collections.emptyList());
        }

        PageInfo<String> pageInfo = new PageInfo<>(shopNos);

        List<CmApplyEqShopVO> resultList = cmWorkEqApplyDao.selectShopDetailsByShopNos(shopNos, operatorNo);

        PageResult<CmApplyEqShopVO> result = new PageResult<>();
        result.setTotal(pageInfo.getTotal());
        result.setList(resultList);
        result.setPageNo(pageQuery.getPageNo());
        result.setPageSize(pageQuery.getPageSize());
        result.setSize(resultList.size());
        result.setPages(pageInfo.getPages());

        return result;
    }

    public CmBusinessShopVO getShopByNo(String shopNo) {
        CmBusinessShopVO vo = cmBusinessShopDao.getShopByNo(shopNo);
        if (ObjectUtil.isEmpty(vo)) {
            vo = new CmBusinessShopVO();
        }
        return vo;
    }






}
