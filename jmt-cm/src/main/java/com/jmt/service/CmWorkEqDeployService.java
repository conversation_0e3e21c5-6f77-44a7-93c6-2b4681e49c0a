package com.jmt.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.reflect.TypeToken;
import com.jmt.base.BaseService;
import com.jmt.client.EqFeignClient;
import com.jmt.convertor.CmWorkEqDeployConvertor;
import com.jmt.dao.CmWorkEqDeployDao;
import com.jmt.model.cm.dto.CmWorkEqDeployCreateDTO;
import com.jmt.model.cm.dto.CmWorkEqDeployDTO;
import com.jmt.model.cm.dto.WorkOrderDTO;
import com.jmt.model.cm.entity.CmWorkEqDeploy;
import com.jmt.model.eq.dto.EqInfo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.cm.vo.CmWorkEqDeployVO;
import com.jmt.util.GsonUtil;
import com.jmt.util.SysCodeGenerateUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class CmWorkEqDeployService extends BaseService {

    @Resource
    private CmWorkEqDeployDao cmWorkEqDeployDao;

    @Resource
    private CmWorkEqDeployConvertor cmWorkEqDeployConvertor;

    @Resource
    private EqFeignClient eqFeignClient;

    @Resource
    private CmWorkEqApplyService cmWorkEqApplyService;

    @Transactional
    public int create(CmWorkEqDeployCreateDTO dto) {
        //申请工单设置状态为 同意
        cmWorkEqApplyService.auditBusiness(dto.getId(),1,"同意");

        CmWorkEqDeploy cmWorkEqDeploy = new CmWorkEqDeploy();

        BeanUtils.copyProperties(dto, cmWorkEqDeploy);

        String workNo = SysCodeGenerateUtil.generateWorkOrderCode("002");

        cmWorkEqDeploy.setWorkNo(workNo);
        cmWorkEqDeploy.setWorkStatus(0);
        cmWorkEqDeploy.setCreateTime(new Date());
        cmWorkEqDeploy.setUpdateTime(new Date());
        cmWorkEqDeploy.setIsDelete(0);

        int result = cmWorkEqDeployDao.insert(cmWorkEqDeploy);
        return result;
    }

    @Transactional
    public int update(CmWorkEqDeployDTO dto) {
        // 验证工单是否存在
        CmWorkEqDeploy cmWorkEqDeploy = cmWorkEqDeployDao.selectById(dto.getId());
        if (cmWorkEqDeploy == null) {
            throw new RuntimeException("工单不存在");
        }
        // 只能修改待审核状态的工单
        if (cmWorkEqDeploy.getWorkStatus() != 0) {
            throw new RuntimeException("只有待审核状态的工单可以修改");
        }

        CmWorkEqDeploy cmWorkEq = cmWorkEqDeployConvertor.toEntity(dto);

        cmWorkEq.setUpdateTime(new Date());
        int result = cmWorkEqDeployDao.update(cmWorkEq);
        return result;
    }


    @Transactional
    public int delete(Long id) {
        CmWorkEqDeploy cmWorkEqDeploy = cmWorkEqDeployDao.selectById(id);
        if (cmWorkEqDeploy == null) {
            throw new RuntimeException("工单不存在");
        }
        return cmWorkEqDeployDao.delete(id);
    }

    @Transactional
    public CmWorkEqDeployVO getById(Long id) {
        return cmWorkEqDeployConvertor.toVo(cmWorkEqDeployDao.selectById(id));
    }

    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    public PageResult<CmWorkEqDeployDTO> getPage(PageQuery<WorkOrderDTO> pageQuery, String operatorNo) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        WorkOrderDTO queryDTO = pageQuery.getQueryData();
        List<CmWorkEqDeployDTO> list = cmWorkEqDeployDao.selectByCondition(
                queryDTO.getWorkStatus(),
                queryDTO.getTimeScope(),operatorNo);

        Page<CmWorkEqDeployDTO> page = (Page<CmWorkEqDeployDTO>) list;
        PageResult<CmWorkEqDeployDTO> result = new PageResult<>();
        result.setList(page.getResult());
        result.setTotal(page.getTotal());
        result.setPageNo(page.getPageNum());
        result.setPageSize(page.getPageSize());
        result.setPages(page.getPages());
        result.setSize(page.size());

        return result;
    }
//    public PageResult<CmWorkEqDeployDTO> getPage(PageQuery<CmWorkEqDeployDTO> pageQuery) {
//        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
//        CmWorkEqDeployDTO cmWorkEqDeployDTO = pageQuery.getQueryData();
//        List<CmWorkEqDeployDTO> list = cmWorkEqDeployDao.selectByCondition(cmWorkEqDeployDTO);
//        if (list != null) {
//            return new PageResult<>(list);
//        }
//        return null;
//    }

    /**
     * 审核工单
     * @param id
     * @param auditStatus
     * @return
     */
    @Transactional
    public int auditBusiness(Long id, Integer auditStatus, String reason) {
        CmWorkEqDeploy cmWorkEqDeploy = cmWorkEqDeployDao.selectById(id);
        if (cmWorkEqDeploy == null) {
            throw new RuntimeException("工单不存在");
        }
        if (auditStatus == 2 && StringUtils.isBlank(reason)) {
            throw new RuntimeException("驳回时必须填写原因");
        }

        if (cmWorkEqDeploy.getWorkStatus() != 0) {
            throw new RuntimeException("只能审核待审核状态的工单");
        }

        CmWorkEqDeploy update = new CmWorkEqDeploy();
        update.setId(id);
        update.setWorkStatus(auditStatus);
        update.setReason(reason);
        update.setUpdateTime(new Date());

        return cmWorkEqDeployDao.update(update);
    }
    /**
     * 部署工单自动确定
     * @param id
     * @return
     */
    @Transactional
    public int autoCompleteDeployOrder(Long id) {
        CmWorkEqDeploy deployOrder = cmWorkEqDeployDao.selectById(id);
        if (deployOrder == null) {
            throw new RuntimeException("工单不存在");
        }
        if (deployOrder.getWorkStatus() != 1) {
            throw new RuntimeException("只有状态为'同意'的工单可自动完成");
        }

        String busNo = deployOrder.getBusNo();
        String infoByBusNo = eqFeignClient.getInfoByBusNo(busNo);

        JsonObject jsonObject = JsonParser.parseString(infoByBusNo).getAsJsonObject();
        String dataStr = jsonObject.get("data").toString();


        List<EqInfo> eqInfoList = GsonUtil.fromJson(dataStr, new TypeToken<List<EqInfo>>(){});
        // 判断是否有设备在线
        boolean hasOnlineDevice = eqInfoList.size() > 0;
        if (hasOnlineDevice) {
            CmWorkEqDeploy update = new CmWorkEqDeploy();
            update.setId(id);
            update.setWorkStatus(3);
            update.setUpdateTime(new Date());
            return cmWorkEqDeployDao.update(update);
        } else {
            throw new RuntimeException("当前商家没有在线设备，无法完成工单");
        }
    }
    /**
     * 查询运营商下已部署设备数量
     *
     * @param
     * @return
     */
    public Integer getDeployDeviceCount(String operatorNo) {
        return cmWorkEqDeployDao.getDeployDeviceCount(operatorNo);
    }
    /**
     * 获取待处理工单数量
     * @param operatorNo
     * @return
     */
    public Integer getDeployDeviceWaitCountForReady(String operatorNo) {
        return cmWorkEqDeployDao.getDeployDeviceWaitCount(operatorNo);
    }
}
