package com.jmt.service;

import cn.hutool.core.convert.Convert;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.client.ProfitFeignClient;
import com.jmt.client.UaaFeignClient;
import com.jmt.convertor.CmSupplyReqInfoConvertor;
import com.jmt.dao.*;
import com.jmt.enums.PayTypeEnum;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.cm.dto.CmInfoQueryDataDTO;
import com.jmt.model.cm.dto.CmIndexSupplyDTO;
import com.jmt.model.cm.entity.*;
import com.jmt.model.cm.vo.CmInfoVo;
import com.jmt.model.cm.vo.PubBusInfoVO;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.cm.dto.CmSupplyReqInfoDTO;
import com.jmt.model.cm.vo.CmSupplyReqInfoVO;
import com.jmt.model.profit.dto.BalancePayDto;
import com.jmt.model.profit.dto.PointPayDTO;
import com.jmt.model.uaa.UaaUserWallet;
import com.jmt.util.GsonUtil;
import com.jmt.util.ResponseUtil;
import com.jmt.util.SysCodeGenerateUtil;
import com.jmt.util.WorkNoGeneratorUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
@Slf4j
@Service
public class CmSupplyReqInfoService extends BaseService {
    @Resource
    private CmSupplyReqInfoDao cmSupplyReqInfoDao;

    @Resource
    private CmSupplyReqAuditDao cmSupplyReqAuditDao;

    @Resource
    private CmSupplyReqOrderDao cmSupplyReqOrderDao;

    @Resource
    private CmSupplyReqInfoConvertor cmSupplyReqInfoConvertor;

    @Resource
    private CmUserDao cmUserDao;

    @Resource
    private CmBusinessInfoDao cmBusinessInfoDao;

    @Resource
    private CmBusinessShopDao cmBusinessShopDao;


    @Resource
    private ProfitFeignClient profitFeignClient;

    @Resource
    private UaaFeignClient uaaFeignClient;


/*
 *发布供需 （商家发 / 门店发）
 * 必传参数 userId(可不传 通过token查cmUser), title, content,picture,video,province,city,infoType,payType, payAmount,password
 *
 * */
    @Transactional
    public Long create(CmSupplyReqInfoDTO dto, LoginUaaUser loginUser) {
        CmSupplyReqInfo cmSupplyReqInfo = new CmSupplyReqInfo();
        BeanUtils.copyProperties(dto, cmSupplyReqInfo);

        Long uaaId = loginUser.getUaaId();
        CmUser user = cmUserDao.selectByUaaId(uaaId);
        cmSupplyReqInfo.setUserId(user.getId());
        cmSupplyReqInfo.setAuditStatus(0);
        cmSupplyReqInfo.setCreateTime(new Date());
        cmSupplyReqInfo.setUpdateTime(new Date());
        cmSupplyReqInfo.setIsDelete(0);

        String shopName = null;
        if( user.getRoleType() == 1){
            shopName = cmBusinessInfoDao.selectBusNameByUserId(user.getId());
        }else if(user.getRoleType() == 2){
            shopName = cmBusinessShopDao.selectBusNameByUserId(user.getId());
        }
        cmSupplyReqInfo.setBusName(shopName);
        cmSupplyReqInfo.setVisitCount(0);
        cmSupplyReqInfo.setContactCount(0);
        cmSupplyReqInfo.setMessageCount(0);
        cmSupplyReqInfo.setDealCount(0);

        Long result = cmSupplyReqInfoDao.insert(cmSupplyReqInfo);
        Long generatedId = cmSupplyReqInfo.getId();

        CmSupplyReqAudit audit = new CmSupplyReqAudit();
        audit.setInfoId(generatedId);
        audit.setAuditStatus(0);
        audit.setCreateTime(new Date());
        audit.setUpdateTime(new Date());
        audit.setIsDelete(0);
        cmSupplyReqAuditDao.insert(audit);

        //创建订单
        CmSupplyReqOrder order=new CmSupplyReqOrder();
        order.setCreateTime(new Date());
        order.setUpdateTime(new Date());
        order.setIsDelete(0);
        String orderNo = null;
        do {
            orderNo = SysCodeGenerateUtil.generateProfitCode("002"); // 信息发布
            int count = cmSupplyReqOrderDao.countByOrderNo(orderNo);
            if (count == 0) {
                break;
            }
        } while (true); // 生成唯一编号

        order.setOrderNo(orderNo);
        order.setInfoId(generatedId);
        order.setOrderName(dto.getTitle());
        order.setPayType(dto.getPayType());
        order.setPayAmount(dto.getPayAmount());
        order.setUserId(user.getId());

        //校验是否设置支付密码
        String walletRes =uaaFeignClient.getWalletByUaaId(uaaId);
        UaaUserWallet wallet= ResponseUtil.getData(walletRes,UaaUserWallet.class);
        if (wallet == null) {
            throw new RuntimeException("用户钱包不存在");
        }
        if (StringUtils.isEmpty(wallet.getPayPassword())){
            log.error("未设置密码");
            throw new RuntimeException("未设置密码");
        }

        //去支付
        PayTypeEnum payType = PayTypeEnum.getByCode(dto.getPayType());
        switch (payType) {
            case POINTS_PAY:
                PointPayDTO pointPayDTO = new PointPayDTO();
                pointPayDTO.setUaaId(uaaId);
                pointPayDTO.setPoints(dto.getPayAmount().intValue());
                pointPayDTO.setBusUaaId(1L);
                pointPayDTO.setOrderNo(order.getOrderNo());
                pointPayDTO.setPayPassword(dto.getPassword());
                String payRes = profitFeignClient.pointsByUaaIdPay(pointPayDTO);
                Map<String, Object> responseMap = GsonUtil.fromJsonMap(payRes);
                String code = Convert.toStr(responseMap.get("code"));
                if(!"200".equals(code)){
                    String errorMsg = Convert.toStr(responseMap.get("msg"));
                    throw new RuntimeException(errorMsg);
                }

                break;
            case BALANCE_PAY:
                BalancePayDto payDTO = new BalancePayDto();
                payDTO.setUaaId(uaaId);
                payDTO.setBalance(dto.getPayAmount());
                payDTO.setBusUaaId(1L);
                payDTO.setOrderNo(order.getOrderNo());
                payDTO.setPayPassword(dto.getPassword());
                String balPayRes = profitFeignClient.balanceByUaaIdPay(payDTO);
                Map<String, Object> balResponseMap = GsonUtil.fromJsonMap(balPayRes);
                String balCode = Convert.toStr(balResponseMap.get("code"));
                if(!"200".equals(balCode)){
                    String errorMsg = Convert.toStr(balResponseMap.get("msg"));
                    throw new RuntimeException(errorMsg);
                }

                break;
//                case WECHAT_PAY:
//                    break;
//                case ALIPAY_PAY:
//                    break;
            default:
                throw new RuntimeException("不支持的支付方式: " + payType.getMessage());
        }
        order.setOrderStatus(1);
        cmSupplyReqOrderDao.insert(order);
        return result;
    }

    @Transactional
    public int update(CmSupplyReqInfoDTO dto) {
        CmSupplyReqInfo cmSupplyReqInfo = cmSupplyReqInfoDao.selectById(dto.getId());
        if (cmSupplyReqInfo == null) {
            throw new RuntimeException("工单不存在");
        }
        if (cmSupplyReqInfo.getAuditStatus() != 0) {
            throw new RuntimeException("只有待审核状态的工单可以修改");
        }

        CmSupplyReqInfo cmWorkEq = cmSupplyReqInfoConvertor.toEntity(dto);
        cmWorkEq.setUpdateTime(new Date());
        return cmSupplyReqInfoDao.update(cmWorkEq);
    }


    @Transactional
    public int delete(Long id) {
        CmSupplyReqInfo cmSupplyReqInfo = cmSupplyReqInfoDao.selectById(id);
        if (cmSupplyReqInfo == null) {
            throw new RuntimeException("工单不存在");
        }
        return cmSupplyReqInfoDao.delete(id);
    }

    @Transactional
    public CmSupplyReqInfoVO getByWithVisitId(Long id) {
        cmSupplyReqInfoDao.incrementVisitCount(id);
        CmSupplyReqInfoDTO dto = cmSupplyReqInfoDao.selectDtoById(id);
        if (dto == null) {
            return null;
        }
        CmSupplyReqInfoVO vo = new CmSupplyReqInfoVO();
        BeanUtils.copyProperties(dto, vo);
        if (dto.getUserId() != null) {
            CmUser user = cmUserDao.selectById(dto.getUserId());
            PubBusInfoVO pubBusInfoVO = new PubBusInfoVO();
            if(user.getRoleType() == 1){ //商家
                pubBusInfoVO = cmBusinessInfoDao.selectBusInfoByUserId(dto.getUserId());
            }else if(user.getRoleType() == 2){ //门店
                pubBusInfoVO = cmBusinessShopDao.selectBusInfoByUserId(dto.getUserId());
            }


            if (user != null && pubBusInfoVO != null) {
                pubBusInfoVO.setNickName(user.getNickName());
                pubBusInfoVO.setHeadImg(user.getHeadImg());
                pubBusInfoVO.setCategoryName(pubBusInfoVO.getCategoryId());
            }
            vo.setPubBusInfoVO(pubBusInfoVO);
        }

        return vo;
    }

    @Transactional
    public CmSupplyReqInfoVO getById(Long id) {
        CmSupplyReqInfoDTO dto = cmSupplyReqInfoDao.selectDtoById(id);
        if (dto == null) {
            return null;
        }
        CmSupplyReqInfoVO vo = new CmSupplyReqInfoVO();
        BeanUtils.copyProperties(dto, vo);
        if (dto.getUserId() != null) {
            CmUser user = cmUserDao.selectById(dto.getUserId());
            PubBusInfoVO pubBusInfoVO=new PubBusInfoVO();
            if(user.getRoleType() == 1){ //商家
                pubBusInfoVO = cmBusinessInfoDao.selectBusInfoByUserId(dto.getUserId());
            }else if(user.getRoleType() == 2){ //门店
                pubBusInfoVO = cmBusinessShopDao.selectBusInfoByUserId(dto.getUserId());
            }


            if (user != null && pubBusInfoVO != null) {
                pubBusInfoVO.setNickName(user.getNickName());
                pubBusInfoVO.setHeadImg(user.getHeadImg());
                pubBusInfoVO.setCategoryName(pubBusInfoVO.getCategoryId());
            }
            vo.setPubBusInfoVO(pubBusInfoVO);
        }

        return vo;
    }
    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    public PageResult<CmSupplyReqInfoVO> getPage(PageQuery<CmSupplyReqInfoDTO> pageQuery) {
        int pageNo = pageQuery.getPageNo() == null ? 1 : pageQuery.getPageNo(); // 默认第1页
        int pageSize = pageQuery.getPageSize() == null ? 10 : pageQuery.getPageSize(); // 默认10条/页
        pageNo = Math.max(pageNo, 1); // 页码最小为1
        pageSize = Math.max(Math.min(pageSize, 100), 1); // 页大小限制1~100条（防过大查询）

        PageHelper.startPage(pageNo, pageSize);
        CmSupplyReqInfoDTO queryDTO = pageQuery.getQueryData() != null ? pageQuery.getQueryData() : new CmSupplyReqInfoDTO();
        List<CmSupplyReqInfoDTO> dtoList = cmSupplyReqInfoDao.selectByCondition(queryDTO);

        Page<CmSupplyReqInfoDTO> dtoPage = (Page<CmSupplyReqInfoDTO>) dtoList;

        // 关联查询商户/用户信息
        List<CmSupplyReqInfoVO> voList = dtoPage.stream().map(dto -> {
            CmSupplyReqInfoVO vo = new CmSupplyReqInfoVO();
            BeanUtils.copyProperties(dto, vo);

            // 关联查询
            if (dto.getUserId() != null) {
                CmUser user = cmUserDao.selectById(dto.getUserId());
                PubBusInfoVO pubBusInfoVO=new PubBusInfoVO();
                if(user.getRoleType() == 1){
                    pubBusInfoVO = cmBusinessInfoDao.selectBusInfoByUserId(dto.getUserId());
                }else if(user.getRoleType() == 2){
                    pubBusInfoVO = cmBusinessShopDao.selectBusInfoByUserId(dto.getUserId());
                }


                if (user != null && pubBusInfoVO != null) {
                    pubBusInfoVO.setNickName(user.getNickName());
                    pubBusInfoVO.setHeadImg(user.getHeadImg());
                    pubBusInfoVO.setCategoryName(pubBusInfoVO.getCategoryId());
                }
                vo.setPubBusInfoVO(pubBusInfoVO);
            }
            return vo;
        }).collect(Collectors.toList());

        return new PageResult<>(dtoPage, voList);
    }

    /**
     * 审核工单
     * @param id
     * @param auditStatus
     * @return
     */
    @Transactional
    public int auditBusiness(Long id, Integer auditStatus, String reason, Long auditUserId) {
        CmSupplyReqInfo cmSupplyReqInfo = cmSupplyReqInfoDao.selectById(id);
        if (cmSupplyReqInfo == null) {
            throw new RuntimeException("工单不存在");
        }

        if (auditStatus == 2 && StringUtils.isBlank(reason)) {
            throw new RuntimeException("驳回时必须填写原因");
        }

        if (cmSupplyReqInfo.getAuditStatus() != 0) {
            throw new RuntimeException("只能审核待审核状态的工单");
        }

        // 更新工单审核状态
        CmSupplyReqInfo update = new CmSupplyReqInfo();
        update.setId(id);
        update.setAuditStatus(auditStatus);
        update.setReason(reason);
        update.setUpdateTime(new Date());
        int updateCount = cmSupplyReqInfoDao.update(update);

        // 查询审核记录
        CmSupplyReqAudit existingAudit = cmSupplyReqAuditDao.selectByInfoId(id);

        // 更新审核表
        CmSupplyReqAudit audit = new CmSupplyReqAudit();
        audit.setInfoId(id);
        audit.setAuditStatus(auditStatus);
        audit.setReason(reason);
        audit.setCreateTime(new Date());
        audit.setUpdateTime(new Date());
        audit.setAuditUser(auditUserId);
        audit.setIsDelete(0);
        audit.setId(existingAudit.getId());
        cmSupplyReqAuditDao.update(audit);

        return updateCount;
    }

    /**
     * 创建订单
     * @param order
     */
    @Transactional
    public void createOrder(CmSupplyReqOrder order) {
        order.setOrderStatus(0);
        order.setCreateTime(new Date());
        order.setUpdateTime(new Date());
        order.setIsDelete(0);
        String orderNo = WorkNoGeneratorUtil.generate("RNO");
        order.setOrderNo(orderNo);
        cmSupplyReqOrderDao.insert(order);
    }

    /**
     * 取消订单
     * @param id
     */
    @Transactional
    public void cancelOrder(Long id) {
        CmSupplyReqOrder order = cmSupplyReqOrderDao.selectById(id);
        if (order == null || order.getIsDelete() == 1) {
            throw new RuntimeException("订单不存在或已被删除");
        }
        order.setOrderStatus(3); //取消支付
        order.setUpdateTime(new Date());
        cmSupplyReqOrderDao.update(order);
    }

    /**
     * 订单支付状态改变
     * @param id
     * @param auditStatus
     * @param reason
     * @return
     */
    @Transactional
    public int auditOrder(Long id, Integer auditStatus, String reason) {
        CmSupplyReqOrder order = cmSupplyReqOrderDao.selectById(id);
        if (order == null) {
            throw new RuntimeException("工单不存在");
        }

        if (auditStatus == 2 && StringUtils.isBlank(reason)) {
            throw new RuntimeException("驳回时必须填写原因");
        }

        if (order.getOrderStatus() != 0) {
            throw new RuntimeException("只能审核待审核状态的工单");
        }

        // 更新工单支付状态
        CmSupplyReqOrder update = new CmSupplyReqOrder();
        update.setId(id);
        update.setOrderStatus(auditStatus);
        update.setReason(reason);
        update.setUpdateTime(new Date());

        return cmSupplyReqOrderDao.update(update);
    }

    public List<CmSupplyReqInfoVO> getListByUaaId(Long uaaId) {
        CmUser myUser = cmUserDao.selectByUaaId(uaaId);
        if(myUser == null){
            throw new RuntimeException("用户不存在");
        }

        List<CmSupplyReqInfoDTO> cmSupplyReqInfoDTOS = cmSupplyReqInfoDao.selectListByUserId(myUser.getId());
        if (CollectionUtils.isEmpty(cmSupplyReqInfoDTOS)) {
            return Collections.emptyList();
        }
        List<CmSupplyReqInfoVO> voList = cmSupplyReqInfoDTOS.stream().map(dto -> {
            CmSupplyReqInfoVO vo = new CmSupplyReqInfoVO();
            BeanUtils.copyProperties(dto, vo);

            // 关联查询
            if (dto.getUserId() != null) {
                PubBusInfoVO pubBusInfoVO = new PubBusInfoVO();
                if(myUser.getRoleType() == 1){
                    pubBusInfoVO = cmBusinessInfoDao.selectBusInfoByUserId(dto.getUserId());
                }else if( myUser.getRoleType() == 2){
                    pubBusInfoVO = cmBusinessShopDao.selectBusInfoByUserId(dto.getUserId());
                }
                if ( pubBusInfoVO != null) {
                    pubBusInfoVO.setNickName(myUser.getNickName());
                    pubBusInfoVO.setHeadImg(myUser.getHeadImg());
                    pubBusInfoVO.setCategoryName(pubBusInfoVO.getCategoryId());
                }
                vo.setPubBusInfoVO(pubBusInfoVO);
            }
            return vo;
        }).collect(Collectors.toList());
        return voList;
    }


public PageResult<CmInfoVo> getAllPage(PageQuery<CmInfoQueryDataDTO> pageQuery, Long uaaId) {
    CmUser myUser = cmUserDao.selectByUaaId(uaaId);

    String sourceType = null;
    if (pageQuery.getQueryData() != null) {
        sourceType = pageQuery.getQueryData().getSourceType();
    }

    // 将startPage()放在真正要分页的查询之前
    Page<CmInfoVo> page = PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());

    // 执行查询
    List<CmInfoVo> list = cmSupplyReqInfoDao.selectAllInfo(myUser.getId(), sourceType);

    PageResult<CmInfoVo> result = new PageResult<>();
    result.setList(list);
    result.setTotal(page.getTotal());
    result.setPageNo(page.getPageNum());
    result.setPageSize(page.getPageSize());
    result.setPages(page.getPages());
    result.setSize(page.size());

    return result;
}

    public List<CmIndexSupplyDTO> selectIndexSupplyPage(){
        return cmSupplyReqInfoDao.selectIndexSupplyPage();
    }

    public List<CmIndexSupplyDTO> selectIndexNeedPage(){
        return cmSupplyReqInfoDao.selectIndexNeedPage();
    }
    public List<CmIndexSupplyDTO> selectIndexAllPage(){
        List<CmIndexSupplyDTO> result = new ArrayList<>();

        // 获取供应数据
        List<CmIndexSupplyDTO> supplyList = cmSupplyReqInfoDao.selectIndexSupplyPage();
        if (supplyList != null) {
            result.addAll(supplyList);
        }

        // 获取需求数据
        List<CmIndexSupplyDTO> needList = cmSupplyReqInfoDao.selectIndexNeedPage();
        if (needList != null) {
            result.addAll(needList);
        }

        return result;
    }

}
