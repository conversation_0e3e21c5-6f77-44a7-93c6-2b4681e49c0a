package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.convertor.CmUserConvertor;
import com.jmt.dao.CmBusinessInfoDao;
import com.jmt.dao.CmBusinessShopDao;
import com.jmt.dao.CmUserDao;
import com.jmt.model.cm.dto.CmUserDTO;
import com.jmt.model.cm.entity.CmBusinessInfo;
import com.jmt.model.cm.entity.CmBusinessShop;
import com.jmt.model.cm.entity.CmUser;
import com.jmt.model.cm.vo.CmUserBaseVO;
import com.jmt.model.cm.vo.CmUserVO;
import com.jmt.model.jhh.dto.TransferSalesmanEqDto;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class CmUserService extends BaseService {

    @Resource
    private CmUserDao cmUserDao;
    @Resource
    private CmUserConvertor cmUserConvertor;

    @Resource
    private CmBusinessInfoDao cmBusinessInfoDao;

    @Resource
    private CmBusinessShopDao cmBusinessShopDao;

    /**
     * 新增用户
     */
    @Transactional
    public CmUser createUser(CmUserDTO cmUserDTO) {
        CmUser cmUser = cmUserConvertor.toEntity(cmUserDTO);
        cmUser.setCreateTime(new Date());
        cmUser.setUpdateTime(new Date());
        cmUser.setIsFirstLogin(1);
        cmUser.setIsDelete(0);
        cmUser.setRoleType(0); //游客
        cmUser.setIsSupplier(0);
        cmUserDao.insert(cmUser);
        return cmUser;
    }

    /**
     * 创建门店负责人用户
     * @param cmUserDTO
     * @return
     */
    @Transactional
    public CmUser createShopUser(CmUserDTO cmUserDTO) {
        CmUser cmUser = cmUserConvertor.toEntity(cmUserDTO);
        cmUser.setCreateTime(new Date());
        cmUser.setUpdateTime(new Date());
        cmUser.setIsFirstLogin(1);
        cmUser.setIsDelete(0);
        cmUser.setParentId(cmUserDTO.getParentId());
        cmUser.setRoleType(2);
        cmUser.setIsSupplier(0);
        cmUserDao.insert(cmUser);
        return cmUser;
    }

    /**
     * 更新用户信息
     */
    @Transactional
    public Integer updateByUaaId(CmUser cmUser) {
        cmUser.setUpdateTime(new Date());
        return cmUserDao.updateByUaaId(cmUser);
    }

    /**
     * 删除用户
     */
    @Transactional
    public int deleteUser(Long id) {
        CmUser cmUser = cmUserDao.selectById(id);
        if (cmUser == null) {
            throw new RuntimeException("用户不存在");
        }
        return cmUserDao.deleteById(id);
    }

    /**
     * 根据ID查询用户
     */
    @Transactional
    public CmUser getUserById(Long id) {
        return cmUserDao.selectById(id);
    }
    @Transactional
    public CmUser getUserByUaaId(Long uaaId) {
        return cmUserDao.selectByUaaId(uaaId);
    }

    public CmUserBaseVO getUserVoByUaaId(Long uaaId) {
        CmUser cmUser = cmUserDao.selectByUaaId(uaaId);
        CmUserBaseVO cmUserBaseVO=new CmUserBaseVO();
        BeanUtils.copyProperties(cmUser,cmUserBaseVO);
        if(cmUser.getRoleType()==1){
            CmBusinessInfo cmBusinessInfo=cmBusinessInfoDao.selectByUserId(cmUser.getId());
            cmUserBaseVO.setBusName(cmBusinessInfo.getBusName());
            cmUserBaseVO.setBusNo(cmBusinessInfo.getBusNo());
        }else if(cmUser.getRoleType()==2){
            CmBusinessShop cmBusinessShop=cmBusinessShopDao.selectByUserId(cmUser.getId());
            cmUserBaseVO.setShopName(cmBusinessShop.getShopName());
            cmUserBaseVO.setShopNo(cmBusinessShop.getShopNo());
        }

        return cmUserBaseVO;
    }

    /**
     * 更新cm用户为商家类型
     * @param userId
     * @param roleType
     * @return
     */
    @Transactional
    public int updateUserRoleType(Long userId, Integer roleType) {
        if (userId == null || roleType == null) {
            throw new IllegalArgumentException("用户ID和角色类型不能为空");
        }

        CmUser user = new CmUser();
        user.setId(userId);
        user.setRoleType(roleType);
        user.setParentId(0L);
        user.setUpdateTime(new Date());

        return cmUserDao.updateById(user);
    }
    /**
     * 获取游客列表
     *
     * @param operatorNo
     * @return
     */
    public List<CmUser> getUserList(String operatorNo) {
        return cmUserDao.selectByOperatorNo(operatorNo);
    }

    public PageResult<CmUserVO> getPageByOperatorNo(PageQuery pageQuery,String operatorNo) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<CmUserVO> list = cmUserDao.selectVoByOperatorNo(operatorNo);
        if (list != null) {
            return new PageResult<>(list);
        }
        return null;
    }
    public CmUser getUserByPhone(String phone) {
        return cmUserDao.selectByPhone(phone);
    }

    public Integer getUserNum(String operatorNo) {
        return cmUserDao.getUserNum(operatorNo);
    }

    public String getUserAddress(Long uaaId) {
        return cmUserDao.getUserAddress(uaaId);
    }

    public void transferSalesman(TransferSalesmanEqDto transferSalesmanEqDto) {
        cmUserDao.transferSalesman(transferSalesmanEqDto);
    }
}
