package com.jmt.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.convertor.CmWorkEqRecallConvertor;
import com.jmt.dao.CmWorkEqRecallDao;
import com.jmt.model.cm.dto.WorkOrderDTO;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.cm.dto.CmWorkEqRecallDTO;
import com.jmt.model.cm.entity.CmWorkEqRecall;
import com.jmt.model.cm.vo.CmWorkEqRecallVO;
import com.jmt.util.SysCodeGenerateUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class CmWorkEqRecallService extends BaseService {

    @Resource
    private CmWorkEqRecallDao cmWorkEqRecallDao;

    @Resource
    private CmWorkEqRecallConvertor cmWorkEqRecallConvertor;
    @Transactional
    public int create(CmWorkEqRecallDTO dto) {
        CmWorkEqRecall cmWorkEqRecall = cmWorkEqRecallConvertor.toEntity(dto);
        String workNo = SysCodeGenerateUtil.generateWorkOrderCode("004");

        cmWorkEqRecall.setWorkNo(workNo);
        cmWorkEqRecall.setWorkStatus(0);
        cmWorkEqRecall.setCreateTime(new Date());
        cmWorkEqRecall.setUpdateTime(new Date());
        cmWorkEqRecall.setIsDelete(0);
        // 先插入主表
        int result = cmWorkEqRecallDao.insert(cmWorkEqRecall);
        return result;
    }

    @Transactional
    public int update(CmWorkEqRecallDTO dto) {
        // 验证工单是否存在
        CmWorkEqRecall cmWorkEqRecall = cmWorkEqRecallDao.selectById(dto.getId());
        if (cmWorkEqRecall == null) {
            throw new RuntimeException("工单不存在");
        }
        // 只能修改待审核状态的工单
        if (cmWorkEqRecall.getWorkStatus() != 0) {
            throw new RuntimeException("只有待审核状态的工单可以修改");
        }

        CmWorkEqRecall cmWorkEq = cmWorkEqRecallConvertor.toEntity(dto);

        cmWorkEq.setUpdateTime(new Date());
        int result = cmWorkEqRecallDao.update(cmWorkEq);
        return result;
    }


    @Transactional
    public int delete(Long id) {
        CmWorkEqRecall cmWorkEqRecall = cmWorkEqRecallDao.selectById(id);
        if (cmWorkEqRecall == null) {
            throw new RuntimeException("工单不存在");
        }
        return cmWorkEqRecallDao.delete(id);
    }

    @Transactional
    public CmWorkEqRecallVO getById(Long id) {
        return cmWorkEqRecallConvertor.toVo(cmWorkEqRecallDao.selectById(id));
    }

    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    public PageResult<CmWorkEqRecallDTO> getPage(PageQuery<WorkOrderDTO> pageQuery, String operatorNo) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        WorkOrderDTO queryDTO = pageQuery.getQueryData();
        List<CmWorkEqRecallDTO> list = cmWorkEqRecallDao.selectByCondition(
                queryDTO.getWorkStatus(),
                queryDTO.getTimeScope(),operatorNo);

        Page<CmWorkEqRecallDTO> page = (Page<CmWorkEqRecallDTO>) list;
        PageResult<CmWorkEqRecallDTO> result = new PageResult<>();
        result.setList(page.getResult());
        result.setTotal(page.getTotal());
        result.setPageNo(page.getPageNum());
        result.setPageSize(page.getPageSize());
        result.setPages(page.getPages());
        result.setSize(page.size());

        return result;
    }
//    public PageResult<CmWorkEqRecallDTO> getPage(PageQuery<CmWorkEqRecallDTO> pageQuery) {
//        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
//        CmWorkEqRecallDTO cmWorkEqRecallDTO = pageQuery.getQueryData();
//        List<CmWorkEqRecallDTO> list = cmWorkEqRecallDao.selectByCondition(cmWorkEqRecallDTO);
//        if (list != null) {
//            return new PageResult<>(list);
//        }
//        return null;
//    }

    /**
     * 审核工单
     * @param id
     * @param auditStatus
     * @return
     */
    @Transactional
    public int auditBusiness(Long id, Integer auditStatus, String reason) {
        CmWorkEqRecall cmWorkEqRecall = cmWorkEqRecallDao.selectById(id);
        if (cmWorkEqRecall == null) {
            throw new RuntimeException("工单不存在");
        }
        if (auditStatus == 2 && StringUtils.isBlank(reason)) {
            throw new RuntimeException("驳回时必须填写原因");
        }

        if (cmWorkEqRecall.getWorkStatus() != 0) {
            throw new RuntimeException("只能审核待审核状态的工单");
        }

        CmWorkEqRecall update = new CmWorkEqRecall();
        update.setId(id);
        update.setWorkStatus(auditStatus);
        update.setReason(reason);
        update.setUpdateTime(new Date());

        return cmWorkEqRecallDao.update(update);
    }

}
