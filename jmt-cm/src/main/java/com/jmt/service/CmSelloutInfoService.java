package com.jmt.service;

import cn.hutool.core.convert.Convert;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.client.ProfitFeignClient;
import com.jmt.client.UaaFeignClient;
import com.jmt.convertor.CmSelloutInfoConvertor;
import com.jmt.dao.*;
import com.jmt.enums.PayTypeEnum;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.cm.dto.CmSelloutInfoDTO;
import com.jmt.model.cm.entity.CmSelloutAudit;
import com.jmt.model.cm.entity.CmSelloutInfo;
import com.jmt.model.cm.entity.CmSelloutOrder;
import com.jmt.model.cm.entity.CmUser;
import com.jmt.model.cm.vo.PubBusInfoVO;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.cm.vo.CmSelloutInfoVO;
import com.jmt.model.profit.dto.BalancePayDto;
import com.jmt.model.profit.dto.PointPayDTO;
import com.jmt.model.uaa.UaaUserWallet;
import com.jmt.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CmSelloutInfoService extends BaseService {
    @Resource
    private CmSelloutInfoDao cmSelloutInfoDao;

    @Resource
    private CmSelloutAuditDao cmSelloutAuditDao;

    @Resource
    private CmSelloutOrderDao cmSelloutOrderDao;

    @Resource
    private CmSelloutInfoConvertor cmSelloutInfoConvertor;


    @Resource
    private CmUserDao cmUserDao;

    @Resource
    private CmBusinessInfoDao cmBusinessInfoDao;
    @Resource
    private CmBusinessShopDao cmBusinessShopDao;

    @Resource
    private UaaFeignClient uaaFeignClient;

    @Resource
    private ProfitFeignClient profitFeignClient;

    /*
    *发布卖光光 （商家发 / 门店发）
    * 必传参数 userId(可不传 通过token查cmUser), title, content,picture,video,scope, selloutType ,selloutDay,payType, payAmount,password
    *
    * */
    @Transactional
    public Long create(CmSelloutInfoDTO dto , LoginUaaUser loginUser) {
        CmSelloutInfo cmSelloutInfo = cmSelloutInfoConvertor.toEntity(dto);

        Long uaaId = loginUser.getUaaId();
        CmUser user = cmUserDao.selectByUaaId(uaaId);
        if(user == null){
            throw new RuntimeException("当前用户不存在");
        }
        cmSelloutInfo.setUserId(user.getId());
        //cmSelloutInfo.setUserId(dto.getUserId());
        cmSelloutInfo.setAuditStatus(0);
        cmSelloutInfo.setCreateTime(new Date());
        cmSelloutInfo.setUpdateTime(new Date());
        cmSelloutInfo.setIsDelete(0);

        String shopName = null;
        if( user.getRoleType() == 1){
            shopName = cmBusinessInfoDao.selectBusNameByUserId(user.getId());
        }else if (user.getRoleType() == 2){
            shopName = cmBusinessShopDao.selectBusNameByUserId(user.getId());
        }
        cmSelloutInfo.setShopName(shopName);
        cmSelloutInfo.setVisitCount(0);
        cmSelloutInfo.setMessageCount(0);
        cmSelloutInfo.setDealCount(0);
        cmSelloutInfo.setEndTime(DateCalculatorUtil.calculateEndTime(cmSelloutInfo.getCreateTime(), dto.getSelloutDay()));
        Long result = cmSelloutInfoDao.insert(cmSelloutInfo);
        Long generatedId = cmSelloutInfo.getId();

        log.info("----------generatedId:{}",generatedId);

        //创建审核记录
        CmSelloutAudit audit = new CmSelloutAudit();
        audit.setInfoId(generatedId);
        audit.setAuditStatus(0);
        audit.setCreateTime(new Date());
        audit.setUpdateTime(new Date());
        audit.setIsDelete(0);
        cmSelloutAuditDao.insert(audit);

        //创建卖光光订单
        CmSelloutOrder order=new CmSelloutOrder();
        order.setCreateTime(new Date());
        order.setUpdateTime(new Date());
        order.setIsDelete(0);
        String orderNo = null;
       // String orderNo = ProfitCodeGenerateUtil.generateProfitCode("002"); //信息发布
        do {
            orderNo = SysCodeGenerateUtil.generateProfitCode("002"); // 信息发布
            int count = cmSelloutOrderDao.countByOrderNo(orderNo);
            if (count == 0) {
                break;
            }
        } while (true); // 生成唯一编号

        order.setOrderNo(orderNo);
        order.setInfoId(generatedId);
        order.setOrderName(dto.getTitle());
        order.setPayType(dto.getPayType());
        order.setPayAmount(dto.getPayAmount());
        order.setUserId(user.getId());
        //校验是否设置支付密码
        String walletRes =uaaFeignClient.getWalletByUaaId(uaaId);
        UaaUserWallet wallet= ResponseUtil.getData(walletRes,UaaUserWallet.class);
        if (wallet == null) {
            throw new RuntimeException("用户钱包不存在");
        }
        if (StringUtils.isEmpty(wallet.getPayPassword())){
            log.error("未设置密码");
            throw new RuntimeException("未设置密码");
        }

        //去支付
        PayTypeEnum payType = PayTypeEnum.getByCode(dto.getPayType());
        switch (payType) {
            case POINTS_PAY:
                PointPayDTO pointPayDTO = new PointPayDTO();
                pointPayDTO.setUaaId(uaaId);
                pointPayDTO.setPoints(dto.getPayAmount().intValue());
                pointPayDTO.setBusUaaId(1L);
                pointPayDTO.setOrderNo(order.getOrderNo());
                pointPayDTO.setPayPassword(dto.getPassword());
                log.info("----------point");
                String payRes = profitFeignClient.pointsByUaaIdPay(pointPayDTO);
                log.info("----------payRes:{}",payRes);
                Map<String, Object> responseMap = GsonUtil.fromJsonMap(payRes);
                String code = Convert.toStr(responseMap.get("code"));
                if(!"200".equals(code)){
                    String errorMsg = Convert.toStr(responseMap.get("msg"));
                    throw new RuntimeException(errorMsg);
                }

                break;
            case BALANCE_PAY:
                BalancePayDto payDTO = new BalancePayDto();
                payDTO.setUaaId(uaaId);
                payDTO.setBalance(dto.getPayAmount());
                payDTO.setBusUaaId(1L);
                payDTO.setOrderNo(order.getOrderNo());
                payDTO.setPayPassword(dto.getPassword());
                String balPayRes = profitFeignClient.balanceByUaaIdPay(payDTO);
                Map<String, Object> balResponseMap = GsonUtil.fromJsonMap(balPayRes);
                String balCode = Convert.toStr(balResponseMap.get("code"));
                if(!"200".equals(balCode)){
                    String errorMsg = Convert.toStr(balResponseMap.get("msg"));
                    throw new RuntimeException(errorMsg);
                }

                break;
//                case WECHAT_PAY:
//                    break;
//                case ALIPAY_PAY:
//                    break;
            default:
                throw new RuntimeException("不支持的支付方式: " + payType.getMessage());
        }
        order.setOrderStatus(1);
        cmSelloutOrderDao.insert(order);

        return result;
    }

    @Transactional
    public int update(CmSelloutInfoDTO dto) {
        CmSelloutInfo cmSelloutInfo = cmSelloutInfoDao.selectById(dto.getId());
        if (cmSelloutInfo == null) {
            throw new RuntimeException("工单不存在");
        }
        if (cmSelloutInfo.getAuditStatus() != 0) {
            throw new RuntimeException("只有待审核状态的工单可以修改");
        }

        CmSelloutInfo cmWorkEq = cmSelloutInfoConvertor.toEntity(dto);
        cmWorkEq.setUpdateTime(new Date());
        return cmSelloutInfoDao.update(cmWorkEq);
    }


    @Transactional
    public int delete(Long id) {
        CmSelloutInfo cmSelloutInfo = cmSelloutInfoDao.selectById(id);
        if (cmSelloutInfo == null) {
            throw new RuntimeException("工单不存在");
        }
        return cmSelloutInfoDao.delete(id);
    }

    /**
     * 增加访问量的查询
     * @param id
     * @return
     */
    @Transactional
    public CmSelloutInfoVO getByWithVisitId(Long id) {
        cmSelloutInfoDao.incrementVisitCount(id);
        CmSelloutInfoDTO dto = cmSelloutInfoDao.selectDtoById(id);
        if (dto == null) {
            return null;
        }
        CmSelloutInfoVO vo = new CmSelloutInfoVO();
        BeanUtils.copyProperties(dto, vo);
        if (dto.getUserId() != null) {
            CmUser user = cmUserDao.selectById(dto.getUserId());
            PubBusInfoVO pubBusInfoVO = new PubBusInfoVO();
            if(user.getRoleType() == 1){ //商家
                pubBusInfoVO = cmBusinessInfoDao.selectBusInfoByUserId(dto.getUserId());
            }else if (user.getRoleType() == 2){ //门店
                pubBusInfoVO = cmBusinessShopDao.selectBusInfoByUserId(dto.getUserId());
            }


            if (user != null && pubBusInfoVO != null) {
                pubBusInfoVO.setNickName(user.getNickName());
                pubBusInfoVO.setHeadImg(user.getHeadImg());
                pubBusInfoVO.setCategoryName(pubBusInfoVO.getCategoryId());
            }
            vo.setPubBusInfoVO(pubBusInfoVO);
        }

        return vo;
    }
    @Transactional
    public CmSelloutInfoVO getById(Long id) {
        CmSelloutInfoDTO dto = cmSelloutInfoDao.selectDtoById(id);
        if (dto == null) {
            return null;
        }
        CmSelloutInfoVO vo = new CmSelloutInfoVO();
        BeanUtils.copyProperties(dto, vo);
        if (dto.getUserId() != null) {
            CmUser user = cmUserDao.selectById(dto.getUserId());
            PubBusInfoVO pubBusInfoVO = new PubBusInfoVO();
            if(user.getRoleType() == 1){ //商家
                pubBusInfoVO = cmBusinessInfoDao.selectBusInfoByUserId(dto.getUserId());
            }else if (user.getRoleType() == 2){ //门店
                pubBusInfoVO = cmBusinessShopDao.selectBusInfoByUserId(dto.getUserId());
            }


            if (user != null && pubBusInfoVO != null) {
                pubBusInfoVO.setNickName(user.getNickName());
                pubBusInfoVO.setHeadImg(user.getHeadImg());
                pubBusInfoVO.setCategoryName(pubBusInfoVO.getCategoryId());
            }
            vo.setPubBusInfoVO(pubBusInfoVO);
        }

        return vo;
    }

    /**
     * 根据UaaID查询卖光光列表
     * @param uaaId
     * @return
     */
    public List<CmSelloutInfoVO> getListByUaaId(Long uaaId) {
        CmUser myUser = cmUserDao.selectByUaaId(uaaId);
        if(myUser == null){
            throw new RuntimeException("用户不存在");
        }

        List<CmSelloutInfoDTO> cmSelloutInfoDTOS = cmSelloutInfoDao.selectListByUserId(myUser.getId());
        if (CollectionUtils.isEmpty(cmSelloutInfoDTOS)) {
            return Collections.emptyList();
        }
        List<CmSelloutInfoVO> voList = cmSelloutInfoDTOS.stream().map(dto -> {
            CmSelloutInfoVO vo = new CmSelloutInfoVO();
            BeanUtils.copyProperties(dto, vo);

            // 关联查询
            if (dto.getUserId() != null) {
                PubBusInfoVO pubBusInfoVO = new PubBusInfoVO();
                if( myUser.getRoleType() == 1){
                    pubBusInfoVO = cmBusinessInfoDao.selectBusInfoByUserId(dto.getUserId());
                }else if (myUser.getRoleType() == 2){
                    pubBusInfoVO = cmBusinessShopDao.selectBusInfoByUserId(dto.getUserId());
                }
                if ( pubBusInfoVO != null) {
                    pubBusInfoVO.setNickName(myUser.getNickName());
                    pubBusInfoVO.setHeadImg(myUser.getHeadImg());
                    pubBusInfoVO.setCategoryName(pubBusInfoVO.getCategoryId());
                }
                vo.setPubBusInfoVO(pubBusInfoVO);
            }
            return vo;
        }).collect(Collectors.toList());
        return voList;
    }

    /**
    返回数据
     id, title, content,auditStatus,userId(查店名称，昵称，手机，头像，星级，业态categoryId，所在位置),
     createTime,  picture,video,scope, selloutType ,selloutDay,endTime 范围（）
    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    public PageResult<CmSelloutInfoVO> getPage(PageQuery<CmSelloutInfoDTO> pageQuery) {
        int pageNo = pageQuery.getPageNo() == null ? 1 : pageQuery.getPageNo(); // 默认第1页
        int pageSize = pageQuery.getPageSize() == null ? 10 : pageQuery.getPageSize(); // 默认10条/页
        pageNo = Math.max(pageNo, 1); // 页码最小为1
        pageSize = Math.max(Math.min(pageSize, 100), 1); // 页大小限制1~100条（防过大查询）

        PageHelper.startPage(pageNo, pageSize);
        CmSelloutInfoDTO queryDTO = pageQuery.getQueryData() != null ? pageQuery.getQueryData() : new CmSelloutInfoDTO();
        List<CmSelloutInfoDTO> dtoList = cmSelloutInfoDao.selectByCondition(queryDTO);

        Page<CmSelloutInfoDTO> dtoPage = (Page<CmSelloutInfoDTO>) dtoList;

        // 关联查询商户/用户信息
        List<CmSelloutInfoVO> voList = dtoPage.stream().map(dto -> {
            CmSelloutInfoVO vo = new CmSelloutInfoVO();
            BeanUtils.copyProperties(dto, vo);

            // 关联查询
            if (dto.getUserId() != null) {
                CmUser user = cmUserDao.selectById(dto.getUserId());
                PubBusInfoVO pubBusInfoVO = new PubBusInfoVO();
                if(user.getRoleType() == 1){
                    pubBusInfoVO = cmBusinessInfoDao.selectBusInfoByUserId(dto.getUserId());
                }else if(user.getRoleType() == 2){
                    pubBusInfoVO = cmBusinessShopDao.selectBusInfoByUserId(dto.getUserId());
                }


                if (user != null && pubBusInfoVO != null) {
                    pubBusInfoVO.setNickName(user.getNickName());
                    pubBusInfoVO.setHeadImg(user.getHeadImg());
                    pubBusInfoVO.setCategoryName(pubBusInfoVO.getCategoryId());
                }
                vo.setPubBusInfoVO(pubBusInfoVO);
            }
            return vo;
        }).collect(Collectors.toList());

        return new PageResult<>(dtoPage, voList);
    }

    public PageResult<CmSelloutInfoVO> getPcPage(PageQuery<CmSelloutInfoDTO> pageQuery) {
        int pageNo = pageQuery.getPageNo() == null ? 1 : pageQuery.getPageNo(); // 默认第1页
        int pageSize = pageQuery.getPageSize() == null ? 10 : pageQuery.getPageSize(); // 默认10条/页
        pageNo = Math.max(pageNo, 1); // 页码最小为1
        pageSize = Math.max(Math.min(pageSize, 100), 1); // 页大小限制1~100条（防过大查询）

        PageHelper.startPage(pageNo, pageSize);
        CmSelloutInfoDTO queryDTO = pageQuery.getQueryData() != null ? pageQuery.getQueryData() : new CmSelloutInfoDTO();
        List<CmSelloutInfoDTO> dtoList = cmSelloutInfoDao.selectPcByCondition(queryDTO);

        Page<CmSelloutInfoDTO> dtoPage = (Page<CmSelloutInfoDTO>) dtoList;

        // 关联查询商户/用户信息
        List<CmSelloutInfoVO> voList = dtoPage.stream().map(dto -> {
            CmSelloutInfoVO vo = new CmSelloutInfoVO();
            BeanUtils.copyProperties(dto, vo);

            // 关联查询
            if (dto.getUserId() != null) {
                CmUser user = cmUserDao.selectById(dto.getUserId());
                PubBusInfoVO pubBusInfoVO = new PubBusInfoVO();
                if(user.getRoleType() == 1){
                    pubBusInfoVO = cmBusinessInfoDao.selectBusInfoByUserId(dto.getUserId());
                }else if(user.getRoleType() == 2){
                    pubBusInfoVO = cmBusinessShopDao.selectBusInfoByUserId(dto.getUserId());
                }


                if (user != null && pubBusInfoVO != null) {
                    pubBusInfoVO.setNickName(user.getNickName());
                    pubBusInfoVO.setHeadImg(user.getHeadImg());
                    pubBusInfoVO.setCategoryName(pubBusInfoVO.getCategoryId());
                }
                vo.setPubBusInfoVO(pubBusInfoVO);
            }
            return vo;
        }).collect(Collectors.toList());

        return new PageResult<>(dtoPage, voList);
    }

    /**
     * 审核工单
     * @param id
     * @param auditStatus
     * @return
     */
    @Transactional
    public int auditBusiness(Long id, Integer auditStatus, String reason, Long auditUserId) {
        CmSelloutInfo cmSelloutInfo = cmSelloutInfoDao.selectById(id);
        if (cmSelloutInfo == null) {
            throw new RuntimeException("工单不存在");
        }

        if (auditStatus == 2 && StringUtils.isBlank(reason)) {
            throw new RuntimeException("驳回时必须填写原因");
        }

        if (cmSelloutInfo.getAuditStatus() != 0) {
            throw new RuntimeException("只能审核待审核状态的工单");
        }

        // 更新工单审核状态
        cmSelloutInfo.setAuditStatus(auditStatus);
        cmSelloutInfo.setReason(reason);
        cmSelloutInfo.setUpdateTime(new Date());
        int updateCount = cmSelloutInfoDao.update(cmSelloutInfo);

        // 更新审核表
        CmSelloutAudit existingAudit = cmSelloutAuditDao.selectByInfoId(id);
        existingAudit.setAuditStatus(auditStatus);
        existingAudit.setReason(reason);
        existingAudit.setUpdateTime(new Date());
        existingAudit.setAuditUser(auditUserId);
        cmSelloutAuditDao.update(existingAudit);

        return updateCount;
    }

    /**
     * 创建订单 --------> 订单在创建info时同步创建
     * @param order
     */
    @Transactional
    public void createOrder(CmSelloutOrder order) {
        order.setOrderStatus(0);
        order.setCreateTime(new Date());
        order.setUpdateTime(new Date());
        order.setIsDelete(0);
        String orderNo = WorkNoGeneratorUtil.generate("SNO");
        order.setOrderNo(orderNo);
        cmSelloutOrderDao.insert(order);
    }

    /**
     * 取消订单
     * @param id
     */
    @Transactional
    public void cancelOrder(Long id) {
        CmSelloutOrder order = cmSelloutOrderDao.selectById(id);
        if (order == null || order.getIsDelete() == 1) {
            throw new RuntimeException("订单不存在或已被删除");
        }
        order.setOrderStatus(3); //取消支付
        order.setUpdateTime(new Date());
        cmSelloutOrderDao.update(order);
    }

    /**
     * 订单支付状态改变
     * @param id
     * @param auditStatus
     * @param reason
     * @return
     */
    @Transactional
    public int auditOrder(Long id, Integer auditStatus, String reason) {
        CmSelloutOrder order = cmSelloutOrderDao.selectById(id);
        if (order == null) {
            throw new RuntimeException("工单不存在");
        }

        if (auditStatus == 2 && StringUtils.isBlank(reason)) {
            throw new RuntimeException("驳回时必须填写原因");
        }

        if (order.getOrderStatus() != 0) {
            throw new RuntimeException("只能审核待审核状态的工单");
        }

        // 更新工单支付状态
        CmSelloutOrder update = new CmSelloutOrder();
        update.setId(id);
        update.setOrderStatus(auditStatus);
        update.setReason(reason);
        update.setUpdateTime(new Date());

        return cmSelloutOrderDao.update(update);
    }
}
