package com.jmt.service;

import cn.hutool.core.convert.Convert;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.client.ProfitFeignClient;
import com.jmt.client.UaaFeignClient;
import com.jmt.convertor.CmFarmingFoodInfoConvertor;
import com.jmt.dao.*;
import com.jmt.enums.PayTypeEnum;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.cm.dto.CmFarmingFoodInfoDTO;
import com.jmt.model.cm.entity.CmFarmingFoodAudit;
import com.jmt.model.cm.entity.CmFarmingFoodInfo;
import com.jmt.model.cm.entity.CmFarmingFoodOrder;
import com.jmt.model.cm.entity.CmUser;
import com.jmt.model.cm.vo.PubBusInfoVO;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.cm.vo.CmFarmingFoodInfoVO;
import com.jmt.model.profit.dto.BalancePayDto;
import com.jmt.model.profit.dto.PointPayDTO;
import com.jmt.model.uaa.UaaUserWallet;
import com.jmt.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CmFarmingFoodInfoService extends BaseService {
    @Resource
    private CmFarmingFoodInfoDao cmFarmingFoodInfoDao;

    @Resource
    private CmFarmingFoodAuditDao cmFarmingFoodAuditDao;

    @Resource
    private CmFarmingFoodOrderDao cmFarmingFoodOrderDao;

    @Resource
    private CmFarmingFoodInfoConvertor cmFarmingFoodInfoConvertor;

    @Resource
    private CmUserDao cmUserDao;

    @Resource
    private CmBusinessInfoDao cmBusinessInfoDao;

    @Resource
    private UaaFeignClient uaaFeignClient;

    @Resource
    private CmCategoryDao cmCategoryDao;

    @Resource
    private ProfitFeignClient profitFeignClient;

    @Resource
    private CmBusinessShopDao cmBusinessShopDao;

    /*
     *发布农餐对接 （商家发 / 门店发）
     * 必传参数 userId(可不传 通过token查cmUser), title, content,picture,video,province,city, selloutType ,selloutDay,payType, payAmount,password
     *
     * */
    @Transactional
    public Long create(CmFarmingFoodInfoDTO dto, LoginUaaUser loginUser) {
        CmFarmingFoodInfo cmFarmingFoodInfo = new CmFarmingFoodInfo();
        BeanUtils.copyProperties(dto, cmFarmingFoodInfo);

        Long uaaId = loginUser.getUaaId();
        CmUser user = cmUserDao.selectByUaaId(uaaId);
        cmFarmingFoodInfo.setUserId(user.getId());
        cmFarmingFoodInfo.setAuditStatus(0);
        cmFarmingFoodInfo.setCreateTime(new Date());
        cmFarmingFoodInfo.setUpdateTime(new Date());
        cmFarmingFoodInfo.setIsDelete(0);

        String shopName = null;
        if( user.getRoleType() == 1){
            shopName = cmBusinessInfoDao.selectBusNameByUserId(user.getId());
        }else if(user.getRoleType() == 2 ){
            shopName = cmBusinessShopDao.selectBusNameByUserId(user.getId());
        }
        cmFarmingFoodInfo.setBusName(shopName);
        cmFarmingFoodInfo.setVisitCount(0);
        cmFarmingFoodInfo.setMessageCount(0);
        cmFarmingFoodInfo.setDealCount(0);
        cmFarmingFoodInfo.setEndTime(DateCalculatorUtil.calculateEndTime(cmFarmingFoodInfo.getCreateTime(), dto.getSelloutDay()));
        Long result = cmFarmingFoodInfoDao.insert(cmFarmingFoodInfo);
        Long generatedId = cmFarmingFoodInfo.getId();

        CmFarmingFoodAudit audit = new CmFarmingFoodAudit();
        audit.setInfoId(generatedId);
        audit.setAuditStatus(0);
        audit.setCreateTime(new Date());
        audit.setUpdateTime(new Date());
        audit.setIsDelete(0);
        cmFarmingFoodAuditDao.insert(audit);

        //创建订单
        CmFarmingFoodOrder order=new CmFarmingFoodOrder();
        order.setCreateTime(new Date());
        order.setUpdateTime(new Date());
        order.setIsDelete(0);
        String orderNo;
        do {
            orderNo = SysCodeGenerateUtil.generateProfitCode("002"); // 信息发布
            int count = cmFarmingFoodOrderDao.countByOrderNo(orderNo);
            if (count == 0) {
                break;
            }
        } while (true); // 生成唯一编号

        order.setOrderNo(orderNo);
        order.setInfoId(generatedId);
        order.setOrderName(dto.getTitle());
        order.setPayType(dto.getPayType());
        order.setPayAmount(dto.getPayAmount());
        order.setUserId(user.getId());

        //校验是否设置支付密码
        String walletRes =uaaFeignClient.getWalletByUaaId(uaaId);
        UaaUserWallet wallet= ResponseUtil.getData(walletRes,UaaUserWallet.class);
        if (wallet == null) {
            throw new RuntimeException("用户钱包不存在");
        }
        if (StringUtils.isEmpty(wallet.getPayPassword())){
            log.error("未设置密码");
            throw new RuntimeException("未设置密码");
        }


        // 去支付
        PayTypeEnum payType = PayTypeEnum.getByCode(dto.getPayType());
        switch (payType) {
            case POINTS_PAY:
                PointPayDTO pointPayDTO = new PointPayDTO();
                pointPayDTO.setUaaId(uaaId);
                pointPayDTO.setPoints(dto.getPayAmount().intValue());
                pointPayDTO.setBusUaaId(1L);
                pointPayDTO.setOrderNo(order.getOrderNo());
                pointPayDTO.setPayPassword(dto.getPassword());
                String payRes = profitFeignClient.pointsByUaaIdPay(pointPayDTO);
                Map<String, Object> responseMap = GsonUtil.fromJsonMap(payRes);
                String code = Convert.toStr(responseMap.get("code"));
                if(!"200".equals(code)){
                    String errorMsg = Convert.toStr(responseMap.get("msg"));
                    throw new RuntimeException(errorMsg);
                }

                break;
            case BALANCE_PAY:
                BalancePayDto payDTO = new BalancePayDto();
                payDTO.setUaaId(uaaId);
                payDTO.setBalance(dto.getPayAmount());
                payDTO.setBusUaaId(1L);
                payDTO.setOrderNo(order.getOrderNo());
                payDTO.setPayPassword(dto.getPassword());
                String balPayRes = profitFeignClient.balanceByUaaIdPay(payDTO);
                Map<String, Object> balResponseMap = GsonUtil.fromJsonMap(balPayRes);
                String balCode = Convert.toStr(balResponseMap.get("code"));
                if(!"200".equals(balCode)){
                    String errorMsg = Convert.toStr(balResponseMap.get("msg"));
                    throw new RuntimeException(errorMsg);
                }

                break;
//                case WECHAT_PAY:
//                    break;
//                case ALIPAY_PAY:
//                    break;
            default:
                throw new RuntimeException("不支持的支付方式: " + payType.getMessage());
        }
        order.setOrderStatus(1);
        cmFarmingFoodOrderDao.insert(order);
        return result;
    }

    @Transactional
    public int update(CmFarmingFoodInfoDTO dto) {
        CmFarmingFoodInfo cmFarmingFoodInfo = cmFarmingFoodInfoDao.selectById(dto.getId());
        if (cmFarmingFoodInfo == null) {
            throw new RuntimeException("工单不存在");
        }
        if (cmFarmingFoodInfo.getAuditStatus() != 0) {
            throw new RuntimeException("只有待审核状态的工单可以修改");
        }

        CmFarmingFoodInfo cmWorkEq = cmFarmingFoodInfoConvertor.toEntity(dto);
        cmWorkEq.setUpdateTime(new Date());
        return cmFarmingFoodInfoDao.update(cmWorkEq);
    }


    @Transactional
    public int delete(Long id) {
        CmFarmingFoodInfo cmFarmingFoodInfo = cmFarmingFoodInfoDao.selectById(id);
        if (cmFarmingFoodInfo == null) {
            throw new RuntimeException("工单不存在");
        }
        return cmFarmingFoodInfoDao.delete(id);
    }

    /**
     * 增加访客量的查询
     * @param id
     * @return
     */
    @Transactional
    public CmFarmingFoodInfoVO getByWithVisitId(Long id) {
        cmFarmingFoodInfoDao.incrementVisitCount(id);
        CmFarmingFoodInfoDTO dto = cmFarmingFoodInfoDao.selectDtoById(id);
        if (dto == null) {
           return null;
        }
        CmFarmingFoodInfoVO vo = new CmFarmingFoodInfoVO();
        BeanUtils.copyProperties(dto, vo);
        if (dto.getUserId() != null) {
            CmUser user = cmUserDao.selectById(dto.getUserId());
            PubBusInfoVO pubBusInfoVO = new PubBusInfoVO();
            if(user.getRoleType() == 1){ //商家
                pubBusInfoVO = cmBusinessInfoDao.selectBusInfoByUserId(dto.getUserId());
            }else if(user.getRoleType() == 2 ){ //门店
                pubBusInfoVO = cmBusinessShopDao.selectBusInfoByUserId(dto.getUserId());
            }


            if (user != null && pubBusInfoVO != null) {
                pubBusInfoVO.setNickName(user.getNickName());
                pubBusInfoVO.setHeadImg(user.getHeadImg());
                pubBusInfoVO.setCategoryName(pubBusInfoVO.getCategoryId());
            }
            vo.setPubBusInfoVO(pubBusInfoVO);
        }

        return vo;
    }


    @Transactional
    public CmFarmingFoodInfoVO getById(Long id) {
        CmFarmingFoodInfoDTO dto = cmFarmingFoodInfoDao.selectDtoById(id);
        if (dto == null) {
            return null;
        }
        CmFarmingFoodInfoVO vo = new CmFarmingFoodInfoVO();
        BeanUtils.copyProperties(dto, vo);
        if (dto.getUserId() != null) {
            CmUser user = cmUserDao.selectById(dto.getUserId());
            PubBusInfoVO pubBusInfoVO = new PubBusInfoVO();
            if(user.getRoleType() == 1){ //商家
                pubBusInfoVO = cmBusinessInfoDao.selectBusInfoByUserId(dto.getUserId());
            }else if(user.getRoleType() == 2 ){ //门店
                pubBusInfoVO = cmBusinessShopDao.selectBusInfoByUserId(dto.getUserId());
            }


            if (user != null && pubBusInfoVO != null) {
                pubBusInfoVO.setNickName(user.getNickName());
                pubBusInfoVO.setHeadImg(user.getHeadImg());
                pubBusInfoVO.setCategoryName(pubBusInfoVO.getCategoryId());
            }
            vo.setPubBusInfoVO(pubBusInfoVO);
        }

        return vo;
    }

    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    public PageResult<CmFarmingFoodInfoVO> getPage(PageQuery<CmFarmingFoodInfoDTO> pageQuery) {
        int pageNo = pageQuery.getPageNo() == null ? 1 : pageQuery.getPageNo(); // 默认第1页
        int pageSize = pageQuery.getPageSize() == null ? 10 : pageQuery.getPageSize(); // 默认10条/页
        pageNo = Math.max(pageNo, 1); // 页码最小为1
        pageSize = Math.max(Math.min(pageSize, 100), 1); // 页大小限制1~100条（防过大查询）

        PageHelper.startPage(pageNo, pageSize);
        CmFarmingFoodInfoDTO queryDTO = pageQuery.getQueryData() != null ? pageQuery.getQueryData() : new CmFarmingFoodInfoDTO();
        List<CmFarmingFoodInfoDTO> dtoList = cmFarmingFoodInfoDao.selectByCondition(queryDTO);

        Page<CmFarmingFoodInfoDTO> dtoPage = (Page<CmFarmingFoodInfoDTO>) dtoList;

        // 关联查询商户/用户信息
        List<CmFarmingFoodInfoVO> voList = dtoPage.stream().map(dto -> {
            CmFarmingFoodInfoVO vo = new CmFarmingFoodInfoVO();
            BeanUtils.copyProperties(dto, vo);

            // 关联查询
            if (dto.getUserId() != null) {
                CmUser user = cmUserDao.selectById(dto.getUserId());
                PubBusInfoVO pubBusInfoVO = new PubBusInfoVO();
                if(user.getRoleType() == 1){
                    pubBusInfoVO = cmBusinessInfoDao.selectBusInfoByUserId(dto.getUserId());
                }else if(user.getRoleType() == 2){
                    pubBusInfoVO = cmBusinessShopDao.selectBusInfoByUserId(dto.getUserId());
                }


                if (user != null && pubBusInfoVO != null) {
                    pubBusInfoVO.setNickName(user.getNickName());
                    pubBusInfoVO.setHeadImg(user.getHeadImg());
                    pubBusInfoVO.setCategoryName(pubBusInfoVO.getCategoryId());
                }
                vo.setPubBusInfoVO(pubBusInfoVO);
            }
            return vo;
        }).collect(Collectors.toList());

        return new PageResult<>(dtoPage, voList);
    }

    /**
     * 审核工单
     * @param id
     * @param auditStatus
     * @return
     */
    @Transactional
    public int auditBusiness(Long id, Integer auditStatus, String reason, Long auditUserId) {
        CmFarmingFoodInfo cmFarmingFoodInfo = cmFarmingFoodInfoDao.selectById(id);
        if (cmFarmingFoodInfo == null) {
            throw new RuntimeException("工单不存在");
        }

        if (auditStatus == 2 && StringUtils.isBlank(reason)) {
            throw new RuntimeException("驳回时必须填写原因");
        }

        if (cmFarmingFoodInfo.getAuditStatus() != 0) {
            throw new RuntimeException("只能审核待审核状态的工单");
        }

        // 更新工单审核状态
        cmFarmingFoodInfo.setAuditStatus(auditStatus);
        cmFarmingFoodInfo.setReason(reason);
        cmFarmingFoodInfo.setUpdateTime(new Date());
        int updateCount = cmFarmingFoodInfoDao.update(cmFarmingFoodInfo);

        // 查询审核记录
        CmFarmingFoodAudit existingAudit = cmFarmingFoodAuditDao.selectByInfoId(id);
        existingAudit.setAuditStatus(auditStatus);
        existingAudit.setReason(reason);
        existingAudit.setUpdateTime(new Date());
        existingAudit.setAuditUser(auditUserId);
        cmFarmingFoodAuditDao.update(existingAudit);

        return updateCount;
    }

    /**
     * 创建订单
     * @param order
     */
    @Transactional
    public void createOrder(CmFarmingFoodOrder order) {
        order.setOrderStatus(0);
        order.setCreateTime(new Date());
        order.setUpdateTime(new Date());
        order.setIsDelete(0);
        String orderNo = WorkNoGeneratorUtil.generate("FNO");
        order.setOrderNo(orderNo);
        cmFarmingFoodOrderDao.insert(order);
    }

    /**
     * 取消订单
     * @param id
     */
    @Transactional
    public void cancelOrder(Long id) {
        CmFarmingFoodOrder order = cmFarmingFoodOrderDao.selectById(id);
        if (order == null || order.getIsDelete() == 1) {
            throw new RuntimeException("订单不存在或已被删除");
        }
        order.setOrderStatus(3); //取消支付
        order.setUpdateTime(new Date());
        cmFarmingFoodOrderDao.update(order);
    }

    /**
     * 订单支付状态改变
     * @param id
     * @param auditStatus
     * @param reason
     * @return
     */
    @Transactional
    public int auditOrder(Long id, Integer auditStatus, String reason) {
        CmFarmingFoodOrder order = cmFarmingFoodOrderDao.selectById(id);
        if (order == null) {
            throw new RuntimeException("工单不存在");
        }

        if (auditStatus == 2 && StringUtils.isBlank(reason)) {
            throw new RuntimeException("驳回时必须填写原因");
        }

        if (order.getOrderStatus() != 0) {
            throw new RuntimeException("只能审核待审核状态的工单");
        }

        // 更新工单支付状态
        CmFarmingFoodOrder update = new CmFarmingFoodOrder();
        update.setId(id);
        update.setOrderStatus(auditStatus);
        update.setReason(reason);
        update.setUpdateTime(new Date());

        return cmFarmingFoodOrderDao.update(update);
    }

    public CmFarmingFoodOrder getOrderByOrderNo(String orderNo) {
        if (StringUtils.isBlank(orderNo)) {
            return null;
        }
        return cmFarmingFoodOrderDao.selectByOrderNo(orderNo);
    }

    /**
     * 根据订单编号修改订单支付状态
     * @param orderNo
     * @param auditStatus
     * @return
     */
    @Transactional
    public int updateStatusByOrderNo(String orderNo, Integer auditStatus) {
        CmFarmingFoodOrder order = cmFarmingFoodOrderDao.selectByOrderNo(orderNo);

        if (order == null) {
            throw new RuntimeException("订单不存在");
        }

        if (order.getOrderStatus() != 0) {
            throw new RuntimeException("只能修改未支付状态的订单");
        }

        // 更新订单支付状态
        order.setOrderStatus(auditStatus);
        order.setUpdateTime(new Date());

        return cmFarmingFoodOrderDao.update(order);
    }

    public List<CmFarmingFoodInfoVO> getListByUaaId(Long uaaId) {
        CmUser myUser = cmUserDao.selectByUaaId(uaaId);
        if(myUser == null){
            throw new RuntimeException("用户不存在");
        }

        List<CmFarmingFoodInfoDTO> cmFarmingFoodInfoDTOS = cmFarmingFoodInfoDao.selectListByUserId(myUser.getId());
        if (CollectionUtils.isEmpty(cmFarmingFoodInfoDTOS)) {
            return Collections.emptyList();
        }
        List<CmFarmingFoodInfoVO> voList = cmFarmingFoodInfoDTOS.stream().map(dto -> {
            CmFarmingFoodInfoVO vo = new CmFarmingFoodInfoVO();
            BeanUtils.copyProperties(dto, vo);

            // 关联查询
            if (dto.getUserId() != null) {
                PubBusInfoVO pubBusInfoVO = new PubBusInfoVO();
                if( myUser.getRoleType() == 1){
                    pubBusInfoVO = cmBusinessInfoDao.selectBusInfoByUserId(dto.getUserId());
                }else if( myUser.getRoleType() == 2){
                    pubBusInfoVO = cmBusinessShopDao.selectBusInfoByUserId(dto.getUserId());
                }
                if ( pubBusInfoVO != null) {
                    pubBusInfoVO.setNickName(myUser.getNickName());
                    pubBusInfoVO.setHeadImg(myUser.getHeadImg());
                    pubBusInfoVO.setCategoryName(pubBusInfoVO.getCategoryId());
                }
                vo.setPubBusInfoVO(pubBusInfoVO);
            }
            return vo;
        }).collect(Collectors.toList());
        return voList;
    }
}
