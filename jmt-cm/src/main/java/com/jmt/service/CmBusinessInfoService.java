package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.dao.CmBusinessInfoDao;
import com.jmt.dao.CmBusinessShopDao;
import com.jmt.model.cm.dto.BusinessInfoQueryDTO;
import com.jmt.model.cm.entity.CmBusinessInfo;
import com.jmt.model.cm.entity.CmBusinessMange;
import com.jmt.model.cm.entity.CmUser;
import com.jmt.model.cm.vo.CmBusinessInfoVO;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.util.DateUtil;
import com.jmt.util.SysCodeGenerateUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class CmBusinessInfoService extends BaseService {
    @Resource
    private CmBusinessInfoDao cmBusinessInfoDao;

    @Resource
    private CmBusinessShopDao cmBusinessShopDao;

    @Resource
    private CmUserService  cmUserService;

    @Transactional
    public int createBusinessInfo(CmBusinessInfo businessInfo, CmUser cmUser) {

        businessInfo.setUserId(cmUser.getId());
        businessInfo.setTelPhone(cmUser.getTelPhone());
        //随机生成商家编号
        String busNo=null;
        do {
            busNo = SysCodeGenerateUtil.generateUserCode("JMT-C1");
            int count = cmBusinessInfoDao.countByBusNo(busNo);
            if (count == 0) {
                break;
            }
        } while (true);
        businessInfo.setBusNo(busNo);

        businessInfo.setCreateTime(new Date());
        businessInfo.setUpdateTime(new Date());
        businessInfo.setIsDelete(0);
        businessInfo.setAuditStatus(0); // 默认待审核状态
        int result =cmBusinessInfoDao.insert(businessInfo);

        if (result > 0) {
            cmUserService.updateUserRoleType(cmUser.getId(), 1);
        }
        return result;
    }


    @Transactional
    public int updateBusinessInfo(CmBusinessInfo businessInfo) {
        if (businessInfo.getId() == null) {
            throw new IllegalArgumentException("商家ID不能为空");
        }
        if (businessInfo.getAuditStatus() != 0){
            throw new RuntimeException("商家已审核通过，无法修改");
        }
        businessInfo.setUpdateTime(new Date());
        return cmBusinessInfoDao.updateById(businessInfo);
    }


    @Transactional
    public int deleteBusinessInfo(Long id) {
        CmBusinessInfo businessInfo = cmBusinessInfoDao.selectById(id);
        if (businessInfo == null) {
            throw new RuntimeException("商家不存在");
        }
        return cmBusinessInfoDao.delete(id);
    }


    public CmBusinessInfo getBusinessInfoById(Long id) {
        return cmBusinessInfoDao.selectById(id);
    }

    public CmBusinessInfo selectByUserId(Long userId) {
        return cmBusinessInfoDao.selectByUserId(userId);
    }


    /**
     * 分页查询商家信息
     */
    public PageResult<CmBusinessInfoVO> getPage(PageQuery<BusinessInfoQueryDTO> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<CmBusinessInfoVO> list = cmBusinessInfoDao.selectByCondition(pageQuery.getQueryData());
        if (list != null) {
            return new PageResult<>(list);
        }
        return null;
    }


    /**
     * 审核商家
     */
    @Transactional
    public int auditBusiness(Long id, Integer auditStatus, String reason,Integer busStar) {
        CmBusinessInfo info = cmBusinessInfoDao.selectById(id);
        if (info == null) {
            throw new RuntimeException("商家不存在");
        }
        if (auditStatus == 2 && StringUtils.isBlank(reason)) {
            throw new RuntimeException("驳回时必须填写原因");
        }

        if (info.getAuditStatus() != 0) {
            throw new RuntimeException("只能审核待审核状态的商家");
        }

        info.setAuditStatus(auditStatus);
        info.setReason(reason);
        info.setBusStar(busStar);
        info.setUpdateTime(new Date());
        return cmBusinessInfoDao.updateById(info);
    }

    public CmBusinessInfo getBusinessInfoByBusinessNo(String businessNo) {
        CmBusinessInfo cmBusinessInfo = cmBusinessInfoDao.selectByBusinessNo(businessNo);
        return cmBusinessInfo;
    }

    @Transactional
    public int updateOperator(Long id,String operatorNo, String salesmanNo) {
        CmBusinessInfo info = cmBusinessInfoDao.selectById(id);
        if (info == null) {
            throw new RuntimeException("商家不存在");
        }
        if (info.getAuditStatus() != 0) {
            throw new RuntimeException("商家已审核通过，无法修改");
        }
        info.setOperatorNo(operatorNo);
        info.setDevloperNo(salesmanNo);
        info.setUpdateTime(new Date());
        return cmBusinessInfoDao.updateById(info);
    }
    /**
     * 根据省获取商家数量
     *
     * @param province
     * @return
     */
    public Integer getNumberByProvince(String province) {
        return cmBusinessInfoDao.getNumberByProvince(province);
    }
    /**
     * 根据省获取商家信息
     *
     * @param province
     * @return
     */
    public List<CmBusinessInfo> getBusinessInfoByProvince(String province) {
        return cmBusinessInfoDao.getBusinessInfoByProvince(province);
    }

    public Integer getUnAuditBusinessNum(String operatorNo) {
        return cmBusinessInfoDao.getUnAuditBusinessNum(operatorNo);
    }

    public Integer getAuditBusinessNum(String operatorNo) {
        return cmBusinessInfoDao.getAuditBusinessNum(operatorNo);
    }

    public Integer getBusinessNum(String operatorNo) {
        return cmBusinessInfoDao.getBusinessNum(operatorNo);
    }

    public List<CmBusinessInfo> getBusinessList(CmBusinessMange cmBusinessMange) {
        return cmBusinessInfoDao.getBusinessList(cmBusinessMange);
    }

    public List<String> getAreaDropdown(String operatorNo) {
        return cmBusinessInfoDao.getAreaDropdown(operatorNo);
    }

    public List<String> getTimeDropdown(String operatorNo) {
        List<LocalDate> lists =cmBusinessInfoDao.getTimeDropdown(operatorNo);
        // 创建一个用于存储转换后字符串的列表
        List<String> dayStrList = new ArrayList<>();
        for (LocalDate localDate : lists){
            String dayStr = DateUtil.localDateToString(localDate);
            dayStrList.add(dayStr); // 将每次循环的结果添加到列表
        }
        return dayStrList;
    }

    public CmUser getByCreditCode(String creditCode) {
        // 从 cm_business_info 查询
        Long userId = cmBusinessInfoDao.getUserIdByCreditCode(creditCode);

        // 从 cm_business_shop 查询
        if (userId == null) {
            userId = cmBusinessShopDao.getUserIdByCreditCode(creditCode);
        }

        if (userId != null) {
            return cmUserService.getUserById(userId);
        }

        return null;
    }

}
