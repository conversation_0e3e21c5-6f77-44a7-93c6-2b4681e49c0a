package com.jmt.service;

import com.jmt.base.BaseService;
import com.jmt.dao.CmBoxBookDao;
import com.jmt.model.cm.entity.CmBoxBook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Service
public class CmBoxBookService extends BaseService {
    @Resource
    private CmBoxBookDao cmBoxBookDao;

    @Transactional
    public Long createBooking(CmBoxBook cmBoxBook) {

        if (cmBoxBook.getReservationStatus() == null) {
            cmBoxBook.setReservationStatus(0);
        }

        cmBoxBookDao.insert(cmBoxBook);
        return cmBoxBook.getId();
    }


    public CmBoxBook getBookingById(Long id) {
        if (id == null) {
            return null;
        }
        return cmBoxBookDao.selectById(id);
    }




    @Transactional
    public int updateBooking(CmBoxBook cmBoxBook)  {
        if (cmBoxBook.getId() == null) {
            throw new RuntimeException("包厢id为空");
        }
        CmBoxBook existing = getBookingById(cmBoxBook.getId());
        if (existing == null) {
            throw new RuntimeException("包厢不存在");
        }
        return cmBoxBookDao.update(cmBoxBook);
    }



    @Transactional
    public void deleteBooking(Long id) {
        CmBoxBook book = cmBoxBookDao.selectById(id);
        if (book != null) {
            cmBoxBookDao.deleteById(id);
        }
    }



}
