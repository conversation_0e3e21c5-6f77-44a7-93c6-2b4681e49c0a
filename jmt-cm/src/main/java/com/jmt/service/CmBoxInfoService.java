package com.jmt.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.client.ProfitFeignClient;
import com.jmt.client.UaaFeignClient;
import com.jmt.dao.*;
import com.jmt.enums.PayTypeEnum;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.cm.dto.CmBoxInfoDTO;
import com.jmt.model.cm.dto.BoxPhotoDTO;
import com.jmt.model.cm.dto.FeatureDTO;
import com.jmt.model.cm.entity.*;
import com.jmt.model.cm.vo.CmBoxInfoVO;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.profit.dto.BalancePayDto;
import com.jmt.model.profit.dto.PointPayDTO;
import com.jmt.model.uaa.UaaUserWallet;
import com.jmt.util.GsonUtil;
import com.jmt.util.ResponseUtil;
import com.jmt.util.SysCodeGenerateUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class CmBoxInfoService extends BaseService {
    @Resource
    private CmBoxInfoDao cmBoxInfoDao;

    @Resource
    private CmBoxInfoDetailDao cmBoxInfoDetailDao;

    @Resource
    private CmBoxInfoPhotoDao cmBoxInfoPhotoDao;

    @Resource
    private CmBusinessShopDao cmBusinessShopDao;

    @Resource
    private CmBoxOrderDao cmBoxOrderDao;

    @Resource
    private UaaFeignClient uaaFeignClient;

    @Resource
    private ProfitFeignClient profitFeignClient;

    @Resource
    private CmUserDao cmUserDao;


    /**
     * 创建包厢
     * @param createDTO
     */
    @Transactional
    public void createBoxInfo(CmBoxInfoDTO createDTO, LoginUaaUser loginUser) {

        Long uaaId = loginUser.getUaaId();
        CmUser user = cmUserDao.selectByUaaId(uaaId);
        if(user.getRoleType() == 0){
            throw new RuntimeException("游客无法创建包厢");
        }

        CmBoxInfo boxInfo = new CmBoxInfo();
        BeanUtil.copyProperties(createDTO, boxInfo);

        boxInfo.setCreateTime(new Date());
        boxInfo.setUpdateTime(new Date());
        boxInfo.setIsDelete(0);
        boxInfo.setBoxNo(SysCodeGenerateUtil.generateUserCode("JMT-P3"));
        cmBoxInfoDao.insert(boxInfo);
        Long generatedId = boxInfo.getId();

        // 处理特色菜和包厢环境
        List<CmBoxInfoDetail> details = processFeatures(createDTO.getFeatures(), boxInfo.getBoxNo());
        if (!details.isEmpty()) {
            cmBoxInfoDetailDao.batchInsert(details);
        }

        //  处理照片
        List<CmBoxInfoPhoto> photos = processPhotos(createDTO.getPhotos(), boxInfo.getBoxNo(), boxInfo.getShopNo());
        if (!photos.isEmpty()) {
            cmBoxInfoPhotoDao.batchInsert(photos);
        }

        //支付
        //创建发布包厢订单
        CmBoxOrder order=new CmBoxOrder();
        order.setCreateTime(new Date());
        order.setUpdateTime(new Date());
        order.setIsDelete(0);
        String orderNo = null;
        do {
            orderNo = SysCodeGenerateUtil.generateProfitCode("002"); // 信息发布
            int count = cmBoxOrderDao.countByOrderNo(orderNo);
            if (count == 0) {
                break;
            }
        } while (true); // 生成唯一编号

        order.setOrderNo(orderNo);
        order.setInfoId(generatedId);
        order.setPayType(createDTO.getPayType());
        order.setPayAmount(createDTO.getPayAmount());
        order.setUserId(user.getId());
        //校验是否设置支付密码
        String walletRes =uaaFeignClient.getWalletByUaaId(uaaId);
        UaaUserWallet wallet= ResponseUtil.getData(walletRes,UaaUserWallet.class);
        if (wallet == null) {
            throw new RuntimeException("用户钱包不存在");
        }
        if (StringUtils.isEmpty(wallet.getPayPassword())){

            throw new RuntimeException("未设置密码");
        }

        //去支付
        PayTypeEnum payType = PayTypeEnum.getByCode(createDTO.getPayType());
        switch (payType) {
            case POINTS_PAY:
                PointPayDTO pointPayDTO = new PointPayDTO();
                pointPayDTO.setUaaId(uaaId);
                pointPayDTO.setPoints(createDTO.getPayAmount().intValue());
                pointPayDTO.setBusUaaId(1L);
                pointPayDTO.setOrderNo(order.getOrderNo());
                pointPayDTO.setPayPassword(createDTO.getPassword());
                String payRes = profitFeignClient.pointsByUaaIdPay(pointPayDTO);
                Map<String, Object> responseMap = GsonUtil.fromJsonMap(payRes);
                String code = Convert.toStr(responseMap.get("code"));
                if(!"200".equals(code)){
                    String errorMsg = Convert.toStr(responseMap.get("msg"));
                    throw new RuntimeException(errorMsg);
                }

                break;
            case BALANCE_PAY:
                BalancePayDto payDTO = new BalancePayDto();
                payDTO.setUaaId(uaaId);
                payDTO.setBalance(createDTO.getPayAmount());
                payDTO.setBusUaaId(1L);
                payDTO.setOrderNo(order.getOrderNo());
                payDTO.setPayPassword(createDTO.getPassword());
                String balPayRes = profitFeignClient.balanceByUaaIdPay(payDTO);
                Map<String, Object> balResponseMap = GsonUtil.fromJsonMap(balPayRes);
                String balCode = Convert.toStr(balResponseMap.get("code"));
                if(!"200".equals(balCode)){
                    String errorMsg = Convert.toStr(balResponseMap.get("msg"));
                    throw new RuntimeException(errorMsg);
                }

                break;
//                case WECHAT_PAY:
//                    break;
//                case ALIPAY_PAY:
//                    break;
            default:
                throw new RuntimeException("不支持的支付方式: " + payType.getMessage());
        }
        order.setOrderStatus(1);
        cmBoxOrderDao.insert(order);
    }

    /**
     * 处理特色菜和包厢环境数据
     */
    private List<CmBoxInfoDetail> processFeatures(List<FeatureDTO> features, String boxNo) {
        List<CmBoxInfoDetail> details = new ArrayList<>();

        if (features != null && !features.isEmpty()) {
            for (FeatureDTO feature : features) {
                CmBoxInfoDetail detail = new CmBoxInfoDetail();
                detail.setBoxNo(boxNo);
                detail.setDataType(feature.getDataType());     // 存储key
                detail.setTextLabel(feature.getTextLabel());  // 存储value
                detail.setCreateTime(new Date());
                detail.setUpdateTime(new Date());
                detail.setIsDelete(0);
                details.add(detail);
            }
        }

        return details;
    }

    /**
     * 处理照片数据
     */
    private List<CmBoxInfoPhoto> processPhotos(List<BoxPhotoDTO> photoDTOs, String boxNo, String shopNo) {
        List<CmBoxInfoPhoto> photos = new ArrayList<>();

        if (photoDTOs != null && !photoDTOs.isEmpty()) {
            for (BoxPhotoDTO dto : photoDTOs) {
                CmBoxInfoPhoto photo = new CmBoxInfoPhoto();
                photo.setBoxNo(boxNo);
                photo.setShopNo(shopNo);
                photo.setPhotoType(dto.getPhotoType());
                photo.setPhotoUrl(dto.getPhotoUrl());
                photo.setCreateTime(new Date());
                photo.setUpdateTime(new Date());
                photo.setIsDelete(0);
                photos.add(photo);
            }
        }

        return photos;
    }

    /**
     * 根据ID删除包厢信息
     * @param id
     */
    @Transactional
    public void deleteBoxInfo(Long id) {
        CmBoxInfo boxInfo = cmBoxInfoDao.selectById(id);
        if (boxInfo != null) {
            cmBoxInfoDao.delete(id);
            cmBoxInfoDetailDao.deleteByBoxNo(boxInfo.getBoxNo());
            cmBoxInfoPhotoDao.deleteByBoxNo(boxInfo.getBoxNo());
        }
    }


    /**
     * 更新包厢信息
     * @param updateDTO
     */
    @Transactional
    public void updateBoxInfo( CmBoxInfoDTO updateDTO) {
        CmBoxInfo existingBox = cmBoxInfoDao.selectById(updateDTO.getId());
        if (existingBox == null) {
            throw new RuntimeException("包厢信息不存在");
        }

        CmBoxInfo boxInfo = new CmBoxInfo();
        BeanUtil.copyProperties(updateDTO, boxInfo);
        boxInfo.setUpdateTime(new Date());

        cmBoxInfoDao.updateById(boxInfo);

        if(updateDTO.getFeatures() != null && !updateDTO.getFeatures().isEmpty()) {
            cmBoxInfoDetailDao.deleteByBoxNo(existingBox.getBoxNo());
            List<CmBoxInfoDetail> details = processFeatures(updateDTO.getFeatures(), existingBox.getBoxNo());
            if (!details.isEmpty()) {
                cmBoxInfoDetailDao.batchInsert(details);
            }
        }
        if(updateDTO.getPhotos() != null && !updateDTO.getPhotos().isEmpty()) {
            cmBoxInfoPhotoDao.deleteByBoxNo(existingBox.getBoxNo());
            List<CmBoxInfoPhoto> photos = processPhotos(updateDTO.getPhotos(), existingBox.getBoxNo(), existingBox.getShopNo());
            if (!photos.isEmpty()) {
                cmBoxInfoPhotoDao.batchInsert(photos);
            }
        }

    }

    /**
     * 根据ID查询完整包厢信息
     * @param id
     * @return
     */
    public CmBoxInfoDTO getBoxInfoById(Long id) {
        // 查询主表信息
        CmBoxInfo boxInfo = cmBoxInfoDao.selectById(id);

        if (boxInfo == null) {
            return null;
        }
        CmBoxInfoDTO dto = new CmBoxInfoDTO();
        BeanUtil.copyProperties(boxInfo, dto);

        // 查询并转换详情信息
        List<CmBoxInfoDetail> details = cmBoxInfoDetailDao.selectByBoxNo(boxInfo.getBoxNo());
        List<FeatureDTO> features = details.stream()
                .map(detail -> new FeatureDTO(detail.getDataType(), detail.getTextLabel()))
                .collect(Collectors.toList());
        dto.setFeatures(features);

        // 查询并转换照片信息
        List<CmBoxInfoPhoto> photos = cmBoxInfoPhotoDao.selectByBoxNo(boxInfo.getBoxNo());
        List<BoxPhotoDTO> photoDTOs = photos.stream()
                .map(photo -> {
                    BoxPhotoDTO photoDTO = new BoxPhotoDTO();
                    photoDTO.setPhotoType(photo.getPhotoType());
                    photoDTO.setPhotoUrl(photo.getPhotoUrl());
                    return photoDTO;
                })
                .collect(Collectors.toList());
        dto.setPhotos(photoDTOs);

        return dto;
    }

    /**
     * 分页查询包厢列表
     * @param pageQuery
     * @return
     */
    public PageResult<CmBoxInfoVO> getBoxInfoList(PageQuery<CmBoxInfo> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());

        // 处理空查询条件
        CmBoxInfo queryCondition = pageQuery.getQueryData() != null ?
                pageQuery.getQueryData() : new CmBoxInfo();

        List<CmBoxInfoVO> list = cmBoxInfoDao.selectByCondition(queryCondition);

        if (list != null && !list.isEmpty()) {
            Map<String, List<FeatureDTO>> allFeatures = getBatchFeatures(list);
            for (CmBoxInfoVO vo : list) {
                vo.setFeatures(allFeatures.get(vo.getBoxNo()));
            }
        }

        return new PageResult<>(list);
    }

    private Map<String, List<FeatureDTO>> getBatchFeatures(List<CmBoxInfoVO> boxList) {
        if (boxList == null || boxList.isEmpty()) {
            return new HashMap<>();
        }

        List<String> boxNos = boxList.stream()
                .map(CmBoxInfoVO::getBoxNo)
                .collect(Collectors.toList());

        // 批量查询详情信息
        List<CmBoxInfoDetail> allDetails = cmBoxInfoDetailDao.selectByBoxNos(boxNos);

        // 按包厢编号分组并转换为FeatureVO列表
        return allDetails.stream()
                .collect(Collectors.groupingBy(
                        CmBoxInfoDetail::getBoxNo,
                        Collectors.mapping(
                                detail -> new FeatureDTO(detail.getDataType(), detail.getTextLabel()),
                                Collectors.toList()
                        )
                ));
    }

    public PageResult<CmBoxInfoVO> getMyBoxInfoList(PageQuery<CmBoxInfo> pageQuery, CmUser cmUser) {
        // 查询门店信息
        CmBusinessShop cmBusinessShop = cmBusinessShopDao.selectByUserId(cmUser.getId());
        if (cmBusinessShop == null || StringUtils.isBlank(cmBusinessShop.getShopNo())) {
            return new PageResult<>(Collections.emptyList());
        }

        // 创建查询条件
        CmBoxInfo queryCondition = Optional.ofNullable(pageQuery.getQueryData())
                .orElse(new CmBoxInfo());
        queryCondition.setShopNo(cmBusinessShop.getShopNo());

        // 执行查询
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<CmBoxInfoVO> list = cmBoxInfoDao.selectByCondition(queryCondition);

        // 处理详情信息
        if (list != null && !list.isEmpty()) {
            Map<String, List<FeatureDTO>> allFeatures = getBatchFeatures(list);
            for (CmBoxInfoVO vo : list) {
                vo.setFeatures(allFeatures.get(vo.getBoxNo()));
            }
        }

        return new PageResult<>(list);
    }





}
