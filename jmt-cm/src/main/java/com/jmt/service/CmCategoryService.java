package com.jmt.service;

import com.jmt.base.BaseService;
import com.jmt.dao.CmCategoryDao;
import com.jmt.model.cm.entity.CmCategory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class CmCategoryService extends BaseService {

    @Resource
    private CmCategoryDao cmCategoryDao;

    @Transactional
    public int add(CmCategory cmCategory) {
        if (cmCategory.getParentId() == null) {
            cmCategory.setParentId(0L);
        }
        cmCategory.setCreateTime(new Date());
        cmCategory.setUpdateTime(new Date());
        cmCategory.setIsDelete(0);
        cmCategory.setCategoryNo( "");

        int affectedRows = cmCategoryDao.insert(cmCategory);

        String categoryNo = "yt" + String.format("%04d", cmCategory.getId());
        cmCategory.setCategoryNo(categoryNo);

        cmCategoryDao.update(cmCategory);

        return affectedRows;
    }

    @Transactional
    public int delete(Long id) {
        CmCategory cmCategory = cmCategoryDao.selectById(id);
        if(cmCategory == null){
            throw new RuntimeException("类别不存在");
        }
        return cmCategoryDao.deleteById(id);
    }

    @Transactional
    public int update(CmCategory cmCategory) {
        CmCategory category = cmCategoryDao.selectById(cmCategory.getId());
        if(category == null){
            throw new RuntimeException("类别不存在");
        }
        cmCategory.setUpdateTime(new Date());
        return cmCategoryDao.update(cmCategory);
    }


    public CmCategory getById(Long id) {
        return cmCategoryDao.selectById(id);
    }


    public List<CmCategory> getAll() {
        return cmCategoryDao.selectAll();
    }
}
