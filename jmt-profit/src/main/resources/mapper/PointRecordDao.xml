<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.PointRecordDao">
    <insert id="insert" parameterType="com.jmt.model.profit.entity.PointRecord" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO profit_points_record (
        uaaId, points, remainPoints,recordType, pointSource, sourceId, remark, createTime, updateTime, isDelete
        ) VALUES (
        #{uaaId}, #{points}, #{remainPoints},#{recordType}, #{pointSource}, #{sourceId}, #{remark}, #{createTime}, #{updateTime}, #{isDelete}
        )
    </insert>

    <select id="getPage" resultType="com.jmt.model.profit.vo.PointRecordVo">
        select
            id,uaaId, points, remainPoints,recordType, pointSource, sourceId, remark, createTime, updateTime, isDelete
        from
            profit_points_record
        where isDelete = 0 and uaaId=#{uaaId}
    </select>

    <select id="getInfo" resultType="com.jmt.model.profit.vo.PointRecordVo">
        select
            id,uaaId, points, remainPoints,recordType, pointSource, sourceId, remark, createTime, updateTime, isDelete
        from
            profit_points_record
        where
            isDelete = 0 and id=#{id}
    </select>
</mapper>
