<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.ProfitShareDivideDao">

    <insert id="insert" parameterType="com.jmt.model.profit.entity.ProfitShareDivide" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO `profit_share_divide`
        (
            `orderNo`,
            `projectId`,
            `projectName`,
            `operatorNo`,
            `eqNo`,
            `profitTotalPoints`,
            `sharePoints`,
            `shareStatus`,
            `uaaId`,
            `createTime`,
            `updateTime`,
            `isDelete`
        )
        VALUES
        (
            #{orderNo},
            #{projectId},
            #{projectName},
            #{operatorNo},
            #{eqNo},
            #{profitTotalPoints},
            #{sharePoints},
            0,
            #{uaaId},
            NOW(),
            NOW(),
            0
        )
    </insert>

    <select id="getPage" resultType="com.jmt.model.profit.vo.ProfitShareDivideVo">
        SELECT
            `id`,
            `orderNo`,
            `projectId`,
            `projectName`,
            `operatorNo`,
            `eqNo`,
            `profitTotalPoints`,
            `sharePoints`,
            `shareStatus`,
            `uaaId`,
            `createTime`,
            `updateTime`,
            `isDelete`
        FROM
            profit_share_divide
        WHERE
            isDelete = 0 AND shareStatus = 0 AND uaaId=#{uaaId}
            <if test="projectId != null">
                AND projectId = #{projectId}
            </if>
    </select>

    <select id="getTotalPoints" resultType="java.lang.Integer">
        SELECT
            IFNULL(SUM(sharePoints),0) AS totalPoints
        FROM
            profit_share_divide
        WHERE
            isDelete = 0 AND shareStatus = 0 AND uaaId=#{uaaId}
    </select>

</mapper>
