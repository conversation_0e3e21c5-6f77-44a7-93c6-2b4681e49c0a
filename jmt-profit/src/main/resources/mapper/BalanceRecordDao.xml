<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace名字必须与Mapper接口名一致 -->

<mapper namespace="com.jmt.dao.BalanceRecordDao">
	<insert id="add">
		INSERT INTO profit_balance_record
		(uaaId,balance,remainBalance,recordType,balanceSource,sourceId,remark,createTime,updateTime,isDelete)
		VALUES
		(#{uaaId},#{balance},#{remainBalance},#{recordType},#{balanceSource},#{sourceId},#{remark},NOW(),NOW(),0)
	</insert>
    <select id="getInfoByUaaId" resultType="com.jmt.model.profit.entity.BalanceRecord">
		SELECT
			id,
			uaaId,
			balance,
			remainBalance
			recordType,
			balanceSource,
			sourceId,
			remark,
			createTime,
			updateTime,
			isDelete
		FROM
			profit_balance_record
		WHERE
			uaaId = #{uaaId} AND isDelete = 0
	</select>

	<select id="getPage" resultType="com.jmt.model.profit.vo.BalanceRecordVo">
		SELECT
			id,
			uaaId,
			balance,
			remainBalance
			recordType,
			balanceSource,
			sourceId,
			remark,
			createTime,
			updateTime,
			isDelete
		FROM
			profit_balance_record
		WHERE
			isDelete = 0 AND uaaId = #{uaaId}
	</select>

	<select id="getInfo" resultType="com.jmt.model.profit.vo.BalanceRecordVo">
		SELECT
			id,
			uaaId,
			balance,
			remainBalance
			recordType,
			balanceSource,
			sourceId,
			remark,
			createTime,
			updateTime,
			isDelete
		FROM
			profit_balance_record
		WHERE
			isDelete = 0 AND
	</select>
</mapper>
