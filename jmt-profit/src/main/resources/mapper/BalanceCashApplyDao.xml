<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace名字必须与Mapper接口名一致 -->

<mapper namespace="com.jmt.dao.BalanceCashApplyDao">
	<insert id="add" parameterType="com.jmt.model.profit.entity.BalanceCashApply">
		INSERT INTO profit_balance_cash_apply
		    (applyUser,applyUserName,clientId,applyNo,balance,bankId,bankName,cardNo,accountName,auditStatus,reason,auditUser,auditUserName,createTime,updateTime,isDelete)
		VALUES
		    (#{applyUser},#{applyUserName},#{clientId},#{applyNo},#{balance},#{bankId},#{bankName},#{cardNo},#{accountName},0,#{reason},#{auditUser},#{auditUserName},NOW(),NOW(),0)
	</insert>
	<update id="audit">
		UPDATE profit_balance_cash_apply
		<set>
			<if test="auditStatus != null">
				 auditStatus = #{auditStatus},
			</if>
			<if test="reason != null">
				reason = #{reason},
			</if>
			<if test="auditUser != null">
				auditUser = #{auditUser},
			</if>
			<if test="auditUserName != null">
				auditUserName = #{auditUserName},
			</if>
				updateTime = NOW()
		</set>
		WHERE
			id = #{id}
	</update>
	<select id="getCountByUaaIdAndCreateTime" resultType="java.lang.Integer">
		SELECT
			COUNT(1)
		FROM
			profit_balance_cash_apply
		WHERE
			applyUser = #{applyUser}
		  	AND createTime BETWEEN #{startDay} AND #{endDay}
		  	AND isDelete = 0
	</select>
	<select id="getPage" parameterType="com.jmt.model.profit.dto.BalanceCashApplyPageQueryDto" resultType="com.jmt.model.profit.vo.BalanceCashApplyVo">
		SELECT
		    id,
		    applyUser,
			applyUserName,
			clientId,
		    applyNo,
		    balance,
		    bankId,
			bankName,
			cardNo,
			accountName,
		    auditStatus,
		    reason,
			auditUser,
			auditUserName,
		    createTime,
		    updateTime,
		    isDelete
		FROM
			profit_balance_cash_apply
		<where>
			<if test="applyUserName != null">
				applyUserName LIKE CONCAT('%',#{applyUserName},'%')
			</if>
			<if test="auditStatus != null">
				auditStatus = #{auditStatus}
			</if>
			<if test="applyNo != null">
				applyNo LIKE CONCAT('%',#{applyNo},'%')
			</if>
			<if test="true">
				AND isDelete = 0
			</if>
		</where>
	</select>
	<select id="getInfo" resultType="com.jmt.model.profit.vo.BalanceCashApplyVo">
		SELECT
			id,
			applyUser,
			applyUserName,
			clientId,
			applyNo,
			balance,
			bankId,
			bankName,
			cardNo,
			accountName,
			auditStatus,
			reason,
			auditUser,
			auditUserName,
			createTime,
			updateTime,
			isDelete
		FROM
			profit_balance_cash_apply
		WHERE
			id = #{id} AND isDelete = 0
	</select>
    <select id="getBalanceCashApplyCount" resultType="java.lang.Integer">
		SELECT
			COUNT(1)
		FROM
			profit_balance_cash_apply
		WHERE
			uaaId = #{uaaId} AND isDelete = 0
	</select>

	<select id="getPendingBalanceCashApplyByApplyUser" resultType="com.jmt.model.profit.vo.BalanceCashApplyVo">
		SELECT
			id,
			applyUser,
			applyUserName,
			clientId,
			applyNo,
			balance,
			bankId,
			bankName,
			cardNo,
			accountName,
			auditStatus,
			reason,
			auditUser,
			auditUserName,
			createTime,
			updateTime,
			isDelete
		FROM
			profit_balance_cash_apply
		WHERE
			applyUser = #{applyUser} AND isDelete = 0 AND auditStatus = 0
	</select>

	<update id="complete">
		UPDATE profit_balance_cash_apply
		<set>
			<if test="auditStatus != null">
				auditStatus = #{auditStatus},
			</if>
				updateTime = NOW()
		</set>
		WHERE
			id = #{id}
	</update>
</mapper>
