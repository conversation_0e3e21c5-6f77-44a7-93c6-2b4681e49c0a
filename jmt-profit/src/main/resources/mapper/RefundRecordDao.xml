<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.RefundRecordDao">

    <sql id="Base_Column_List">
        id, seqNo, orderNo, rechargeUser,rechargeType, rechargeAmount, rechargeStatus, reason, createTime, updateTime, isDelete
    </sql>

    <insert id="insert" parameterType="com.jmt.model.profit.entity.RefundRecord" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO profit_recharge_record (
        seqNo,orderNo,rechargeUser, rechargeType, rechargeAmount, rechargeStatus, reason, createTime, updateTime, isDelete
        ) VALUES (
        #{seqNo},#{orderNo},#{rechargeUser}, #{rechargeType}, #{rechargeAmount}, #{rechargeStatus}, #{reason}, #{createTime}, #{updateTime}, #{isDelete}
        )
    </insert>

    <select id="selectByCondition" parameterType="com.jmt.model.profit.dto.RefundRecordDTO" resultType="com.jmt.model.profit.dto.RefundRecordDTO">
        SELECT <include refid="Base_Column_List"/>
        FROM profit_recharge_record
        <where>
            isDelete = 0
            <if test="seqNo != null and seqNo != ''">AND seqNo = #{seqNo}</if>
            <if test="orderNo != null and orderNo != ''">AND orderNo = #{orderNo}</if>
            <if test="rechargeUser != null">AND rechargeUser = #{rechargeUser}</if>
            <if test="rechargeType != null">AND rechargeType = #{rechargeType}</if>
            <if test="rechargeAmount != null">AND rechargeAmount = #{rechargeAmount,jdbcType=DECIMAL}</if>
            <if test="rechargeStatus != null">AND rechargeStatus = #{rechargeStatus}</if>
            <if test="reason != null and reason != ''">AND reason LIKE CONCAT('%', #{reason}, '%')</if>
        </where>
        ORDER BY createTime
    </select>
</mapper>
