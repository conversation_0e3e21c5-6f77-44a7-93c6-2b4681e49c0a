package com.jmt.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

//@Configuration
//@PropertySource("classpath:lklpay.properties")
//@ConfigurationProperties(prefix = "lklpay")
//@Data
//public class LakalaPayConfig {
//    private String appId;
//    private String serialNo;
//    private String priKeyPath;
//    private String lklCerPath;
//    private String lklNotifyCerPath;
//    private String sm4Key;
//    private String serverUrl;
//}
