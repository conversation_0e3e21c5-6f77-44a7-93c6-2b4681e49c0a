package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.client.UaaFeignClient;
import com.jmt.constant.JmtConstant;
import com.jmt.constant.ProfitConstant;
import com.jmt.dao.*;
import com.jmt.enums.AuditStatusEnum;
import com.jmt.enums.BalanceConstantEnum;
import com.jmt.exception.BusinessException;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.profit.vo.BalanceCashApplyVo;
import com.jmt.model.profit.dto.*;
import com.jmt.model.profit.entity.BalanceCashApply;
import com.jmt.model.uaa.UaaUserBankcard;
import com.jmt.model.uaa.UaaUserWallet;
import com.jmt.util.ProfitUserNoUtil;
import com.jmt.util.ResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

@Service
@Slf4j
public class BalanceCashService extends BaseService {

    @Resource
    private UaaFeignClient uaaFeignClient;

    @Resource
    private BalanceCashApplyDao balanceCashApplyDao;

    @Resource
    private BalanceRecordService balanceRecordService;

    /**
     * 申请提现
     * @param balanceCashApplyDto 申请提现参数
     * @param applyUser 申请人uaaId
     */
    @Transactional(rollbackFor = {Exception.class,BusinessException.class})
    public Integer apply(BalanceCashApplyDto balanceCashApplyDto, LoginUaaUser applyUser) {
        Long applyUserId = applyUser.getUaaId();
        String userName = applyUser.getUsername();
        String clientId = applyUser.getClientId();
        //获取申请人待审核的申请单
        List<BalanceCashApplyVo> balanceCashApplyVoList = balanceCashApplyDao.getPendingBalanceCashApplyByApplyUser(applyUserId);
        if (balanceCashApplyVoList.size() > 0) {
            throw new BusinessException("有未审核通过的申请");
        }
        // 校验用户存在
        UaaUserWallet uaaUserWallet = balanceRecordService.getWallet(applyUserId);
        if (uaaUserWallet == null) {
            throw new BusinessException("用户钱包不存在");
        }
        // 获取余额配置，校验积分是否满足兑现条件
        BigDecimal balance = balanceCashApplyDto.getBalance();
        // 校验用户余额是否足够
        if (uaaUserWallet.getBalance().compareTo(balance)<0) {
            throw new BusinessException("用户余额不足");
        }
        //查看银行卡是否存在
        Long bankId = balanceCashApplyDto.getBankId();
        String bankResponse = uaaFeignClient.getBankcardById(bankId);
        UaaUserBankcard userBankcard = ResponseUtil.getData(bankResponse,UaaUserBankcard.class);
        if (userBankcard == null){
            throw new BusinessException("银行卡不存在");
        }
        BalanceCashApply balanceCashApply = new BalanceCashApply();
        //扣减用户余额
        String applyNo = ProfitUserNoUtil.generate("006");
        // 根据是否达到金额下限决定是否需要审核
        if (balance.compareTo(BalanceConstantEnum.INSTANCE.getMinAuditBalance()) > 0) {
            balanceCashApply.setAuditStatus(AuditStatusEnum.PENDING.getCode());// 待审核
        } else {
            balanceCashApply.setAuditStatus(AuditStatusEnum.APPROVED.getCode());// 无需审核
        }
        balanceCashApply.setApplyNo(applyNo);
        balanceCashApply.setApplyUser(applyUserId);
        balanceCashApply.setApplyUserName(userName);
        balanceCashApply.setClientId(clientId);
        balanceCashApply.setBankId(bankId);
        balanceCashApply.setAccountName(userBankcard.getAccountName());
        balanceCashApply.setBankName(userBankcard.getBankName());
        balanceCashApply.setCardNo(userBankcard.getCardNo());

        Integer r = balanceCashApplyDao.add(balanceCashApply);
        if (r == 1) {
            String decreaseBalanceResponse = uaaFeignClient.decreaseBalance(applyUserId, balance);
            Integer code = ResponseUtil.getCode(decreaseBalanceResponse);
            if (!code.equals(JmtConstant.SERVICE_RESPONSE_SUCCESS_CODE)) {
                throw new BusinessException("扣减用户余额失败");
            }
        }
        return r;
    }

    /**
     * 获取用户余额提现申请列表
     * @param pageQuery 分页参数
     * @return PageResult
     */
    public PageResult<BalanceCashApplyVo> getPage(PageQuery<BalanceCashApplyPageQueryDto> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<BalanceCashApplyVo> page = balanceCashApplyDao.getPage(pageQuery.getQueryData());
        if (page != null){
            return new PageResult<>(page);
        }
        return null;
    }
    @Transactional(rollbackFor = {Exception.class, BusinessException.class})
    public Integer audit(BalanceCashApplyAuditDto balanceApplyAuditDto, LoginUaaUser auditUser) {
        Long id = balanceApplyAuditDto.getId();
        Integer auditStatus = balanceApplyAuditDto.getAuditStatus();
        String reason = balanceApplyAuditDto.getReason();
        //查询余额兑现申请
        BalanceCashApplyVo balanceCashApplyVo = balanceCashApplyDao.getInfo(id);
        if (balanceCashApplyVo == null){
            throw new BusinessException("申请不存在");
        }
        Long applyUser = balanceCashApplyVo.getApplyUser();
        //校验申请状态
        if (!balanceCashApplyVo.getAuditStatus().equals(AuditStatusEnum.PENDING.getCode())){
            throw new BusinessException("申请已处理");
        }
        //校验审核状态
        if (!(auditStatus.equals(AuditStatusEnum.APPROVED.getCode())||auditStatus.equals(AuditStatusEnum.REJECTED.getCode()))){
            throw new BusinessException("审核状态有误");
        }
        //更新申请表申请状态
        Integer r = balanceCashApplyDao.audit(id,auditStatus,reason,auditUser.getUaaId(),auditUser.getUsername());
        if (r == 1) {
            //计算兑现金额(审核通过)
            if (auditStatus.equals(AuditStatusEnum.REJECTED.getCode())){
                //审核拒绝，退还余额
                String increaseBalance = uaaFeignClient.increaseBalance(applyUser, balanceCashApplyVo.getBalance());
                Integer code = ResponseUtil.getCode(increaseBalance);
                if (!code.equals(JmtConstant.SERVICE_RESPONSE_SUCCESS_CODE)) {
                   throw  new BusinessException("退还余额失败");
                }
            }
        }
        return r;
    }


    public BalanceCashApplyVo getInfo(Long id) {
        return balanceCashApplyDao.getInfo(id);
    }

    @Transactional(rollbackFor = {Exception.class,BusinessException.class})
    public Integer toTransfer(Long id) {
        //查询余额兑现申请
        BalanceCashApplyVo balanceCashApplyVo = balanceCashApplyDao.getInfo(id);
        if (balanceCashApplyVo == null){
            throw new BusinessException("申请不存在");
        }
        //校验申请状态
        if (!balanceCashApplyVo.getAuditStatus().equals(AuditStatusEnum.APPROVED.getCode())){
            throw new BusinessException("申请单未通过或已完成");
        }
        balanceRecordService.recordBalanceOutcome(balanceCashApplyVo.getApplyUser(),balanceCashApplyVo.getBalance(), ProfitConstant.BALANCE_CASH);
        return balanceCashApplyDao.complete(id,AuditStatusEnum.COMPLETED.getCode());
    }
}
