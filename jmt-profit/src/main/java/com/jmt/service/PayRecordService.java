package com.jmt.service;

import com.jmt.base.BaseService;
import com.jmt.dao.PayRecordDao;
import com.jmt.enums.PayStatusEnum;
import com.jmt.model.profit.dto.PayRecordDTO;
import com.jmt.model.profit.entity.PayRecord;
import com.jmt.util.WorkNoGeneratorUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class PayRecordService extends BaseService {

    @Resource
    private PayRecordDao payRecordDao;

    @Transactional
    public Integer insert(PayRecord record) {
        record.setSeqNo(WorkNoGeneratorUtil.generate("PEQ"));
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
        record.setIsDelete(0);
        record.setPayStatus(PayStatusEnum.UNPAID.getCode());
        return payRecordDao.insert(record);
    }

    public List<PayRecordDTO> selectByCondition(PayRecordDTO payRecordDTO) {
        return payRecordDao.selectByCondition(payRecordDTO);
    }

    public PayRecord selectByOrderNo(String orderNo) {
        return payRecordDao.selectByOrderNo(orderNo);
    }
}
