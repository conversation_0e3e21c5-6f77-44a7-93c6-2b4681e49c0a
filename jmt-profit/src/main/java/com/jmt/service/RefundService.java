package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.client.UaaFeignClient;
import com.jmt.dao.RefundRecordDao;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.profit.dto.RefundRecordDTO;
import com.jmt.model.profit.dto.RefundDTO;
import com.jmt.model.profit.entity.PayRecord;
import com.jmt.model.profit.entity.RefundRecord;
import com.jmt.util.WorkNoGeneratorUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.jmt.enums.PayTypeEnum;

import javax.annotation.Resource;
import java.util.List;

@Service
public class RefundService extends BaseService {

    @Resource
    private RefundRecordDao refundRecordDao;

    @Resource
    private PayRecordService payRecordService;

    @Resource
    private UaaFeignClient uaaFeignClient;

    /**
     * 分页查询退款流水
     * @param pageQuery
     * @return
     */
    public PageResult<RefundRecordDTO> getPage(PageQuery<RefundRecordDTO> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        RefundRecordDTO profitPayRecordDTO = pageQuery.getQueryData();
        List<RefundRecordDTO> list = refundRecordDao.selectByCondition(profitPayRecordDTO);
        if (list != null) {
            return new PageResult<>(list);
        }
        return null;
    }
    @Transactional(rollbackFor = Exception.class)
    public void submit(RefundDTO dto) {
        PayRecord payRecord = payRecordService.selectByOrderNo(dto.getOrderNo());

        //  校验订单是否可退款
        if(payRecord.getPayStatus()!=1){
            throw new IllegalArgumentException("未完成支付，无法退款");
        }


        try {
            boolean refundSuccess = false;
            PayTypeEnum payType = PayTypeEnum.getByCode(payRecord.getPayType());

            switch (payType) {
                case POINTS_PAY:
                    refundSuccess = rechargeByPoints(payRecord,dto.getBusUaaId());

                    break;
                case BALANCE_PAY:
                    refundSuccess = rechargeByBalance(payRecord,dto.getBusUaaId());

                    break;
//                case WECHAT_PAY:
//                    break;
//                case ALIPAY_PAY:
//                    break;
                default:
                    throw new RuntimeException("不支持的支付方式: " + payType.getMessage());
            }

            // 更新退款流水
            if (refundSuccess) {
                RefundRecord record = buildRechargeRecord(payRecord,payRecord.getPayType());
                refundRecordDao.insert(record);
            } else {
                throw new RuntimeException("退款处理失败:" );
            }
        } catch (Exception e) {
            throw new RuntimeException("退款处理异常: " + e.getMessage());
        }
    }
    private RefundRecord buildRechargeRecord(PayRecord payRecord, int rechargeType) {
        return RefundRecord.builder()
                .seqNo(WorkNoGeneratorUtil.generate("REQ"))
                .orderNo(payRecord.getOrderNo())
                .rechargeUser(payRecord.getPayUser())
                .rechargeAmount(payRecord.getPayAmount())
                .rechargeType(rechargeType)
                .rechargeStatus(1)
                .isDelete(0)
                .build();
    }

    // 积分退款
    private boolean rechargeByPoints(PayRecord payRecord, Long busUaaId) {
        // 返还用户积分
        uaaFeignClient.increasePoints(payRecord.getPayUser(), payRecord.getPayAmount().intValue());
        uaaFeignClient.decreasePoints(busUaaId, payRecord.getPayAmount().intValue());
        return true;
    }

    // 余额退款
    private boolean rechargeByBalance(PayRecord payRecord, Long busUaaId) {
        // 返还用户余额
        uaaFeignClient.increaseBalance(payRecord.getPayUser(), payRecord.getPayAmount());
        uaaFeignClient.decreaseBalance(busUaaId, payRecord.getPayAmount());
        return true;
    }





}
