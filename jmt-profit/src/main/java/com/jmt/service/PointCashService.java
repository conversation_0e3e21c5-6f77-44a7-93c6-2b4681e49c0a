package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.client.UaaFeignClient;

import com.jmt.constant.JmtConstant;
import com.jmt.constant.ProfitConstant;
import com.jmt.dao.*;
import com.jmt.enums.AuditStatusEnum;
import com.jmt.enums.PointConstantEnum;
import com.jmt.exception.BusinessException;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.profit.dto.*;
import com.jmt.model.profit.entity.PointCashApply;
import com.jmt.model.uaa.UaaUserWallet;
import com.jmt.util.ProfitUserNoUtil;
import com.jmt.util.ResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

@Service
@Slf4j
public class PointCashService extends BaseService {

    @Resource
    private UaaFeignClient uaaFeignClient;

    @Resource
    private PointCashApplyDao pointCashApplyDao;

    @Resource
    private BalanceRecordService balanceRecordService;

    @Resource
    private PointRecordService pointRecordService;

    /**
     * 申请积分兑现
     * @param pointCashApplyDto 申请参数
     * @param applyUser 申请人
     */
    @Transactional(rollbackFor = {Exception.class,BusinessException.class})
    public Integer apply(PointCashApplyDto pointCashApplyDto, LoginUaaUser applyUser) {
        Long applyUserId = applyUser.getUaaId();
        String clientId = applyUser.getClientId();
        //获取申请人待审核的申请单
        List<PointCashApply> pointCashApplyList = pointCashApplyDao.getPendingPointCashApplyByApplyUser(applyUserId);
        if (pointCashApplyList.size() > 0) {
            throw new BusinessException("有未审核通过的");
        }
        String applyUserName = applyUser.getUsername();
        // 1. 校验用户存在
        PointCashApply pointCashApply = new PointCashApply();
        BeanUtils.copyProperties(pointCashApplyDto, pointCashApply);
        UaaUserWallet uaaUserWallet = balanceRecordService.getWallet(applyUserId);
        // 2. 获取积分配置，校验积分是否满足兑现条件
        Integer points = pointCashApplyDto.getPoints();
        this.validatePointsCashCondition(uaaUserWallet,points);
        String applyNo = ProfitUserNoUtil.generate("005");
        pointCashApply.setApplyNo(applyNo);
        pointCashApply.setApplyUser(applyUserId);
        pointCashApply.setApplyUserName(applyUserName);
        pointCashApply.setClientId(clientId);

        if (pointCashApplyDto.getInvoiceProvide() == 1) {
            pointCashApply.setAuditStatus(AuditStatusEnum.PENDING.getCode());// 待审核
        }else{
            pointCashApply.setAuditStatus(AuditStatusEnum.APPROVED.getCode());// 无需审核
            //BigDecimal finalAmount = this.toBalance(points);转换成余额
        }
        Integer r = pointCashApplyDao.add(pointCashApply);
        if (r == 1) {
            String decreasePoints = uaaFeignClient.decreasePoints(applyUserId, points);
            Integer code = ResponseUtil.getCode(decreasePoints);
            if (!code.equals(JmtConstant.SERVICE_RESPONSE_SUCCESS_CODE)) {
                throw new BusinessException("扣减积分失败");
            }
        }
        return r;
    }

    public BigDecimal calculate(Integer points) {
        // 积分转余额基准比例
        BigDecimal baseRate = new BigDecimal(PointConstantEnum.INSTANCE.getPointRatio());
        // 计算原始金额
        BigDecimal originalAmount = new BigDecimal(points).multiply(baseRate);
        //计算可兑现余额(扣税)
        BigDecimal taxRate = new BigDecimal(PointConstantEnum.INSTANCE.getTaxRatio()); // 税率
        BigDecimal taxAmount = originalAmount.multiply(taxRate);  // 税费 = 原始金额 * 税率
        return originalAmount.subtract(taxAmount);
    }

    /**
     * 获取积分申请列表
     * @param pageQuery 查询参数
     * @return PageResult
     */
    public PageResult<PointCashApply> getPage(PageQuery<PointCashApplyPageQueryDto> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<PointCashApply> page = pointCashApplyDao.getPage(pageQuery.getQueryData());
        if (page != null){
            return new PageResult<>(page);
        }
        return null;
    }
    /**
     * 审核
     * @return Integer
     */
    @Transactional(rollbackFor = {Exception.class,BusinessException.class})
    public Integer audit(PointCashApplyAuditDto pointApplyAuditDto, LoginUaaUser auditUser) {
        Long id = pointApplyAuditDto.getId();
        Integer auditStatus = pointApplyAuditDto.getAuditStatus();
        String reason = pointApplyAuditDto.getReason();

        //查询积分兑现申请
        PointCashApply pointCashApply = pointCashApplyDao.getInfo(id);
        if (pointCashApply == null){
            throw new BusinessException("申请单不存在");
        }
        //校验申请状态
        if (!pointCashApply.getAuditStatus().equals(AuditStatusEnum.PENDING.getCode())){
            throw new BusinessException("申请已处理");
        }
        //校验审核状态
        if (!(auditStatus.equals(AuditStatusEnum.APPROVED.getCode())||!auditStatus.equals(AuditStatusEnum.REJECTED.getCode()))){
            throw new BusinessException("审核状态有误");
        }
        Long applyUserId = pointCashApply.getApplyUser();
        Integer r = pointCashApplyDao.audit(id,auditStatus,reason,auditUser.getUaaId(),auditUser.getUsername());
        //审核不通过 退还积分
        if (!pointApplyAuditDto.getAuditStatus().equals(AuditStatusEnum.APPROVED.getCode())){
            String increasePoints = uaaFeignClient.increasePoints(applyUserId, pointCashApply.getPoints());
            Integer code = ResponseUtil.getCode(increasePoints);
            if (!code.equals(JmtConstant.SERVICE_RESPONSE_SUCCESS_CODE)) {
                throw new BusinessException("退还积分失败");
            }
        }
        return r;
    }
    /**
     * 校验积分兑现条件
     */
    private void validatePointsCashCondition(UaaUserWallet userWallet, Integer points) {
        // 校验最低兑现积分
        if (points <= PointConstantEnum.INSTANCE.getMinPoint()){
            throw new BusinessException("积分低于最低提现限制");
        }
        // 校验用户积分是否足够
        if (userWallet.getPoints() < points) {
            throw new BusinessException("用户积分不足");
        }
        // 校验最高兑现积分
        if (points > PointConstantEnum.INSTANCE.getMaxPoint()) {
            throw new BusinessException("积分高于最高提现限制");
        }
    }

    public PointCashApply getInfo(Long id) {
        return pointCashApplyDao.getInfo(id);
    }

    @Transactional(rollbackFor = {Exception.class,BusinessException.class})
    public Integer toBalance(Long id) {
        //查询积分兑现申请
        PointCashApply pointCashApply = pointCashApplyDao.getInfo(id);
        if (pointCashApply == null){
            throw new BusinessException("申请单不存在");
        }
        //校验申请状态
        if (!pointCashApply.getAuditStatus().equals(AuditStatusEnum.APPROVED.getCode())){
            throw new BusinessException("申请单未通过或已完成");
        }
        Long applyUser = pointCashApply.getApplyUser();
        Integer points = pointCashApply.getPoints();
        String applyNo = pointCashApply.getApplyNo();
        BigDecimal addBalance = this.calculate(pointCashApply.getPoints());
        Integer r = pointCashApplyDao.complete(id, AuditStatusEnum.COMPLETED.getCode());
        Integer r1 = pointRecordService.recordPointsOutcome(applyUser,points, ProfitConstant.POINT_CASH);
        Integer r2 = balanceRecordService.recordBalanceIncome(applyUser,addBalance,applyNo,ProfitConstant.POINT_CASH);
        if (r == 1 && r1 == 1 && r2 == 1) {
            String increaseBalance = uaaFeignClient.increaseBalance(pointCashApply.getApplyUser(), addBalance);
            Integer code = ResponseUtil.getCode(increaseBalance);
            if (!code.equals(JmtConstant.SERVICE_RESPONSE_SUCCESS_CODE)) {
                throw new BusinessException("增加余额失败");
            }
        }
        return r;
    }
}
