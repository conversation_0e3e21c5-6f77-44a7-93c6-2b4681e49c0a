package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.dao.ProfitShareDivideDao;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.profit.dto.ProfitSharePageQueryDto;
import com.jmt.model.profit.vo.ProfitShareDivideVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class ProfitShareDivideService extends BaseService {

    @Resource
    private ProfitShareDivideDao profitShareDivideDao;


    public Integer getTotalPoints(Long uaaId) {
       return profitShareDivideDao.getTotalPoints(uaaId);
    }
    /**
     * 获取预收益列表
     */
    public PageResult<ProfitShareDivideVo> getPage(PageQuery<ProfitSharePageQueryDto> pageQuery, LoginUaaUser loginUaaUser) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        ProfitSharePageQueryDto dto = pageQuery.getQueryData();
        dto.setUaaId(loginUaaUser.getUaaId());
        List<ProfitShareDivideVo> page = profitShareDivideDao.getPage(dto);
        if (page != null){
            return new PageResult<>(page);
        }
        return null;
    }

}
