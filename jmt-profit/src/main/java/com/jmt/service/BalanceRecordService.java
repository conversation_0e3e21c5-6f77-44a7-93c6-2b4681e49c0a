package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.client.UaaFeignClient;
import com.jmt.dao.BalanceRecordDao;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.profit.dto.BalanceRecordPageQueryDto;
import com.jmt.model.profit.entity.BalanceRecord;
import com.jmt.model.profit.vo.BalanceRecordVo;
import com.jmt.model.uaa.UaaUserWallet;
import com.jmt.util.ResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

@Service
@Slf4j
public class BalanceRecordService extends BaseService {

    @Resource
    private BalanceRecordDao balanceRecordDao;

    @Resource
    private UaaFeignClient uaaFeignClient;

    /**
     * 获取用户余额提现申请列表
     * @param pageQuery 分页参数
     * @param loginUaaUser
     * @return PageResult
     */
    public PageResult<BalanceRecordVo> getPage(PageQuery<BalanceRecordPageQueryDto> pageQuery, LoginUaaUser loginUaaUser) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        BalanceRecordPageQueryDto dto = pageQuery.getQueryData();
        dto.setUaaId(loginUaaUser.getUaaId());
        List<BalanceRecordVo> page = balanceRecordDao.getPage(dto);
        if (page != null){
            return new PageResult<>(page);
        }
        return null;
    }


    public BalanceRecordVo getInfo(Long id) {
        return balanceRecordDao.getInfo(id);
    }

    //记录余额变动(只能收入)
    public Integer recordBalanceIncome(Long applyUser, BigDecimal amount, String sourceId, String remark) {
        BalanceRecord balanceRecord = new BalanceRecord();
        balanceRecord.setSourceId(sourceId);
        balanceRecord.setUaaId(applyUser);
        balanceRecord.setBalance(amount);
        balanceRecord.setRecordType(0);
        balanceRecord.setRemark(remark);
        UaaUserWallet wallet = this.getWallet(applyUser);
        if (wallet != null) {
            balanceRecord.setRemainBalance(wallet.getBalance());
        }
        return balanceRecordDao.add(balanceRecord);
    }
    //记录余额变动(只能支出)
    public Integer recordBalanceOutcome(Long applyUser, BigDecimal amount,String remark) {
        BalanceRecord balanceRecord = new BalanceRecord();
        balanceRecord.setUaaId(applyUser);
        balanceRecord.setBalance(amount);
        balanceRecord.setRecordType(1);
        balanceRecord.setRemark(remark);
        UaaUserWallet wallet = this.getWallet(applyUser);
        if (wallet != null) {
            balanceRecord.setRemainBalance(wallet.getBalance());
        }
        return balanceRecordDao.add(balanceRecord);
    }


    //获取钱包
    public UaaUserWallet getWallet(Long applyUser) {
        String response = uaaFeignClient.getWalletByUaaId(applyUser);
        return ResponseUtil.getData(response,UaaUserWallet.class);
    }
}
