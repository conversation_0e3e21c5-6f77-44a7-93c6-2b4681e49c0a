package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.constant.ProfitConstant;
import com.jmt.dao.PointRecordDao;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.profit.dto.PointRecordPageQueryDto;
import com.jmt.model.profit.entity.PointRecord;
import com.jmt.model.profit.vo.PointRecordVo;
import com.jmt.model.uaa.UaaUserWallet;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class PointRecordService extends BaseService {

    @Resource
    private PointRecordDao pointRecordDao;

    @Resource
    private BalanceRecordService balanceRecordService;


    /**
     * 获取积分申请列表
     * @param pageQuery 查询参数
     * @param loginUaaUser
     * @return PageResult
     */
    public PageResult<PointRecordVo> getPage(PageQuery<PointRecordPageQueryDto> pageQuery, LoginUaaUser loginUaaUser) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        PointRecordPageQueryDto dto = pageQuery.getQueryData();
        dto.setUaaId(loginUaaUser.getUaaId());
        List<PointRecordVo> page = pointRecordDao.getPage(dto);
        if (page != null){
            return new PageResult<>(page);
        }
        return null;
    }
    /**
     * 记录积分支出
     */
    public Integer recordPointsOutcome(Long applyUser, Integer points,String remark) {
        PointRecord pointsRecord = new PointRecord();
        pointsRecord.setUaaId(applyUser);
        pointsRecord.setPoints(points);
        pointsRecord.setRecordType(ProfitConstant.OUTCOME);//支出
        pointsRecord.setRemark(remark);
        UaaUserWallet wallet = balanceRecordService.getWallet(applyUser);
        if (wallet != null) {
            pointsRecord.setRemainPoints(wallet.getPoints());
        }
        return pointRecordDao.insert(pointsRecord);
    }
    /**
     * 记录积分收入
     */
    public Integer recordPointsIncome(Long applyUser, Integer points,Integer pointSource, String sourceId,String remark) {
        PointRecord pointsRecord = new PointRecord();
        pointsRecord.setUaaId(applyUser);
        pointsRecord.setPoints(points);
        pointsRecord.setRecordType(ProfitConstant.INCOME);//收入
        pointsRecord.setPointSource(pointSource);
        pointsRecord.setSourceId(sourceId);
        pointsRecord.setRemark(remark);
        UaaUserWallet wallet = balanceRecordService.getWallet(applyUser);
        if (wallet != null) {
            pointsRecord.setRemainPoints(wallet.getPoints());
        }
        return pointRecordDao.insert(pointsRecord);
    }

    public PointRecordVo getInfo(Long id) {
        return pointRecordDao.getInfo(id);
    }

}
