package com.jmt;

import com.jmt.timer.TimerScheduler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.annotation.Resource;

@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients
@EnableTransactionManagement
@EnableScheduling
public class JmtProfitApplication {

    public static void main(String[] args) {
        SpringApplication.run(JmtProfitApplication.class, args);
    }

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private TimerScheduler timerScheduler;



}
