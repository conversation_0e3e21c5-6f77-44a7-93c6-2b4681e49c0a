package com.jmt.dao;

import com.jmt.model.profit.entity.PointCashApply;
import com.jmt.model.profit.dto.PointCashApplyPageQueryDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface PointCashApplyDao {
    /**
     * 根据用户id和申请时间获取申请数量
     * @param applyUser
     * @param startDay
     * @param endDay
     * @return
     */
    int getCountByUaaIdAndCreateTime(@Param("applyUser") Long applyUser, @Param("startDay")Date startDay, @Param("endDay") Date endDay);
    /**
     * 添加申请
     * @param pointCashApply
     */
    Integer add(PointCashApply pointCashApply);
    /**
     * 获取申请列表
     * @param queryData
     * @return
     */
    List<PointCashApply> getPage(PointCashApplyPageQueryDto queryData);
    /**
     * 获取申请详情
     * @param id
     */
    PointCashApply getInfo(Long id);

    Integer audit(@Param("id") Long id, @Param("auditStatus") Integer auditStatus, @Param("reason") String reason, @Param("auditUser") Long auditUser, @Param("auditUserName") String auditUserName);

    List<PointCashApply> getPendingPointCashApplyByApplyUser(Long applyUserId);

    Integer complete(@Param("id") Long id,  @Param("auditStatus") Integer auditStatus);
}
