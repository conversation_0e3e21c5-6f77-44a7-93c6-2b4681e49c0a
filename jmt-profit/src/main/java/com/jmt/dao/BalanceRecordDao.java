package com.jmt.dao;

import com.jmt.model.profit.dto.BalanceRecordPageQueryDto;
import com.jmt.model.profit.entity.BalanceRecord;
import com.jmt.model.profit.vo.BalanceRecordVo;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BalanceRecordDao {
    /**
     * 添加余额记录
     */
    Integer add(BalanceRecord balanceRecord);

    List<BalanceRecord> getInfoByUaaId(Long uaaId);

    List<BalanceRecordVo> getPage(BalanceRecordPageQueryDto dto);

    BalanceRecordVo getInfo(Long id);
}
