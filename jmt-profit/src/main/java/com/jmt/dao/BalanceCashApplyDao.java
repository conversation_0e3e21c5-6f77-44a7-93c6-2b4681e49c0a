package com.jmt.dao;

import com.jmt.model.profit.vo.BalanceCashApplyVo;
import com.jmt.model.profit.dto.*;
import com.jmt.model.profit.entity.BalanceCashApply;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface BalanceCashApplyDao {
    /**
     * 获取申请次数
     *
     * @return 提现申请列表
     */
    Integer getCountByUaaIdAndCreateTime(@Param("applyUser") Long applyUser, @Param("startDay")Date startDay, @Param("endDay") Date endDay);
    /**
     * 增加提现申请
     *
     * @return 提现申请列表
     */
    Integer add(BalanceCashApply balanceCashApply);
    /**
     * 获取提现申请列表
     *
     * @return 提现申请列表
     */
    List<BalanceCashApplyVo> getPage(BalanceCashApplyPageQueryDto queryData);
    /**
     * 根据id查询
     *
     * @return 提现申请列表
     */
    BalanceCashApplyVo getInfo(Long id);

    Integer getBalanceCashApplyCount(Long uaaId);

    Integer audit(@Param("id") Long id, @Param("auditStatus") Integer auditStatus, @Param("reason") String reason, @Param("auditUser") Long auditUser, @Param("auditUserName") String auditUserName);

    List<BalanceCashApplyVo> getPendingBalanceCashApplyByApplyUser(Long applyUserId);

    Integer complete(@Param("id") Long id, @Param("auditStatus") Integer auditStatus);
}
