package com.jmt.dao;

import com.jmt.model.profit.dto.ProfitSharePageQueryDto;
import com.jmt.model.profit.entity.ProfitShareDivide;
import com.jmt.model.profit.vo.ProfitShareDivideVo;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProfitShareDivideDao {
    /**
     * 添加积分记录
     * @param profitShareDivide
     */
    Integer insert(ProfitShareDivide profitShareDivide);

    List<ProfitShareDivideVo> getPage(ProfitSharePageQueryDto queryData);

    Integer getTotalPoints(Long uaaId);
}
