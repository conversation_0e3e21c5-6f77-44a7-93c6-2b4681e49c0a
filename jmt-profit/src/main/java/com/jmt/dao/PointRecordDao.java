package com.jmt.dao;

import com.jmt.model.profit.dto.PointRecordPageQueryDto;
import com.jmt.model.profit.entity.PointRecord;
import com.jmt.model.profit.vo.PointRecordVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface PointRecordDao {
    /**
     * 添加积分记录
     * @param pointRecord
     */
    Integer insert(PointRecord pointRecord);

    List<PointRecordVo> getPage(PointRecordPageQueryDto queryData);

    PointRecordVo getInfo(Long id);
}
