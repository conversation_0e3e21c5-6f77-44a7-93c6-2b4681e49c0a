package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.profit.dto.*;
import com.jmt.model.profit.vo.ProfitShareDivideVo;
import com.jmt.service.ProfitShareDivideService;
import com.jmt.util.LoginUserUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

@RestController
@RequestMapping("/profit/share/v1")
public class ProfitShareDivideController extends BaseController {

    @Resource
    private ProfitShareDivideService profitShareDivideService;

    /**
     * 用户预分积分查询接口
     * @return String
     */
    @ResponseBody
    @GetMapping("/pre/total")
    public String getPreTotal() {
        Long uaaId = Objects.requireNonNull(LoginUserUtil.get()).getUaaId();
        Integer totalPoints = profitShareDivideService.getTotalPoints(uaaId);
        return super.responseSuccess(totalPoints,"查询成功");
    }
    /**
     * 积分兑现申请分页查询接口
     * @param pageQuery 查询参数
     * @return String
     */
    @ResponseBody
    @PostMapping("/pre/page")
    public String getPrePage(@RequestBody PageQuery<ProfitSharePageQueryDto> pageQuery) {
        logger.info("分页查询参数:{}",pageQuery);
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
        }
        LoginUaaUser loginUaaUser = LoginUserUtil.get();
        PageResult<ProfitShareDivideVo> page = profitShareDivideService.getPage(pageQuery, loginUaaUser);
        return super.responseSuccess(page,"查询成功");
    }
    /**
     * 新增分润接口
     * @param profitShareDivideDto 新增参数
     * @return String
     */
    @ResponseBody
    @PostMapping("/divide")
    public String divide(@RequestBody ProfitShareDivideDto profitShareDivideDto) {
        return super.responseSuccess("新增成功");
    }
}
