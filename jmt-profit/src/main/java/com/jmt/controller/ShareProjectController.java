package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.profit.vo.ShareProjectVo;
import com.jmt.service.ShareProjectService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/profit/project/v1")
public class ShareProjectController extends BaseController {

    @Resource
    private ShareProjectService shareProjectService;
    /**
     * 分润项目下拉选择框数据接口
     * @param
     * @return
     */
    @ResponseBody
    @GetMapping(value = "/getShareProjectSelect")
    public String getShareProjectSelect() {
        List<ShareProjectVo> list = shareProjectService.getShareProjectSelect();
        return super.responseSuccess(list,"查询成功");
    }

}
