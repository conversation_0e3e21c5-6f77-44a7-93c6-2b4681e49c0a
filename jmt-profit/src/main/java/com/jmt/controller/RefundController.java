package com.jmt.controller;

import com.google.gson.JsonObject;
import com.jmt.base.BaseController;
import com.jmt.exception.BusinessException;
import com.jmt.lkl.LakalaRefundService;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.profit.dto.LklRefundDTO;
import com.jmt.model.profit.dto.RefundRecordDTO;
import com.jmt.model.profit.dto.RefundDTO;
import com.jmt.service.RefundService;
import com.jmt.util.LakalaResponseUtil;
import com.lkl.laop.sdk.request.V3LabsRelationRefundRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/profit/refund")
public class RefundController extends BaseController {

    @Resource
    private RefundService refundService;

    @Resource
    private LakalaRefundService lakalaRefundService;

    @GetMapping("/v1/getPage")
    public String getPage(@RequestBody PageQuery<RefundRecordDTO> pageQuery){
        PageResult<RefundRecordDTO> pageResult = refundService.getPage(pageQuery);
        return super.responseSuccess(pageResult,"查询成功");
    }

    @PostMapping("/v1/submit")
    public String balancePay(@RequestBody RefundDTO dto) {
        try {
            refundService.submit(dto);
            return super.responseSuccess("退款成功");
        } catch (RuntimeException e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 拉卡拉 退款
     * @param dto
     * @return
     */
    @PostMapping("/v1/lklRefundOrder")
    public String queryOrderByOutOrderNo(@RequestBody LklRefundDTO dto) {
        try {
            V3LabsRelationRefundRequest request = lakalaRefundService.createRefundRequest(dto);
            String response = LakalaRefundService.executeRefund(request);

            JsonObject responseObj = LakalaResponseUtil.parse(response);
            LakalaResponseUtil.checkSuccess(responseObj);
            return responseSuccess(responseObj,"退款成功");
        } catch (BusinessException e) {
            return responseFail(e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
