package com.jmt.controller;

import cn.hutool.core.util.ObjectUtil;
import com.jmt.base.BaseController;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.common.ObjIdDto;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.profit.vo.BalanceCashApplyVo;
import com.jmt.model.profit.dto.*;
import com.jmt.model.profit.vo.BalanceRecordVo;
import com.jmt.service.BalanceCashService;
import com.jmt.service.BalanceRecordService;
import com.jmt.service.PaymentService;
import com.jmt.util.LoginUserUtil;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;

@RestController
@RequestMapping("/profit/balance/v1")
public class BalanceController extends BaseController {

   @Resource
   private BalanceCashService balanceCashService;

   @Resource
   private BalanceRecordService balanceRecordService;

   @Resource
   private PaymentService paymentService;

   /**
    * 余额提现申请接口
    */
   @ResponseBody
   @PostMapping("/cash/apply")
   public String apply(@RequestBody BalanceCashApplyDto balanceCashApplyDto){
      LoginUaaUser applyUser = LoginUserUtil.get();
      assert applyUser != null;
      paymentService.validatePayPassword(applyUser.getUaaId(),balanceCashApplyDto.getPayPassword());
      Integer r = balanceCashService.apply(balanceCashApplyDto, applyUser);
      if (r == 1) {
         return super.responseSuccess("申请成功");
      }
      return super.responseFail("申请失败");
   }
   /**
    * 余额提现申请分页查询接口
    * @param pageQuery 分页参数
    * @return String
    */
   @ResponseBody
   @PostMapping("/cash/page")
   public String getPage(@RequestBody PageQuery<BalanceCashApplyPageQueryDto> pageQuery) {
      logger.info("分页查询参数:{}",pageQuery);
      if (pageQuery == null) {
         pageQuery = new PageQuery<>();
      }
      PageResult<BalanceCashApplyVo> page = balanceCashService.getPage(pageQuery);
      return super.responseSuccess(page,"查询成功");
   }
   /**
    * 余额提现申请查看
    * @param id 主键ID
    * @return String
    */
   @ResponseBody
   @GetMapping(value = "/cash/info")
   public String getInfo(@RequestParam Long id) {
      BalanceCashApplyVo info = balanceCashService.getInfo(id);
      if (ObjectUtil.isNotEmpty(info)) {
         return super.responseSuccess(info,"查询成功");
      } else {
         return super.responseFail("查询失败:余额提现申请不存在");
      }
   }
   /**
    * 余额提现申请审核（通过、不通过）接口
    * @param balanceApplyAuditDto 审核参数
    * @return String
    */
   @ResponseBody
   @PostMapping("/cash/audit")
   public String audit(@RequestBody BalanceCashApplyAuditDto balanceApplyAuditDto) {
      LoginUaaUser auditUser = LoginUserUtil.get();
      Integer r = balanceCashService.audit(balanceApplyAuditDto,auditUser);
      if (r == 1) {
         return super.responseSuccess("审核成功");
      }
      return super.responseFail("审核失败");
   }
   /**
    * 余额提现汇款接口
    * @param dto 申请单id
    * @return String
    */
   @ResponseBody
   @PostMapping("/cash/toTransfer")
   public String toTransfer(@RequestBody ObjIdDto dto) {
      Integer r = balanceCashService.toTransfer(dto.getId());
      if (r == 1) {
         return super.responseSuccess("汇款成功");
      }
      return super.responseFail("汇款失败");
   }

   @ResponseBody
   @PostMapping("/record/page")
   public String getRecordPage(@RequestBody PageQuery<BalanceRecordPageQueryDto> pageQuery) {
      logger.info("分页查询参数:{}",pageQuery);
      if (pageQuery == null) {
         pageQuery = new PageQuery<>();
      }
      LoginUaaUser loginUaaUser = LoginUserUtil.get();
      PageResult<BalanceRecordVo> page = balanceRecordService.getPage(pageQuery,loginUaaUser);
      return super.responseSuccess(page,"查询成功");
   }
}
