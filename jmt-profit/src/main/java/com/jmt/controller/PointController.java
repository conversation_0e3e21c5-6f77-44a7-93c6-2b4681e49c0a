package com.jmt.controller;

import cn.hutool.core.util.ObjectUtil;
import com.jmt.base.BaseController;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.common.ObjIdDto;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.profit.dto.*;
import com.jmt.model.profit.entity.PointCashApply;
import com.jmt.model.profit.vo.PointRecordVo;
import com.jmt.service.PaymentService;
import com.jmt.service.PointCashService;
import com.jmt.service.PointRecordService;
import com.jmt.util.LoginUserUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;

@RestController
@RequestMapping("/profit/point/v1")
public class PointController extends BaseController {

    @Resource
    private PointCashService pointCashService;

    @Resource
    private PointRecordService pointRecordService;

    @Resource
    private PaymentService paymentService;
    /**
     * 积分兑现申请接口
     * @param  pointCashApplyDto 申请参数
     * @return  String
     */
    @ResponseBody
    @PostMapping("/cash/apply")
    public String apply(@RequestBody PointCashApplyDto pointCashApplyDto){
        LoginUaaUser applyUser = LoginUserUtil.get();
        assert applyUser != null;
        paymentService.validatePayPassword(applyUser.getUaaId(),pointCashApplyDto.getPayPassword());
        Integer r = pointCashService.apply(pointCashApplyDto, applyUser);
        if (r == 1) {
            return super.responseSuccess("申请成功");
        }
        return super.responseFail("申请失败");
    }
    /**
     * 积分兑现申请分页查询接口
     * @param pageQuery 查询参数
     * @return String
     */
    @ResponseBody
    @PostMapping("/cash/page")
    public String getPage(@RequestBody PageQuery<PointCashApplyPageQueryDto> pageQuery) {
        logger.info("分页查询参数:{}",pageQuery);
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
        }
        PageResult<PointCashApply> page = pointCashService.getPage(pageQuery);
        return super.responseSuccess(page,"查询成功");
    }

    /**
     * 积分兑现申请查看
     * @param id 主键ID
     * @return String
     */
    @ResponseBody
    @GetMapping(value = "/cash/info")
    public String getInfo(@RequestParam Long id) {
        PointCashApply info = pointCashService.getInfo(id);
        if (ObjectUtil.isNotEmpty(info)) {
            return super.responseSuccess(info,"查询成功");
        } else {
            return super.responseFail("查询失败:积分兑现申请不存在");
        }
    }
    /**
     * 积分兑现申请审核（通过、不通过）接口
     * @param pointApplyAuditDto 审核参数
     * @return String
     */
    @ResponseBody
    @PostMapping("/cash/audit")
    public String audit(@RequestBody PointCashApplyAuditDto pointApplyAuditDto) {
        LoginUaaUser auditUser = LoginUserUtil.get();
        Integer r = pointCashService.audit(pointApplyAuditDto,auditUser);
        if (r == 1) {
            return super.responseSuccess("审核成功");
        }
        return super.responseFail("审核失败");
    }
    /**
     * 积分转余额接口
     * @param dto 申请单id
     * @return String
     */
    @ResponseBody
    @PostMapping("/cash/toBalance")
    public String toBalance(@RequestBody ObjIdDto dto) {
        Integer r = pointCashService.toBalance(dto.getId());
        if (r == 1) {
            return super.responseSuccess("转余额成功");
        }
        return super.responseFail("转余额失败");
    }
    /**
     * 积分转余额接口
     * @return String
     */
    @ResponseBody
    @PostMapping("/cash/finalBalance")
    public String finalBalance(@RequestBody PointToBalanceDto pointToBalanceDto) {
        BigDecimal r = pointCashService.calculate(pointToBalanceDto.getPoints());

        return super.responseSuccess(r.setScale(2, RoundingMode.HALF_UP),"计算成功");
    }
    /**
     * 积分兑现记录分页查询接口
     * @param pageQuery 查询参数
     * @return String
     */
    @ResponseBody
    @PostMapping("/record/page")
    public String getRecordPage(@RequestBody PageQuery<PointRecordPageQueryDto> pageQuery) {
        logger.info("分页查询参数:{}",pageQuery);
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
        }
        LoginUaaUser loginUaaUser = LoginUserUtil.get();
        assert loginUaaUser != null;
        PageResult<PointRecordVo> page = pointRecordService.getPage(pageQuery,loginUaaUser);
        return super.responseSuccess(page,"查询成功");
    }
    /**
     * 积分兑现记录查看
     * @param id 主键ID
     * @return String
     */
    @ResponseBody
    @GetMapping(value = "/record/info")
    public String getRecordInfo(@RequestParam Long id) {
        PointRecordVo info = pointRecordService.getInfo(id);
        if (ObjectUtil.isNotEmpty(info)) {
            return super.responseSuccess(info,"查询成功");
        } else {
            return super.responseFail("查询失败:积分记录不存在");
        }
    }
}
