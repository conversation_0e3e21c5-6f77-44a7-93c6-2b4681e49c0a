package com.jmt.timer;

import cn.hutool.core.date.DateUtil;
import org.quartz.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;

@Component
public class TimerScheduler {

	@Resource
	private Scheduler scheduler;

	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	/**
	 * 自动解除账号锁定
	 */
	public static final String AUTO_UNLOCK_USER = "auto_unlock_user";

	/**
	 * 启动JOB 当前时间开始 每n秒执行一次
	 * @param seconds 任务执行的间隔（秒）
	 * @param id 任务标识
	 * @param jobClass 任务所执行的Job
	 * @param group 任务组
	 * @return 启动数量
	 */
	public int startJobIntervalInSeconds(int seconds, String id, Class<? extends Job> jobClass, String group) {
		if (id == null || jobClass == null || StringUtils.isEmpty(group)) {
			throw new IllegalArgumentException();
		}
		int size = 0;
		Date startTime = DateUtil.offsetSecond(new Date(),3);
		logger.info("添加Job，id：" + id + "；开始时间：" + startTime + "；间隔：" + seconds + "；组名：" + group);
		try {
			JobDetail jobDetail = JobBuilder.newJob(jobClass).withIdentity(id, group).build();
			SimpleScheduleBuilder scheduleBuilder = SimpleScheduleBuilder.simpleSchedule()
					.withIntervalInSeconds(seconds).repeatForever();
			Trigger trigger = TriggerBuilder.newTrigger().withIdentity(id, group).startAt(startTime)
					.withSchedule(scheduleBuilder).build();
			scheduler.scheduleJob(jobDetail, trigger);
			scheduler.start();
			size++;
		} catch (Exception e) {
			logger.error("捕获异常:",e);
		}
		logger.info("添加Job数量：" + size);
		return size;
	}
	/**
	 * 启动JOB 当前时间开始 每n分钟执行一次
	 * @param mins 任务执行的间隔（分钟）
	 * @param id 任务标识
	 * @param jobClass 任务所执行的Job
	 * @param group 任务组
	 * @return 启动数量
	 */
	public int startJobIntervalInMinutes(int mins,String id, Class<? extends Job> jobClass, String group) {
		int size = 0;
		Date startTime = DateUtil.offsetSecond(new Date(),3);
		try {
			JobDetail jobDetail = JobBuilder.newJob(jobClass).withIdentity(id, group).build();
			SimpleScheduleBuilder scheduleBuilder = SimpleScheduleBuilder.simpleSchedule()
					.withIntervalInMinutes(mins).repeatForever();
			Trigger trigger = TriggerBuilder.newTrigger().withIdentity(id, group)
					.withSchedule(scheduleBuilder).startAt(startTime).build();
			scheduler.scheduleJob(jobDetail, trigger);
			scheduler.start();
			size++;
		} catch (Exception e) {
			logger.error("捕获异常:",e);
		}
		logger.info("添加Job数量：" + size);
		return size;
	}
	/**
	 * 启动JOB 当前时间开始 每n小时执行一次
	 * @param hours 任务执行的间隔（小时）
	 * @param id 任务标识
	 * @param jobClass 任务所执行的Job
	 * @param group 任务组
	 * @return 启动数量
	 */
	public int startJobIntervalInHours(int hours,String id, Class<? extends Job> jobClass, String group) {
		int size = 0;
		Date startTime = DateUtil.offsetSecond(new Date(),3);
		try {
			JobDetail jobDetail = JobBuilder.newJob(jobClass).withIdentity(id, group).build();
			SimpleScheduleBuilder scheduleBuilder = SimpleScheduleBuilder.simpleSchedule()
					.withIntervalInHours(hours).repeatForever();
			Trigger trigger = TriggerBuilder.newTrigger().withIdentity(id, group)
					.withSchedule(scheduleBuilder).startAt(startTime).build();
			scheduler.scheduleJob(jobDetail, trigger);
			scheduler.start();
			size++;
		} catch (Exception e) {
			logger.error("捕获异常:",e);
		}
		logger.info("添加Job数量：" + size);
		return size;
	}
	/**
	 * 启动JOB 一段时间范围内 每N天执行一次
	 * @param begin 任务开始时间
	 * @param over 任务结束时间
	 * @param days 任务执行的间隔（天）
	 * @param id 任务标识
	 * @param jobClass 任务所执行的Job
	 * @param group 任务组
	 * @return 启动数量
	 */
	public int startJobIntervalInDaysInScope(Date begin, Date over, int days, String id, Class<? extends Job> jobClass, String group) {
		if (begin == null || id == null || jobClass == null || StringUtils.isEmpty(group)) {
			throw new IllegalArgumentException();
		}
		int size = 0;
		logger.info("添加Job，id：" + id + "；开始时间：" + begin + "；结束时间：" + over + "；间隔天数：" + days + "；组名：" + group);
		try {
			JobDetail jobDetail = JobBuilder.newJob(jobClass).withIdentity(id, group).build();
			SimpleScheduleBuilder scheduleBuilder = SimpleScheduleBuilder.simpleSchedule()
					.withIntervalInHours(days * 24).repeatForever();
			Trigger trigger = TriggerBuilder.newTrigger().withIdentity(id, group).startAt(begin).endAt(over)
					.withSchedule(scheduleBuilder).build();
			scheduler.scheduleJob(jobDetail, trigger);
			scheduler.start();
			size++;
		} catch (Exception e) {
			logger.error("捕获异常:",e);
		}
		logger.info("添加Job数量：" + size);
		return size;
	}

	/**
	 * 启动JOB 某个具体时间执行一次
	 * @param begin 任务开始时间
	 * @param id 任务标识（taskId）
	 * @param jobClass 任务所执行的Job
	 * @param group 任务组
	 * @return 启动数量
	 */
	public int startJobOnlyOnetime(Date begin, String id, Class<? extends Job> jobClass, String group) {
		if (begin == null || id == null || jobClass == null || StringUtils.isEmpty(group)) {
			throw new IllegalArgumentException();
		}
		int size = 0;
		logger.info("添加Job，id：" + id + "；开始时间：" + begin + "；组名：" + group);
		try {
			JobDetail jobDetail = JobBuilder.newJob(jobClass).withIdentity(id, group).build();
			Trigger trigger = TriggerBuilder.newTrigger().withIdentity(id, group).startAt(begin).build();
			scheduler.scheduleJob(jobDetail, trigger);
			scheduler.start();
			size++;
		} catch (Exception e) {
			logger.error("捕获异常:",e);
		}
		logger.info("添加Job数量：" + size);
		return size;
	}

	/**
	 * 启动JOB
	 * @param cron 表达式
	 * @param id 任务标识（taskId）
	 * @param jobClass 任务所执行的Job
	 * @param group 任务组
	 * @return 启动数量
	 */
	public int startJobByCron(String cron, String id, Class<? extends Job> jobClass, String group) {
		if (StringUtils.isEmpty(cron) || id == null || jobClass == null || StringUtils.isEmpty(group)) {
			throw new IllegalArgumentException();
		}
		int size = 0;
		logger.info("添加Job，id：" + id + "；表达式：" + cron + "；组名：" + group);
		try {
			JobDetail jobDetail = JobBuilder.newJob(jobClass).withIdentity(id, group).build();
			CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(cron);
			Trigger trigger = TriggerBuilder.newTrigger().withIdentity(id, group).withSchedule(scheduleBuilder).build();
			scheduler.scheduleJob(jobDetail, trigger);
			scheduler.start();
			size++;
		} catch (Exception e) {
			logger.error("捕获异常:",e);
		}
		logger.info("添加Job数量：" + size);
		return size;
	}

	/**
	 * 是否含有JOB
	 * @param id 任务标识
	 * @param group 任务组
	 * @return 是否含有JOB
	 */
	public boolean hasJob(String id, String group) {
		try {
			JobKey jobKey = new JobKey(id, group);
			return scheduler.checkExists(jobKey);
		} catch (Exception e) {
			logger.error("捕获异常:",e);
		}
		return false;
	}

	/**
	 * 删除JOB
	 * @param id 任务标识
	 * @param group 任务组
	 * @return 删除数量
	 */
	public int deleteJob(String id, String group) {
		if (id == null || StringUtils.isEmpty(group)) {
			throw new IllegalArgumentException();
		}
		int size = 0;
		logger.info("删除Job，id：" + id + "；组名：" + group);
		try {
			JobKey jobKey = new JobKey(id, group);
			JobDetail jobDetail = scheduler.getJobDetail(jobKey);
			if (jobDetail == null) {
				return size;
			}
			scheduler.deleteJob(jobKey);
			size++;
		} catch (Exception e) {
			logger.error("捕获异常:",e);
		}
		logger.info("删除Job数量：" + size);
		return size;
	}

}
