package com.jmt.timer.job;

import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class ProcessPredividesJob implements Job {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    public void execute(JobExecutionContext context) {
        logger.info("开始执行分润处理任务");
        try {
            logger.info("分润处理任务完成");
        } catch (Exception e) {
            logger.error("分润处理任务执行失败", e);
        }
    }
}
