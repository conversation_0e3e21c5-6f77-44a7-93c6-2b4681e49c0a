package com.jmt.timer.job;

import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class CalculateBatchJob implements Job {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    public void execute(JobExecutionContext context) {
        logger.info("开始执行预分润批量计算任务");
        try {

        } catch (Exception e) {
            logger.error("预分润批量计算任务执行失败", e);
        }
    }
}
