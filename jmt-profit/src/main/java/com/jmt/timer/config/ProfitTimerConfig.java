package com.jmt.timer.config;

import com.jmt.timer.TimerScheduler;
import com.jmt.timer.job.CalculateBatchJob;
import com.jmt.timer.job.ProcessPredividesJob;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

@Configuration
public class ProfitTimerConfig {

    @Resource
    private TimerScheduler timerScheduler;

    /**
     * 初始化分润相关定时任务
     */
    @Bean
    public ApplicationRunner profitTimerInitializer() {
        return args -> {
            // 1分钟调用一次预分润接口
            String calculateBatchJobId = "calculate_batch_job";
            String calculateBatchJobGroup = "profit_group";
            // 检查任务是否已存在，不存在则创建
            if (!timerScheduler.hasJob(calculateBatchJobId, calculateBatchJobGroup)) {
                timerScheduler.startJobIntervalInMinutes(60, calculateBatchJobId, CalculateBatchJob.class, calculateBatchJobGroup);
            }

            // 2分钟调用一次分润接口
            String processPredividesJobId = "process_predivides_job";
            String processPredividesJobGroup = "profit_group";
            if (!timerScheduler.hasJob(processPredividesJobId, processPredividesJobGroup)) {
                timerScheduler.startJobIntervalInMinutes(60, processPredividesJobId, ProcessPredividesJob.class, processPredividesJobGroup);
            }
        };
    }
}
