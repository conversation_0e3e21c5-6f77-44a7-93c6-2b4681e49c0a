//package com.jmt.lkl;
//
//import com.jmt.config.LakalaPayConfig;
//import com.lkl.laop.sdk.Config;
//import com.lkl.laop.sdk.LKLSDK;
//import com.lkl.laop.sdk.exception.SDKException;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.PostConstruct;
//
///***
// * @Description 功能描述：使用Java配置方式初始化SDK
// * @Date 2023/7/19 15:41
// */
//@Component
//public class LakalaBaseCommon {
//    private final LakalaPayConfig payConfig;
//    private volatile boolean init = false;
//
//    @Autowired
//    public LakalaBaseCommon(LakalaPayConfig payConfig) {
//        this.payConfig = payConfig;
//    }
//
//    @PostConstruct
//    public synchronized void init() throws SDKException {
//        if (!init) {
//            Config config = new Config();
//            config.setAppId(payConfig.getAppId());
//            config.setSerialNo(payConfig.getSerialNo());
//            config.setPriKeyPath(payConfig.getPriKeyPath());
//            config.setLklCerPath(payConfig.getLklCerPath());
//            config.setLklNotifyCerPath(payConfig.getLklNotifyCerPath());
//            config.setServerUrl(payConfig.getServerUrl());
//            config.setSm4Key(payConfig.getSm4Key());
//            LKLSDK.init(config);
//            init = true;
//        }
//    }
//}
//
