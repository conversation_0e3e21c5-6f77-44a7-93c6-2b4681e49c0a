package com.jmt.lkl;

import com.lkl.laop.sdk.LKLSDK;
import com.lkl.laop.sdk.request.V3LabsQueryTradequeryRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class LakalaTradeQueryService  {

    /**
     * 交易查询（通过商户订单号查询）
     * @param merchantNo 商户号
     * @param termNo 终端号
     * @param outTradeNo 商户订单号（与tradeNo二选一）
     * @return 交易结果JSON字符串
     */
    public static String queryByOutTradeNo(String merchantNo, String termNo, String outTradeNo) throws Exception {
        V3LabsQueryTradequeryRequest request = buildTradeQueryRequest(merchantNo, termNo, outTradeNo, null);
        return executeQuery(request);
    }

    /**
     * 交易查询（通过拉卡拉交易号查询）
     * @param merchantNo 商户号
     * @param termNo 终端号
     * @param tradeNo 拉卡拉交易号（与outTradeNo二选一）
     * @return 交易结果JSON字符串
     */
    public static String queryByTradeNo(String merchantNo, String termNo, String tradeNo) throws Exception {
        V3LabsQueryTradequeryRequest request = buildTradeQueryRequest(merchantNo, termNo, null, tradeNo);
        return executeQuery(request);
    }

    // 构建查询请求对象
    private static V3LabsQueryTradequeryRequest buildTradeQueryRequest(
            String merchantNo, String termNo, String outTradeNo, String tradeNo) {
        V3LabsQueryTradequeryRequest request = new V3LabsQueryTradequeryRequest();
        request.setMerchantNo(merchantNo);
        request.setTermNo(termNo);

        if (tradeNo != null) {
            request.setTradeNo(tradeNo); // 优先使用拉卡拉交易流水号
        } else if (outTradeNo != null) {
            request.setOutTradeNo(outTradeNo); // 使用商户订单号
        } else {
            throw new IllegalArgumentException("必须提供outTradeNo或tradeNo");
        }

        return request;
    }

    // 执行查询请求
    private static String executeQuery(V3LabsQueryTradequeryRequest request) throws Exception {
        return LKLSDK.httpPost(request);
    }
}
