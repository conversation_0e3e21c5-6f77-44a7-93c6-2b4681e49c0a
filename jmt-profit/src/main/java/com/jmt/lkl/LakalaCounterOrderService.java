package com.jmt.lkl;

import com.lkl.laop.sdk.LKLSDK;
import com.lkl.laop.sdk.request.V3CcssCounterOrderCloseRequest;
import com.lkl.laop.sdk.request.V3CcssCounterOrderQueryRequest;
import com.lkl.laop.sdk.request.V3CcssCounterOrderSpecialCreateRequest;
import com.lkl.laop.sdk.request.model.V3CcssOrderGoodsFieldInfo;
import com.lkl.laop.sdk.request.model.V3CcssOrderSceneFieldInfo;
import com.lkl.laop.sdk.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class LakalaCounterOrderService  {


    /**
     * 创建收银台订单基础请求
     * @param outOrderNo 商户订单号
     * @param merchantNo 银联商户号
     * @param totalAmount 订单总金额(分)
     * @param orderEfficientTime 订单有效时间(yyyyMMddHHmmss)
     * @param notifyUrl 异步通知地址
     * @param payMode 支付模式(ALIPAY/WECHAT等)
     * @return 基础订单请求对象
     */
    public static V3CcssCounterOrderSpecialCreateRequest createBaseOrderRequest(
            String outOrderNo, String merchantNo, Long totalAmount,
            String orderEfficientTime, String notifyUrl,
            String payMode) {

        // 参数校验
        if (StringUtils.isEmpty(outOrderNo) || StringUtils.isEmpty(merchantNo) || totalAmount == null
                || StringUtils.isEmpty(orderEfficientTime) || StringUtils.isEmpty(notifyUrl)) {
            throw new IllegalArgumentException("必要参数不能为空");
        }

        V3CcssCounterOrderSpecialCreateRequest request = new V3CcssCounterOrderSpecialCreateRequest();
        request.setOutOrderNo(outOrderNo);
        request.setMerchantNo(merchantNo);
        request.setTotalAmount(totalAmount);
        request.setOrderEfficientTime(orderEfficientTime);
        request.setNotifyUrl(notifyUrl);
        request.setCounterParam("{\"pay_mode\":\"" + payMode + "\"}");

        // 设置默认值
        request.setSupportRefund(1); // 支持退款
        request.setSupportRepeatPay(1); // 支持重复支付
        request.setGoodsMark("1"); // 商品标识
        request.setProductId("200809"); // 指定产品编号
        request.setAgeLimit("0"); // 无年龄限制
        request.setRepeatPayAutoRefund("0");
        request.setRepeatPayNotify("0");
        request.setCloseOrderAutoRefund("0");
        request.setSupportCancel(0); // 不支持撤销
        request.setSplitMark("0"); // 	合单标识，“1”为合单，不填默认是为非合单

        return request;
    }

    /**
     * 设置商品信息
     * @param request 订单请求对象
     * @param goodsName 商品名称
     * @param goodsAmt 商品金额(分)
     * @param goodsNum 商品数量
     * @param platformType 平台类型(1:自营 2:第三方)
     * @param platformName 平台名称
     */
    public static void setGoodsInfo(
            V3CcssCounterOrderSpecialCreateRequest request,
            String goodsName, Long goodsAmt, String goodsPricingUnit, Integer goodsNum,
            String platformType, String platformName, String goodsType) {

        V3CcssOrderGoodsFieldInfo goodsInfo = new V3CcssOrderGoodsFieldInfo();
        goodsInfo.setGoodsAmt(goodsAmt); // 商品单价
        goodsInfo.setGoodsNum(goodsNum); // 商品数量
        goodsInfo.setGoodsPricingUnit(goodsPricingUnit); // 计价单位: 1-箱 2-件 3-瓶 4-个
        goodsInfo.setGoodsName(goodsName); // 商品名称
        goodsInfo.setTePlatformType(platformType); // 交易电商平台类型 1-境内平台 2-境外平台
        goodsInfo.setTePlatformName(platformName); // 交易电商平台名称
        goodsInfo.setGoodsType(goodsType); // 交易商品类型

        request.setGoodsField(JsonUtils.toJSONString(goodsInfo));
    }

    /**
     * 设置转账信息(适用于转账业务)
     * @param request 订单请求对象
     * @param accOutName 转出账户名
     * @param accOutNo 转出账号
     * @param additional 转账附言
     */
    public static void setTransferInfo(
            V3CcssCounterOrderSpecialCreateRequest request,
            String accOutName, String accOutNo, String additional) {

        V3CcssCounterOrderSpecialCreateRequest.TransferField transferField =
                new V3CcssCounterOrderSpecialCreateRequest.TransferField();
        transferField.setAccOutName(accOutName);
        transferField.setAccOutNo(accOutNo);
        transferField.setAdditional(additional);

        request.setTransferField(transferField);
    }

    /**
     * 设置花呗分期场景
     * @param hbFqNum 分期数(3/6/12等)
     * @param sellerPercent 卖家承担手续费比例,间连模式下只支持传0
     * @return 场景信息对象
     */
    public static V3CcssOrderSceneFieldInfo createHbFqScene(String hbFqNum, String sellerPercent) {
        V3CcssOrderSceneFieldInfo hbFq = new V3CcssOrderSceneFieldInfo();
        V3CcssOrderSceneFieldInfo.HbFqSceneInfo hbFqSceneInfo =
                new V3CcssOrderSceneFieldInfo.HbFqSceneInfo();
        hbFqSceneInfo.setHbFqNum(hbFqNum);
        hbFqSceneInfo.setHbFqSellerPercent(sellerPercent);
        hbFq.setSceneInfo(JsonUtils.toJSONString(hbFqSceneInfo));
        hbFq.setOrderSceneType("HB_FQ");
        return hbFq;
    }

    /**
     * 执行订单创建请求
     * @param request 订单请求对象
     * @return 创建结果(JSON字符串)
     * @throws Exception
     */
    public static String createOrder(V3CcssCounterOrderSpecialCreateRequest request) throws Exception {
        return LKLSDK.httpPost(request);
    }

    /**
     * 创建关单请求
     * @param merchantNo 商户号
     * @param outOrderNo 商户订单号
     * @param channelId 渠道ID(可选)
     * @return 关单请求对象
     * @throws IllegalArgumentException 参数校验失败时抛出
     */
    public static V3CcssCounterOrderCloseRequest createCloseRequest(
            String merchantNo, String outOrderNo, String channelId) {

        if (StringUtils.isEmpty(merchantNo) || StringUtils.isEmpty(outOrderNo)) {
            throw new IllegalArgumentException("商户号和订单号不能为空");
        }

        V3CcssCounterOrderCloseRequest request = new V3CcssCounterOrderCloseRequest();
        request.setMerchantNo(merchantNo);
        request.setOutOrderNo(outOrderNo);

        if (StringUtils.isNotEmpty(channelId)) {
            request.setChannelId(channelId);
        }

        return request;
    }

    /**
     * 执行关单操作
     * @param closeRequest 关单请求对象
     * @return 关单响应结果(JSON字符串)
     * @throws Exception 网络异常或系统错误时抛出
     */
    public static String closeOrder(V3CcssCounterOrderCloseRequest closeRequest) throws Exception {
        return LKLSDK.httpPost(closeRequest);
    }

    /**
     * 通过商户订单号查询
     * @param merchantNo 商户号(必填)
     * @param outOrderNo 商户订单号(必填)
     * @return 查询响应结果(JSON字符串)
     * @throws IllegalArgumentException 参数错误时抛出
     * @throws Exception 系统错误时抛出
     */
    public static String queryByOutOrderNo(String merchantNo, String outOrderNo, String channelId) throws Exception {
        // 参数校验
        if (StringUtils.isEmpty(merchantNo)) {
            throw new IllegalArgumentException("商户号不能为空");
        }
        if (StringUtils.isEmpty(outOrderNo)) {
            throw new IllegalArgumentException("商户订单号不能为空");
        }

        V3CcssCounterOrderQueryRequest request = new V3CcssCounterOrderQueryRequest();
        request.setMerchantNo(merchantNo);
        request.setOutOrderNo(outOrderNo);
        request.setChannelId(channelId);

        return LKLSDK.httpPost(request);
    }

    /**
     * 通过支付订单号查询
     * @param merchantNo 商户号(必填)
     * @param payOrderNo 支付订单号(必填)
     * @return 查询响应结果(JSON字符串)
     * @throws IllegalArgumentException 参数错误时抛出
     * @throws Exception 系统错误时抛出
     */
    public static String queryByPayOrderNo(String merchantNo, String payOrderNo, String channelId) throws Exception {
        // 参数校验
        if (StringUtils.isEmpty(merchantNo)) {
            throw new IllegalArgumentException("商户号不能为空");
        }
        if (StringUtils.isEmpty(payOrderNo)) {
            throw new IllegalArgumentException("支付订单号不能为空");
        }

        V3CcssCounterOrderQueryRequest request = new V3CcssCounterOrderQueryRequest();
        request.setMerchantNo(merchantNo);
        request.setPayOrderNo(payOrderNo);
        request.setChannelId(channelId);
        return LKLSDK.httpPost(request);
    }
}
