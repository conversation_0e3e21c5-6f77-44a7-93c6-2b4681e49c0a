<?xml version="1.0" encoding="UTF-8"?>
<!--Configuration后面的status，这个用于设置log4j2自身内部的信息输出，可以不设置，当设置成trace时，你会看到log4j2内部各种详细输出-->
<!--monitorInterval：Log4j2 自动检测修改配置文件和重新配置本身，设置间隔秒数-->
<configuration status="INFO" monitorInterval="5">
    <!--日志级别以及优先级排序: OFF > FATAL > ERROR > WARN > INFO > DEBUG > TRACE > ALL -->
    <!--变量配置-->
    <Properties>
        <!-- 格式化输出：%date表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度 %msg：日志消息，%n是换行符-->
        <!-- %logger{36} 表示 Logger 名字最长36个字符 -->
        <property name="LOG_PATTERN" value="%date{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"/>
        <!-- 定义日志存储的路径，不要配置相对路径 -->
        <property name="FILE_PATH" value="/usr/jmt/jmt-admin/logs"/>
        <property name="FILE_NAME" value="jmt-admin"/>
    </Properties>

    <appenders>
        <console name="Console" target="SYSTEM_OUT">
            <!--输出日志的格式-->
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <!--控制台只输出level及其以上级别的信息（onMatch），其他的直接拒绝（onMismatch）-->
            <ThresholdFilter level="DEBUG" onMatch="ACCEPT" onMismatch="DENY"/>
        </console>

        <!-- 这个会打印出所有的INFO及以上级别的信息，每次大小超过size日志会自动存入按年-月-日格式建立的文件夹下面并进行存档-->
        <RollingFile name="RollingLogFile" fileName="${FILE_PATH}/${FILE_NAME}-java-log.log"
                     filePattern="${FILE_PATH}/${FILE_NAME}-java-log-%d{yyyy-MM-dd}_%i.log">
            <!--控制台只输出level及以上级别的信息（onMatch），其他的直接拒绝（onMismatch）-->
            <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${LOG_PATTERN}" charset="UTF-8"/>
            <Policies>
                <!--interval属性用来指定多久滚动一次，默认是1 单位取决于filePattern时间精确度-->
                <TimeBasedTriggeringPolicy interval="1"/>
                <SizeBasedTriggeringPolicy size="1MB"/>
            </Policies>
            <!--保存日志文件个数 超出max 会删除旧的文件-->
            <DefaultRolloverStrategy max="1024">
                <Delete basePath="${FILE_PATH}" maxDepth="2">
                    <IfLastModified age="15d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>
    </appenders>

    <!--Logger节点用来单独指定日志的形式，比如要为指定包下的class指定不同的日志级别等。-->
    <!--然后定义loggers，只有定义了logger并引入的appender，appender才会生效-->
    <loggers>
        <!--日志级别-->
        <root level="INFO">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingLogFile"/>
        </root>
    </loggers>
</configuration>