<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.SysBusinessCategoryDao">
    <resultMap id="BaseResultMap" type="com.jmt.model.admin.resource.SysBusinessCategory">
        <id property="id" column="id" />
        <result property="categoryNo" column="categoryNo" />
        <result property="categoryName" column="categoryName" />
        <result property="createTime" column="createTime" />
        <result property="updateTime" column="updateTime" />
        <result property="isDelete" column="isDelete" />
    </resultMap>
    <sql id="Base_Column_List">
        id, categoryNo, categoryName, createTime, updateTime, isDelete
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_business_category
        where id = #{id}
    </select>
    <update id="deleteByPrimaryKey" parameterType="java.lang.Long">
        update sys_business_category
        set isDelete = 1, updateTime = NOW()
        where id = #{id} and isDelete = 0
    </update>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.admin.resource.SysBusinessCategory" useGeneratedKeys="true">
        insert into sys_business_category
            (id, categoryNo, categoryName, createTime, updateTime, isDelete)
        values (#{id}, #{categoryNo}, #{categoryName}, #{createTime}, #{updateTime}, #{isDelete})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.admin.resource.SysBusinessCategory" useGeneratedKeys="true">
        insert into sys_business_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="categoryNo != null">categoryNo,</if>
            <if test="categoryName != null">categoryName,</if>
            <if test="createTime != null">createTime,</if>
            <if test="updateTime != null">updateTime,</if>
            <if test="isDelete != null">isDelete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="categoryNo != null">#{categoryNo},</if>
            <if test="categoryName != null">#{categoryName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.jmt.model.admin.resource.SysBusinessCategory">
        update sys_business_category
        <set>
            <if test="categoryNo != null">categoryNo = #{categoryNo},</if>
            <if test="categoryName != null">categoryName = #{categoryName},</if>
            <if test="createTime != null">createTime = #{createTime},</if>
            <if test="updateTime != null">updateTime = #{updateTime},</if>
            <if test="isDelete != null">isDelete = #{isDelete},</if>
        </set>
        where id = #{id}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.jmt.model.admin.resource.SysBusinessCategory">
        update sys_business_category
        set categoryNo = #{categoryNo}, categoryName = #{categoryName}, createTime = #{createTime}, updateTime = #{updateTime}, isDelete = #{isDelete}
        where id = #{id}
    </update>
</mapper>