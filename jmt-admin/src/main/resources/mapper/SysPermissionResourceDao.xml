<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.SysPermissionResourceDao">
    <delete id="deleteByPermissId" parameterType="java.lang.Long">
        DELETE FROM sys_permission_resource WHERE permissId = #{permissId}
    </delete>
    <!-- 插入记录 -->
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.admin.permission.dto.SysPermissionResourceDto" useGeneratedKeys="true">
        INSERT INTO sys_permission_resource
        (permissId, resoureId, createTime, updateTime, isDelete)
        VALUES (#{permissId}, #{resourceId}, NOW(), NOW(), 0)
    </insert>
</mapper>