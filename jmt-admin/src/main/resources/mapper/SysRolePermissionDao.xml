<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.SysRolePermissionDao">

    <!-- 根据主键删除 -->
    <delete id="deleteByRoleId" parameterType="java.lang.Long">
        DELETE FROM sys_role_permission WHERE roleId = #{roleId}
    </delete>

    <!-- 插入记录（全字段） -->
    <insert id="insert" parameterType="com.jmt.model.admin.role.dto.SysRolePermissionDto">
        INSERT INTO sys_role_permission
        (permissId, roleId,createTime, updateTime, isDelete)
        VALUES
        (#{permissId},#{roleId}, NOW(),NOW(), 0)
    </insert>
</mapper>