<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.SysUserRoleDao">

    <delete id="deleteUserRoleByUserId" parameterType="java.lang.Long">
        DELETE FROM sys_user_role WHERE userId = #{userId}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.admin.user.SysUserRole" useGeneratedKeys="true">
        INSERT INTO sys_user_role (userId, roleId, createTime, updateTime, isDelete) VALUES (#{userId}, #{roleId}, NOW(), NOW(), 0)
    </insert>
</mapper>
