<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.SysAdressDao">
    <resultMap id="BaseMap" type="com.jmt.model.admin.address.SysAddress">
        <result property="placeType" column="placeType" />
        <result property="placeCode" column="placeCode" />
        <result property="placeName" column="placeName" />
        <result property="upPlaceName" column="upPlaceName" />
        <result property="zipCode" column="zipCode" />
    </resultMap>
    <select id="getByPlaceType" resultMap="BaseMap">
        select
        placeType,
        placeCode,
        placeName,
        upPlaceName,
        zipCode
        from ld_address
        where
            placeType = #{placeType}
    </select>
    <select id="getByPrimaryKey" resultMap="BaseMap">
        select
            placeType,
            placeCode,
            placeName,
            upPlaceName,
            zipCode
        from ld_address
        where placeType = #{placeType}
        and upPlaceName = #{placeCode}
    </select>

</mapper>