<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.SysResourceDao">
    <sql id="Base_Column_List">
        id, resKey, resName, resUrl,serverName, createTime, updateTime, isDelete
    </sql>
    <select id="getInfo" parameterType="java.lang.Long" resultType="com.jmt.model.admin.resource.vo.SysResourceVo">
        SELECT <include refid="Base_Column_List" /> FROM sys_resource WHERE id = #{id}
    </select>
    <select id="findByResourceIds" resultType="com.jmt.model.admin.resource.SysResource">
        SELECT <include refid="Base_Column_List" /> FROM sys_resource WHERE isDelete = 0 AND id IN <foreach collection="resourceIds" item="id" open="(" separator="," close=")">#{id}</foreach>
    </select>

    <select id="getPage" resultType="com.jmt.model.admin.resource.vo.SysResourceVo" parameterType="com.jmt.model.admin.resource.SysResource">
        SELECT <include refid="Base_Column_List" /> FROM sys_resource t
        <where>
            <if test="resKey != null and resKey != ''">AND t.resKey = #{resKey}</if>
            <if test="resName != null and resName != ''">AND t.resName = #{resName}</if>
            <if test="resUrl != null and resUrl != ''">AND t.resUrl = #{resUrl}</if>
            <if test="serverName != null and serverName != ''">AND t.serverName = #{serverName}</if>
            AND t.isDelete = 0
        </where>
        ORDER BY t.createTime DESC
    </select>
    <select id="selectAllAvailable" resultType="com.jmt.model.admin.resource.vo.SysResourceVo">
        SELECT <include refid="Base_Column_List" /> FROM sys_resource WHERE isDelete = 0 ORDER BY createTime DESC
    </select>
    <delete id="delete" parameterType="java.lang.Long">
        DELETE FROM sys_resource WHERE id = #{id}
    </delete>

    <update id="update" parameterType="com.jmt.model.admin.resource.dto.SysResourceDto">
        UPDATE sys_resource SET
            resKey = #{resKey},
            resName = #{resName},
            resUrl = #{resUrl},
            serverName = #{serverName},
            updateTime = NOW()
        WHERE id = #{id}
    </update>

<!--    <select id="getResourcesByPermissionId" resultType="com.jmt.model.admin.resource.vo.SysResourceVo">-->
<!--    SELECT-->
<!--        <include refid="Base_Column_List" />-->
<!--    FROM-->
<!--        sys_resource	t-->
<!--    WHERE-->
<!--        t.isDelete = 0 AND EXISTS(SELECT id FROM sys_permission_resource pr WHERE pr.isDelete=0 AND pr.permissId = t.id AND pr.permissId = 1)-->
<!--    </select>-->

    <select id="getResourcesByPermissionId" resultType="com.jmt.model.admin.resource.vo.SysResourceVo">
        SELECT
            t.id,
            t.resKey,
            t.resName,
            t.resUrl,
            t.serverName,
            t.createTime,
            t.updateTime,
            t.isDelete
        FROM sys_resource t
                 INNER JOIN sys_permission_resource pr ON t.id = pr.resoureId AND pr.isDelete = 0
        WHERE t.isDelete = 0 AND pr.permissId = #{id}
    </select>
</mapper>