<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.SysRoleDao">
    <sql id="Base_Column_List">
        id, roleKey, roleName, createTime, updateTime, isDelete
    </sql>
    <select id="getPage" parameterType="com.jmt.model.admin.role.dto.SysRolePageQueryDto" resultType="com.jmt.model.admin.role.vo.SysRoleVo">
        SELECT <include refid="Base_Column_List" /> FROM sys_role t
        <where>
            <if test="roleKey != null and roleKey != ''">AND t.roleKey = #{roleKey}</if>
            <if test="roleName != null and roleName != ''">AND t.roleName = #{roleName}</if>
            AND t.isDelete = 0
        </where>
        ORDER BY t.createTime DESC
    </select>
    <update id="delete" parameterType="java.lang.Long">
        UPDATE sys_role SET isDelete = 1, updateTime = NOW() WHERE id = #{id} AND isDelete = 0
    </update>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.admin.role.dto.SysRoleDto" useGeneratedKeys="true">
        INSERT INTO sys_role (
        roleKey, roleName, createTime, updateTime, isDelete
        ) VALUES
        (#{roleKey}, #{roleName}, NOW(),  NOW(), 0)
    </insert>
    <update id="update" parameterType="com.jmt.model.admin.role.SysRole">
        UPDATE sys_role SET
            roleKey = #{roleKey},
            roleName = #{roleName},
            updateTime = NOW()
        WHERE id = #{id}
    </update>

    <select id="getUserRoles" parameterType="java.lang.Long" resultType="com.jmt.model.admin.role.vo.SysRoleVo">
        SELECT
            t.id,t.roleName,t.roleKey
        FROM
            sys_role t
        WHERE
            t.isDelete = 0 AND EXISTS(SELECT id FROM sys_user_role ur WHERE ur.isDelete=0 AND ur.roleId = t.id AND ur.userId = #{userId})
    </select>

    <select id="getInfo" parameterType="java.lang.Long" resultType="com.jmt.model.admin.role.vo.SysRoleWithPermissionVo">
        SELECT <include refid="Base_Column_List" /> FROM sys_role WHERE id = #{id}
    </select>

    <select id="selectAll" resultType="com.jmt.model.admin.role.vo.SysRoleVo">
        SELECT <include refid="Base_Column_List" /> FROM sys_role WHERE isDelete = 0
    </select>
</mapper>