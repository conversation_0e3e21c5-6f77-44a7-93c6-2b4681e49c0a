<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.SysPermissionDao">
    <sql id="Base_Column_List">
        `id`, `parentId`, `permissKey`, `permissName`, `permissType`, `routerName`, `routerPath`, `routerComponent`, `routerIcon`, `listSort`, `isHide`, `createTime`, `updateTime`, `isDelete`
    </sql>
    <select id="getInfo" parameterType="java.lang.Long" resultType="com.jmt.model.admin.permission.vo.SysPermissionWithResourceVo">
        select <include refid="Base_Column_List" /> from sys_permission where id = #{id}
    </select>
    <select id="getPage" resultType="com.jmt.model.admin.permission.SysPermission" parameterType="com.jmt.model.admin.permission.SysPermission">
        SELECT <include refid="Base_Column_List" /> FROM sys_permission t
        <where>
            <if test="permissKey != null and permissKey != ''">AND t.permissKey = #{permissKey}</if>
            <if test="permissName != null and permissName != ''">AND t.permissName = #{permissName}</if>
            <if test="isDelete != null">AND t.isDelete = #{isDelete}</if>
        </where>
        ORDER BY t.createTime DESC
    </select>

    <select id="findTopLevelPermissions" resultType="com.jmt.model.admin.permission.SysPermission">
        SELECT * FROM sys_permission WHERE (parentId IS NULL OR parentId = 0) AND isDelete = 0 ORDER BY id ASC
    </select>
    <select id="findChildrenByParentId" resultType="com.jmt.model.admin.permission.SysPermission">
        SELECT * FROM sys_permission WHERE parentId = #{parentId} AND isDelete = 0 ORDER BY id ASC
    </select>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.admin.permission.dto.SysPermissionDto" useGeneratedKeys="true">
        INSERT INTO `sys_permission`(
        `parentId`, `permissKey`, `permissName`, `permissType`, `routerName`, `routerPath`, `routerComponent`, `routerIcon`, `listSort`, `isHide`, `createTime`, `updateTime`, `isDelete`)
        VALUES (
        #{parentId}, #{permissKey}, #{permissName}, #{permissType}, #{routerName}, #{routerPath}, #{routerComponent}, #{routerIcon}, #{listSort}, #{isHide}, NOW(), NOW(), 0);
    </insert>

    <update id="update" parameterType="com.jmt.model.admin.permission.dto.SysPermissionDto">
        UPDATE sys_permission SET
            `parentId` = #{parentId},
            `permissKey` = #{permissKey},
            `permissName` = #{permissName},
            `permissType` = #{permissType},
            `routerName` = #{routerName},
            `routerPath` = #{routerPath},
            `routerComponent` = #{routerComponent},
            `routerIcon` = #{routerIcon},
            `listSort` = #{listSort},
            `isHide` = #{isHide},
            `updateTime` = NOW()
        WHERE id = #{id}
    </update>

    <select id="getRolePermissions" parameterType="java.lang.Long" resultType="com.jmt.model.admin.permission.vo.SysPermissionVo">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            sys_permission	t
        WHERE
            t.isDelete = 0 AND t.isHide = 0 AND EXISTS(SELECT id FROM sys_role_permission sp WHERE sp.isDelete=0 AND sp.permissId	= t.id AND sp.roleId = #{roleId})
    </select>

    <update id="delete" parameterType="java.lang.Long">
        UPDATE sys_permission SET `isDelete`=1,`updateTime` = NOW() WHERE id = #{id}
    </update>

    <select id="selectAll" resultType="com.jmt.model.admin.permission.vo.SysPermissionVo">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            sys_permission
        WHERE
            isDelete = 0 AND isHide = 0
    </select>
</mapper>