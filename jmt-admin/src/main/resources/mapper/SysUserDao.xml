<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.dao.SysUserDao">
	<sql id="Base_Column_List">
		id, loginName,telPhone,userName, uaaId, userStatus,isFirstLogin, createTime, updateTime, isDelete, password
	</sql>

	<select id="getPage" parameterType="com.jmt.model.admin.user.dto.SysUserPageQueryDto" resultType="com.jmt.model.admin.user.vo.SysUserWithRolesVo">
		SELECT
			t.id,
			t.userName,
			t.loginName,
			t.telPhone,
			t.uaaId,
			t.userStatus,
			t.isDelete,
			t.createTime,
			t.updateTime,
			t.isFirstLogin,
			-- 合并角色名称，DISTINCT去重，SEPARATOR指定分隔符
			GROUP_CONCAT(DISTINCT r.roleName SEPARATOR ',') AS roles
		FROM
			sys_user t
			-- 关联用户角色关系表（只关联未删除的）
			LEFT JOIN sys_user_role ur ON t.id = ur.userId AND ur.isDelete = 0
			-- 关联角色表（只关联未删除的）
			LEFT JOIN sys_role r ON ur.roleId = r.id AND r.isDelete = 0
		WHERE
			t.isDelete = 0
			<if test="userName != null and userName != ''">
				AND t.userName LIKE CONCAT('%', #{userName}, '%')
			</if>
			<if test="loginName != null and loginName != ''">
				AND t.loginName LIKE CONCAT('%', #{loginName}, '%')
			</if>
		-- 按用户唯一字段分组（确保每个用户只出一条记录）
		GROUP BY
			t.id
		ORDER BY
			t.createTime DESC
	</select>

	<select id="getInfo" parameterType="java.lang.Long" resultType="com.jmt.model.admin.user.vo.SysUserVo">
		SELECT
			<include refid="Base_Column_List" />
	 	FROM
			sys_user t
		WHERE
			id = #{id} AND t.isDelete = 0
	</select>

	<update id="delete" parameterType="java.lang.Long">
		UPDATE sys_user SET isDelete = 1, updateTime = NOW() WHERE id = #{id}
	</update>

	<insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.jmt.model.admin.user.dto.SysUserDto" useGeneratedKeys="true">
		INSERT INTO sys_user
		(loginName,telPhone,userName, uaaId,userStatus, isFirstLogin, createTime, updateTime, isDelete, password)
		VALUES (#{loginName}, #{telPhone},#{userName}, #{uaaId}, 0,0, NOW(), NOW(), 0, #{password})
	</insert>

	<update id="update" parameterType="com.jmt.model.admin.user.dto.SysUserDto">
		update sys_user set
			userName = #{userName},
			updateTime = NOW()
		where
			id = #{id}
	</update>

	<select id="selectComboList" parameterType="string" resultType="com.jmt.model.admin.user.vo.SysUserSelectVo">
		SELECT
			id, userName
		FROM
			sys_user
		WHERE
			isDelete = 0
			<if test="keyword != null and keyword != ''">
				AND userName LIKE CONCAT('%', #{keyword}, '%')
			</if>
		ORDER BY userName ASC
	</select>

	<select id="getInfoByUaaId" resultType="com.jmt.model.admin.user.vo.SysUserVo">
		SELECT
			<include refid="Base_Column_List" />
		FROM
			sys_user
		WHERE
			isDelete = 0 AND uaaId = #{uaaId}
		LIMIT 1
	</select>
	<update id="changeStatus">
		update sys_user set
			userStatus = #{userStatus},
			updateTime = NOW()
		where
			id = #{id}
	</update>
</mapper>
