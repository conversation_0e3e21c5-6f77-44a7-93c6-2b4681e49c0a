package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.admin.vo.IndexSumVo;
import com.jmt.service.index.SysIndexService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


@RestController
@RequestMapping("/admin/index/v1")
public class SysIndexController extends BaseController {
    @Resource
    private SysIndexService sysIndexService;
    /**
     * 首页根据省份统计数据
     * @return
     */
    @GetMapping("/getNumberByProvince")
    public String getNumberByProvince(@RequestParam String province) {
        IndexSumVo indexSumVo = sysIndexService.getNumberByProvince(province);
        return super.responseSuccess(indexSumVo, "查询成功");
    }
}
