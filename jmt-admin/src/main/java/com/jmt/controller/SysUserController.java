package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.admin.user.*;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.auth.UserInfo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.service.user.SysUserService;
import com.jmt.util.LoginUserUtil;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;

// 类上添加@Validated，开启方法参数校验
@Validated
@RestController
@RequestMapping("/admin/user/v1")
public class SysUserController extends BaseController {

    @Resource
    private SysUserService sysUserService;

    /**
     * 获取登录信息
     * @return
     */
    @PostMapping("userInfo")
    @ResponseBody
    public String userInfo() {
        LoginUaaUser loginUser = LoginUserUtil.get();
        assert loginUser != null;
        SysUserVO userInfo = sysUserService.getInfoByUaaId(loginUser.getUaaId());
        return super.responseSuccess(userInfo,"查询成功");
    }
    /**
     * 用户启用/禁用接口
     * @param userId 用户ID（非空）
     * @param status 状态（只能是0或1）
     */
    @PostMapping("/changeStatus")
    public String changeStatus(
            @RequestParam @NotNull(message = "用户ID不能为空") Long userId,
            @RequestParam @NotNull(message = "状态不能为空")
            @Min(value = 0, message = "状态只能是0或1")
            @Max(value = 1, message = "状态只能是0或1") Integer status) {
        sysUserService.changeStatus(userId, status);
        return super.responseSuccess(status == 1 ? "用户启用成功" : "用户禁用成功");
    }

    /**
     * 用户批量删除接口
     * @param userIds 用户ID列表（非空且不为空列表）
     */
    @PostMapping("/batchDelete")
    public String batchDelete(
            @RequestBody
            @NotNull(message = "用户ID列表不能为空")
            @NotEmpty(message = "用户ID列表不能为空") List<Long> userIds) {
        sysUserService.batchDelete(userIds);
        return super.responseSuccess("批量删除用户成功");
    }

    /**
     * 用户分配角色接口（批量）
     * @param userId  用户ID（非空）
     * @param roleIds 角色ID列表（非空且不为空列表）
     */
    @PostMapping("/assignRoles")
    public String assignRoles(
            @RequestParam @NotNull(message = "用户ID不能为空") Long userId,
            @RequestBody
            @NotNull(message = "角色ID列表不能为空")
            @NotEmpty(message = "角色ID列表不能为空") List<Long> roleIds) {
        sysUserService.assignRoles(userId, roleIds);
        return super.responseSuccess("角色分配成功");
    }

    /**
     * 用户下拉框数据接口
     * @param keyword 搜索关键字（可选，若提供则不能为纯空白）
     */
    @GetMapping("/comboList")
    public String comboList(
            @RequestParam(required = false)
            @NotBlank(message = "搜索关键字不能为空白") String keyword) {
        List<SysUser> list = sysUserService.getComboList(keyword);
        return super.responseSuccess(list, "查询成功");
    }

    /**
     * 分页查询
     * @param pageQuery 分页查询参数（非空，内部字段需校验）
     * @param req       请求对象
     */
    @PostMapping("/getPage")
    public String getPage(
            @RequestBody(required = false) PageQuery<SysUserRoleName> pageQuery,
            HttpServletRequest req) {
        // 如果未传递PageQuery参数，则使用默认值
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
        }
        PageResult<SysUserRoleName> page = sysUserService.getPage(pageQuery);

        return super.responseSuccess(page, "查询成功");
    }

    /**
     * 查看用户信息
     * @param userId 用户ID（非空）
     */
    @GetMapping("/getInfo")
    public String getInfo(@RequestParam @NotNull(message = "用户ID不能为空") Long userId) {
        SysUserVO sysUser = sysUserService.getInfo(userId);
        if (sysUser == null) {
            return super.responseFail("用户不存在");
        }
        SysUserDetail userDetail = sysUserService.getUserDetail(sysUser);
        return super.responseSuccess(userDetail, "查询成功");
    }

    /**
     * 新增用户
     * @param user 新增用户参数（非空，内部字段需校验）
     */
    @PostMapping("/add")
    public String add( @RequestBody @Valid @NotNull(message = "用户信息不能为空") SysUser user, @RequestParam(required = false) String[] roleNames) {
        LoginUaaUser loginUser = LoginUserUtil.get();
        if (loginUser == null) {
            return super.responseFail("UaaId=null");
        }
        user.setUaaId(loginUser.getUaaId());
        Long userId = sysUserService.add(user,roleNames);
        return super.responseSuccess(userId, "新增成功");
    }

    /**
     * 删除用户
     * @param userId 用户ID（非空）
     */
    @PostMapping("/delete")
    public String delete(@RequestParam @NotNull(message = "用户ID不能为空") Long userId) {
        sysUserService.delete(userId);
        return super.responseSuccess("删除成功");
    }

    /**
     * 更新用户信息
     * @param user 更新用户参数（非空，内部字段需校验）
     */
    @PostMapping("/update")
    public String update(
            @RequestBody @Valid @NotNull(message = "用户信息不能为空") SysUser user) {
        sysUserService.update(user);
        return super.responseSuccess("更新成功");
    }

    /**
     * 为用户添加角色
     * @param sysUserRoleDto 用户角色关联信息（非空，内部字段需校验）
     */
    @PostMapping("/addUserRole")
    public String addUserRole(
            @RequestBody @Valid @NotNull(message = "用户角色关联信息不能为空") SysUserRoleDto sysUserRoleDto) {
        sysUserService.addUserRole(sysUserRoleDto);
        return super.responseSuccess("添加成功");
    }

    /**
     * 删除用户的角色
     * @param sysUserRoleDto 用户角色关联信息（非空，内部字段需校验）
     */
    @PostMapping("/deleteUserRole")
    public String deleteUserRole(
            @RequestBody @Valid @NotNull(message = "用户角色关联信息不能为空") SysUserRoleDto sysUserRoleDto) {
        sysUserService.deleteUserRole(sysUserRoleDto);
        return super.responseSuccess("删除成功");
    }
}
