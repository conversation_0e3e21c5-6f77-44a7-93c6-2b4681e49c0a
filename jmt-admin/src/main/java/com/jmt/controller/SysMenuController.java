package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.admin.menu.AppRouteRecord;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.service.menu.SysMenuService;
import com.jmt.util.LoginUserUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/admin/menu/v1")
public class SysMenuController extends BaseController {

    @Resource
    private SysMenuService sysMenuService;

    /**
     * 获取用户菜单
     * @return
     */
    @GetMapping("/userMenuList")
    @ResponseBody
    public String userMenuList() {
        LoginUaaUser loginUaaUser = LoginUserUtil.get();
        List<AppRouteRecord> appRouteRecordList = sysMenuService.getUserMenuList(loginUaaUser);
        return super.responseSuccess(appRouteRecordList,"查询成功");
    }
}
