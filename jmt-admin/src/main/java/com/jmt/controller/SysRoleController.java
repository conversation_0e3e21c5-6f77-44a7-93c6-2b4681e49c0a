package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.admin.role.RolePermissionDTO;
import com.jmt.model.admin.role.SysRole;
import com.jmt.model.admin.role.SysRolePermissionDto;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.service.role.SysRoleService;
import com.jmt.util.LoginUserUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/admin/role/v1")

public class SysRoleController extends BaseController {


    @Resource
    private SysRoleService sysRoleService;
    /**
     * 角色查看接口
     * @param roleId 角色ID
     * @return 角色详情
     */
    @GetMapping("/getRoleInfo")
    public String getRoleInfo(@RequestParam Long roleId) {
        try {
            SysRole role = sysRoleService.getRoleInfo(roleId);
            return super.responseSuccess(role, "查询成功");
        } catch (Exception e) {
            logger.error("查询角色详情失败，角色ID: {}", roleId, e);
            return super.responseFail("查询角色详情失败");
        }
    }
    /**
     * 角色启用/禁用接口
     * @param roleId 角色ID
     * @param status 状态（0-禁用，1-启用）
     * @return 操作结果
     */
    @PostMapping("/enableRole")
    public String enableRole(@RequestParam Long roleId, @RequestParam Integer status) {
        try {
            sysRoleService.enableRole(roleId, status);
            return super.responseSuccess(status == 1 ? "启用角色成功" : "禁用角色成功");
        } catch (Exception e) {
            logger.error("角色启用/禁用失败，角色ID: {}, 状态: {}", roleId, status, e);
            return super.responseFail(e.getMessage());
        }
    }
    /**
     * 角色批量删除接口
     * @param roleIds 角色ID列表
     * @return 操作结果
     */
    @PostMapping("/batchDeleteRoles")
    public String batchDeleteRoles(@RequestBody List<Long> roleIds) {
        try {
            int count = sysRoleService.batchDeleteRoles(roleIds);
            return super.responseSuccess("批量删除成功，共删除" + count + "个角色");
        } catch (Exception e) {
            logger.error("批量删除角色失败，角色ID列表: {}", roleIds, e);
            return super.responseFail("批量删除角色失败");
        }
    }
    /**
     * 角色分配权限接口
     * @return 操作结果
     */
    @PostMapping("/assignPermissions")
    public String assignPermissions(@RequestBody RolePermissionDTO dto) {
        try {
            sysRoleService.assignPermissions(dto.getRoleId(), dto.getPermissionIds());
            return super.responseSuccess("分配权限成功");
        } catch (Exception e) {
            logger.error("分配权限失败，角色ID: {}, 权限ID列表: {}", dto.getRoleId(), dto.getPermissionIds(), e);
            return super.responseFail("分配权限失败");
        }
    }
    /**
     * 角色下拉框数据接口
     * @return 角色下拉框数据列表
     */
    @GetMapping("/getSelectOptions")
    public String getSelectOptions() {
        try {
            List<Map<String, Object>> options = sysRoleService.getRoleSelectOptions();
            return super.responseSuccess(options, "查询成功");
        } catch (Exception e) {
            logger.error("获取角色下拉框数据失败", e);
            return super.responseFail("获取角色下拉框数据失败");
        }
    }
    /**
     * 分页查询
     *
     * @param pageQuery 角色列表查询条件
     * @param req       HttpServletRequest对象
     * @return 包含查询结果的JSON字符串
     */
    @PostMapping("/getPage")
    public String getPage(@RequestBody(required = false) PageQuery<SysRole> pageQuery, HttpServletRequest req) {
        try {
            // 如果未传递PageQuery参数，则使用默认值
            if (pageQuery == null) {
                pageQuery = new PageQuery<>();
            }
            PageResult<SysRole> page = sysRoleService.getPage(pageQuery);
            return super.responseSuccess(page, "查询成功");
        } catch (Exception e) {
            logger.error("分页查询角色信息时出现异常", e);
            return super.responseFail("分页查询角色信息时出现异常");
        }
    }

    /**
     * 更新角色信息
     * @param sysRole 角色实体
     * @return 操作结果信息
     */
    @PostMapping("/updateRole")
    public String updateRole(@RequestBody SysRole sysRole) {
        try {
            sysRoleService.updateRole(sysRole);
            return super.responseSuccess("更新角色成功");
        } catch (Exception e) {
            logger.error("更新角色时出现异常，角色信息: {}", sysRole, e);
            return super.responseFail("更新角色失败");
        }
    }

    /**
     * 删除角色信息
     * @param sysRole 角色实体
     * @return 操作结果信息
     */
    @PostMapping("/deleteRole")
    public String deleteRole(@RequestBody SysRole sysRole) {
        try {
            sysRoleService.deleteRole(sysRole);
            return super.responseSuccess("删除角色成功");
        } catch (Exception e) {
            logger.error("删除角色时出现异常，角色信息: {}", sysRole, e);
            return super.responseFail("删除角色失败");
        }
    }

    /**
     * 创建新角色
     * @param sysRole 角色实体
     * @return 操作结果信息
     */
    @PostMapping("/addRole")
    public String addRole(@RequestBody SysRole sysRole) {
        try {
            Long id = sysRoleService.addRole(sysRole);
            return super.responseSuccess(id,"创建角色成功");
        } catch (Exception e) {
            logger.error("创建角色时出现异常，角色信息: {}", sysRole, e);
            return super.responseFail("创建角色失败");
        }
    }

    /**
     * 添加角色权限
     * @param sysRolePermissionDto 角色权限数据传输对象
     * @return 操作结果信息
     */
    @PostMapping("/addRolePermission")
    public String addRolePermission(@RequestBody SysRolePermissionDto sysRolePermissionDto) {
        try {
            sysRoleService.addRolePermission(sysRolePermissionDto);
            return super.responseSuccess("添加角色权限成功");
        } catch (Exception e) {
            logger.error("添加角色权限时出现异常，角色权限信息: {}", sysRolePermissionDto, e);
            return super.responseFail("添加角色权限失败");
        }
    }

    /**
     * 更新角色权限
     * @param sysRolePermissionDto 角色权限数据传输对象
     * @return 操作结果信息
     */
    @PostMapping("/updateRolePermission")
    public String updateRolePermission(@RequestBody SysRolePermissionDto sysRolePermissionDto) {
        try {
            sysRoleService.updateRolePermission(sysRolePermissionDto);
            return super.responseSuccess("更新角色权限成功");
        } catch (Exception e) {
            logger.error("更新角色权限时出现异常，角色权限信息: {}", sysRolePermissionDto, e);
            return super.responseFail("更新角色权限失败");
        }
    }

    /**
     * 删除角色权限
     * @param sysRolePermissionDto 角色权限数据传输对象
     * @return 操作结果信息
     */
    @PostMapping("/deleteRolePermission")
    public String deleteRolePermission(@RequestBody SysRolePermissionDto sysRolePermissionDto) {
        try {
            sysRoleService.deleteRolePermission(sysRolePermissionDto);
            return super.responseSuccess("删除角色权限成功");
        } catch (Exception e) {
            logger.error("删除角色权限时出现异常，角色权限信息: {}", sysRolePermissionDto, e);
            return super.responseFail("删除角色权限失败");
        }
    }
}
