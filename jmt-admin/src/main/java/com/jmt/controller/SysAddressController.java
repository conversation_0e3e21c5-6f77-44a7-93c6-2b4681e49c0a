package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.service.address.SysAdressService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/admin/index/v1")
public class SysAddressController extends BaseController {

    @Resource
    private SysAdressService sysAdressService;

    @RequestMapping("/getAddress")
    public String sysAddress(){
        return super.responseSuccess(sysAdressService.getAddress(), "查询成功");
    }

}
