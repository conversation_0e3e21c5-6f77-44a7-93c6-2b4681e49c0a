package com.jmt.service.resource;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.dao.SysResourceDao;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.admin.resource.SysResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SysResourceService extends BaseService {

    @Resource
    private SysResourceDao sysResourceDao;

    /**
     * 新增资源
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addResource(SysResource sysResource) {
        if (sysResource == null) {
            throw new IllegalArgumentException("资源信息不能为空");
        }
        setCreateFields(sysResource);
        sysResourceDao.insert(sysResource);
        return sysResource.getId();
    }

    /**
     * 更新资源
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer updateResource(SysResource sysResource) {
        if (sysResource == null || sysResource.getId() == null) {
            throw new IllegalArgumentException("资源ID不能为空");
        }
        setUpdateFields(sysResource);
        return sysResourceDao.updateByPrimaryKeySelective(sysResource);
    }

    /**
     * 逻辑删除资源
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteResource(SysResource sysResource) {
        if (sysResource == null || sysResource.getId() == null) {
            throw new IllegalArgumentException("资源ID不能为空");
        }
        setDeleteFields(sysResource);
        return sysResourceDao.updateByPrimaryKeySelective(sysResource);
    }

    /**
     * 资源分页查询
     */
    public PageResult<SysResource> getPage(PageQuery<SysResource> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        SysResource query = pageQuery.getQueryData();
        List<SysResource> list = sysResourceDao.getPage(query);
        return new PageResult<>(list);
    }

    /**
     * 查询资源详情
     */
    public SysResource getResourceInfo(Long resourceId) {
        if (resourceId == null) {
            throw new IllegalArgumentException("资源ID不能为空");
        }
        SysResource resource = sysResourceDao.selectByPrimaryKey(resourceId);
        if (resource == null || resource.getIsDelete() == 1) {
            throw new RuntimeException("资源不存在或已被删除");
        }
        return resource;
    }

    /**
     * 下拉框数据
     */
    public List<Map<String, Object>> getResourceSelectOptions() {
        List<SysResource> resources = sysResourceDao.selectAllAvailable();
        if (resources == null || resources.isEmpty()) {
            return Collections.emptyList();
        }
        return resources.stream()
                .map(resource -> {
                    Map<String, Object> option = new HashMap<>();
                    option.put("id", resource.getId());
                    option.put("name", resource.getResName());
                    return option;
                })
                .collect(Collectors.toList());
    }

    // ================== 通用字段方法 ==================

    private void setCreateFields(SysResource resource) {
        Date now = new Date();
        resource.setCreateTime(now);
        resource.setUpdateTime(now);
        resource.setIsDelete(0);
    }

    private void setUpdateFields(SysResource resource) {
        resource.setUpdateTime(new Date());
    }

    private void setDeleteFields(SysResource resource) {
        resource.setUpdateTime(new Date());
        resource.setIsDelete(1);
    }
}
