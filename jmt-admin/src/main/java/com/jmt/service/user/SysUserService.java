package com.jmt.service.user;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.dao.*;
import com.jmt.model.admin.permission.SysPermission;
import com.jmt.model.admin.resource.SysResource;
import com.jmt.model.admin.role.SysRole;
import com.jmt.model.admin.user.*;
import com.jmt.model.auth.UserInfo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.util.EntityToVOConverter;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class SysUserService extends BaseService {

    @Resource
    private SysUserDao sysUserDao;

    @Resource
    private SysRoleDao sysRoleDao;

    @Resource
    private SysPermissionDao sysPermissionDao;

    @Resource
    private SysRolePermissionDao sysRolePermissionDao;

    @Resource
    private SysUserRoleDao sysUserRoleDao;

    @Resource
    private SysPermissionResourceDao sysPermissionResourceDao;

    @Resource
    private SysResourceDao sysResourceDao;

    /**
     * 用户启用/禁用（目前复用 isDelete 字段：0=启用，1=禁用）
     */
    @Transactional
    public Integer changeStatus(Long userId, Integer status) {
        if (userId == null) throw new IllegalArgumentException("userId不能为空");
        if (status == null || (status != 0 && status != 1)) {
            throw new IllegalArgumentException("status必须为0（禁用）或1（启用）");
        }
        SysUserVO user = sysUserDao.getInfo(userId);
        if (user == null)
            throw new RuntimeException("用户不存在");
        return sysUserDao.changeStatus(userId,status);
    }

    /**
     * 批量逻辑删除用户（级联处理角色关联）
     */
    @Transactional
    public void batchDelete(List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            throw new IllegalArgumentException("用户ID列表不能为空");
        }
        int successCount = 0;

        for (Long userId : userIds) {
            SysUser user = new SysUser();
            user.setId(userId);
            int updated = sysUserDao.updateByPrimaryKeySelective(user);
        }

        if (successCount != userIds.size()) {
            throw new RuntimeException("批量删除失败，部分用户不存在或已删除");
        }
    }

    /**
     * 分配角色（幂等处理：先清空旧的，再插入新的）
     */
    @Transactional
    public void assignRoles(Long userId, List<Long> roleIds) {
        if (userId == null) throw new IllegalArgumentException("userId不能为空");
        if (roleIds == null || roleIds.isEmpty()) throw new IllegalArgumentException("角色ID列表不能为空");

        Date now = new Date();
        SysUserRole deleteRole = new SysUserRole();
        deleteRole.setUserId(userId);
        deleteRole.setIsDelete(1);
        deleteRole.setUpdateTime(now);
        sysUserRoleDao.batchUpdateIsDeleteByUserId(deleteRole);

        List<SysUserRole> userRoles = roleIds.stream()
                .distinct() // 去重，避免重复角色
                .map(roleId -> {
                    SysUserRole ur = new SysUserRole();
                    ur.setUserId(userId);
                    ur.setRoleId(roleId);
                    ur.setCreateTime(now);
                    ur.setUpdateTime(now);
                    ur.setIsDelete(0);
                    return ur;
                }).collect(Collectors.toList());

        sysUserRoleDao.batchInsert(userRoles);
    }

    public List<SysUser> getComboList(String keyword) {
        return sysUserDao.selectComboList(keyword);
    }

    public PageResult<SysUserRoleName> getPage(PageQuery<SysUserRoleName> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<SysUserRoleName> list = sysUserDao.getPage(pageQuery.getQueryData());
        return new PageResult<>(list);
    }

    public SysUserDetail getUserDetail(SysUserVO user) {
        SysUserDetail sysUserDetail = new SysUserDetail();
        sysUserDetail.setUser(user);

        List<Long> roleIds = sysUserRoleDao.findAllByIdLongs(user.getId());
        if (roleIds.isEmpty()) return sysUserDetail;

        List<SysRole> roles = sysRoleDao.findByRoleIds(roleIds);
        sysUserDetail.setRoles(
                roles.stream().map(EntityToVOConverter::convertToRoleVO).collect(Collectors.toList())
        );

        List<Long> permissionIds = sysRolePermissionDao.findByRoleIds(roleIds);
        if (!permissionIds.isEmpty()) {
            List<SysPermission> permissions = sysPermissionDao.findByPermissionIds(permissionIds);
            sysUserDetail.setPermissions(
                    permissions.stream().map(EntityToVOConverter::convertToPermissionVO).collect(Collectors.toList())
            );

            List<Long> resourceIds = sysPermissionResourceDao.findByPermissionIds(permissionIds);
            if (!resourceIds.isEmpty()) {
                List<SysResource> resources = sysResourceDao.findByResourceIds(resourceIds);
                sysUserDetail.setResources(
                        resources.stream().map(EntityToVOConverter::convertToResourceVO).collect(Collectors.toList())
                );
            }
        }
        return sysUserDetail;
    }

    public SysUserVO getInfo(Long userId) {
        return sysUserDao.getInfo(userId);
    }

    public SysUserVO getInfoByUaaId(Long uaaId) {
        return sysUserDao.getInfoByUaaId(uaaId);
    }

    @Transactional
    public Long add(SysUser sysUser, String[] roleNames) {
        Date now = new Date();
        SysUser newSysUser = new SysUser();
        newSysUser.setUserName(sysUser.getUserName());
        newSysUser.setCreateTime(now);
        newSysUser.setUpdateTime(now);
        newSysUser.setIsFirstLogin(1);
        newSysUser.setIsDelete(0);
        newSysUser.setUaaId(sysUser.getUaaId());
        sysUserDao.insert(newSysUser);

        List<SysRole> availableRoles = sysRoleDao.selectAllAvailable();
        for (SysRole sysRole : availableRoles) {
            for (String roleName : roleNames) {
                if (Objects.equals(sysRole.getRoleName(), roleName)) {
                    SysUserRole sysUserRole = new SysUserRole();
                    sysUserRole.setUserId(newSysUser.getId());
                    sysUserRole.setRoleId(sysRole.getId());
                    sysUserRole.setCreateTime(now);
                    sysUserRole.setUpdateTime(now);
                    sysUserRole.setIsDelete(0);
                    sysUserRoleDao.insert(sysUserRole);
                }
            }
        }
        return newSysUser.getId();
    }

    @Transactional
    public Integer delete(Long userId) {
        sysUserRoleDao.deleteUserRoleByUserId(userId);
        return sysUserDao.delete(userId);
    }

    @Transactional
    public Integer update(SysUser sysUser) {
        sysUser.setUpdateTime(new Date());
        sysUser.setIsFirstLogin(0);
        return sysUserDao.updateByPrimaryKeySelective(sysUser);
    }

    public Integer addUserRole(SysUserRoleDto sysUserRoleDto) {
        Date now = new Date();
        SysUserRole sysUserRole = new SysUserRole();
        sysUserRole.setUserId(sysUserRoleDto.getUserId());
        sysUserRole.setRoleId(sysUserRoleDto.getRoleId());
        sysUserRole.setCreateTime(now);
        sysUserRole.setUpdateTime(now);
        sysUserRole.setIsDelete(0);
        return sysUserRoleDao.insert(sysUserRole);
    }

    public Integer deleteUserRole(SysUserRoleDto sysUserRoleDto) {
        SysUserRole sysUserRole = new SysUserRole();
        sysUserRole.setUserId(sysUserRoleDto.getUserId());
        sysUserRole.setRoleId(sysUserRoleDto.getRoleId());
        sysUserRole.setUpdateTime(new Date());
        sysUserRole.setIsDelete(1);
        return sysUserRoleDao.deleteUserRoleByUserIdAndRoleId(sysUserRole);
    }
}
