package com.jmt.service.address;

import com.jmt.base.BaseService;
import com.jmt.dao.SysAdressDao;
import com.jmt.model.admin.address.CityVO;
import com.jmt.model.admin.address.CountryVO;
import com.jmt.model.admin.address.EnumVO;
import com.jmt.model.admin.address.SysAddress;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
public class SysAdressService extends BaseService {

    @Resource
    private SysAdressDao sysAdressDao;

    public List<CityVO> getAddress(){
        List<CityVO> provinceArr = new ArrayList<>();
        //先查询出省
        List<SysAddress> provinces = sysAdressDao.getByPlaceType("01");
        for (SysAddress province : provinces) {
            // 根据省查询市的信息
            List<SysAddress> cities = sysAdressDao.getByPrimaryKey("02", province.getPlaceCode());
            CityVO cityVO = new CityVO();
            List<CountryVO> countryVOS = new ArrayList<>();
            for (SysAddress city : cities) {
                List<SysAddress> countries = sysAdressDao.getByPrimaryKey("03", city.getPlaceCode());
                CountryVO countryVO = new CountryVO();
                List<EnumVO> enumVOS = new ArrayList<>();
                // 一个市对应的多少个区countryArr集合
                for (SysAddress country : countries) {
                    EnumVO enumVO = new EnumVO();
                    enumVO.setType(country.getPlaceCode());
                    enumVO.setName(country.getPlaceName());
                    enumVOS.add(enumVO);
                }
                countryVO.setCountryArr(enumVOS);
                countryVO.setType(city.getPlaceCode());
                countryVO.setName(city.getPlaceName());
                countryVOS.add(countryVO);
            }
            cityVO.setCityArr(countryVOS);
            cityVO.setType(province.getPlaceCode());
            cityVO.setName(province.getPlaceName());
            provinceArr.add(cityVO);
        }
        return provinceArr;
    }
}
