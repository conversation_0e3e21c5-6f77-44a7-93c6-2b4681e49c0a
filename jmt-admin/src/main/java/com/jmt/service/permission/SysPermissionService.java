package com.jmt.service.permission;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.dao.SysPermissionDao;
import com.jmt.dao.SysPermissionResourceDao;
import com.jmt.dao.SysResourceDao;
import com.jmt.exception.BusinessException;
import com.jmt.model.admin.permission.dto.SysAssignResourceDto;
import com.jmt.model.admin.permission.dto.SysPermissionDto;
import com.jmt.model.admin.permission.dto.SysPermissionResourceDto;
import com.jmt.model.admin.permission.vo.SysPermissionVo;
import com.jmt.model.admin.permission.vo.SysPermissionWithResourceVo;
import com.jmt.model.admin.resource.vo.SysResourceVo;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.admin.permission.SysPermission;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SysPermissionService extends BaseService {

    @Resource
    private SysPermissionDao sysPermissionDao;

    @Resource
    private SysPermissionResourceDao sysPermissionResourceDao;

    @Resource
    private SysResourceDao sysResourceDao;

    /**
     * 新增权限
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addPermission(SysPermissionDto sysPermissionDto) {
        return sysPermissionDao.insert(sysPermissionDto);
    }

    /**
     * 更新权限
     */
    @Transactional(rollbackFor = Exception.class)
    public void updatePermission(SysPermissionDto sysPermissionDto) {
        sysPermissionDao.update(sysPermissionDto);
    }

    /**
     * 删除权限（逻辑删除）
     */
    @Transactional(rollbackFor = Exception.class)
    public void deletePermission(Long id) {
        sysPermissionResourceDao.deleteByPermissId(id);
        sysPermissionDao.delete(id);
    }

    /**
     * 权限分页查询
     */
    public PageResult<SysPermission> getPage(PageQuery<SysPermission> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        SysPermission query = pageQuery.getQueryData();
        List<SysPermission> list = sysPermissionDao.getPage(query);
        return new PageResult<>(list);
    }

    /**
     * 构建权限树
     */
    public List<Map<String, Object>> getPermissionTree(Long parentId) {
        List<SysPermission> permissions = getChildrenByParentId(parentId);
        return buildPermissionTree(permissions);
    }

    /**
     * 获取子权限
     */
    private List<SysPermission> getChildrenByParentId(Long parentId) {
        return (parentId == null)
                ? sysPermissionDao.findTopLevelPermissions()
                : sysPermissionDao.findChildrenByParentId(parentId);
    }

    /**
     * 递归构建树
     */
    private List<Map<String, Object>> buildPermissionTree(List<SysPermission> permissions) {
        if (permissions == null || permissions.isEmpty()) {
            return Collections.emptyList();
        }
        return permissions.stream().map(permission -> {
            Map<String, Object> node = new LinkedHashMap<>();
            node.put("permission", permission);
            node.put("children", buildPermissionTree(getChildrenByParentId(permission.getId())));
            return node;
        }).collect(Collectors.toList());
    }

    /**
     * 查询权限详情
     */
    public SysPermissionWithResourceVo getInfo(Long id) {
        SysPermissionWithResourceVo info = sysPermissionDao.getInfo(id);
        if (info == null) {
            throw new BusinessException("权限不存在");
        }
        List<SysResourceVo> resources = sysResourceDao.getResourcesByPermissionId(info.getId());
        info.setResources(resources);
        return info;
    }

    public List<SysPermissionVo> selectAll() {
        return sysPermissionDao.selectAll();
    }

    @Transactional
    public void assignResource(SysAssignResourceDto sysAssignResourceDto) {
        sysPermissionResourceDao.deleteByPermissId(sysAssignResourceDto.getId());

        List<Long> ids = sysAssignResourceDto.getResources();
        for (Long resourceId : ids) {
            SysPermissionResourceDto dto = new SysPermissionResourceDto();
            dto.setPermissId(sysAssignResourceDto.getId());
            dto.setResourceId(resourceId);
            sysPermissionResourceDao.insert(dto);
        }

    }
}
