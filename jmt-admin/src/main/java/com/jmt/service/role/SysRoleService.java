package com.jmt.service.role;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.dao.SysRoleDao;
import com.jmt.dao.SysRolePermissionDao;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.admin.role.SysRole;
import com.jmt.model.admin.role.SysRolePermission;
import com.jmt.model.admin.role.SysRolePermissionDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SysRoleService extends BaseService {

    @Resource
    private SysRoleDao sysRoleDao;
    @Resource
    private SysRolePermissionDao sysRolePermissionDao;

    /**
     * 更新角色信息
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer updateRole(SysRole sysRole) {
        if (sysRole == null || sysRole.getId() == null) {
            throw new IllegalArgumentException("角色信息不完整");
        }
        setUpdateFields(sysRole);
        return sysRoleDao.updateByPrimaryKeySelective(sysRole);
    }

    /**
     * 角色下拉框数据（仅返回id和name）
     */
    public List<Map<String, Object>> getRoleSelectOptions() {
        List<SysRole> roles = sysRoleDao.selectAllAvailable();
        if (roles == null || roles.isEmpty()) {
            return Collections.emptyList();
        }
        return roles.stream()
                .map(role -> {
                    Map<String, Object> option = new HashMap<>();
                    option.put("id", role.getId());
                    option.put("name", role.getRoleName());
                    return option;
                })
                .collect(Collectors.toList());
    }

    /**
     * 逻辑删除角色
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteRole(SysRole sysRole) {
        if (sysRole == null || sysRole.getId() == null) {
            throw new IllegalArgumentException("角色信息不完整");
        }
        setDeleteFields(sysRole);
        return sysRoleDao.updateByPrimaryKeySelective(sysRole);
    }

    /**
     * 添加角色
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addRole(SysRole sysRole) {
        if (sysRole == null) {
            throw new IllegalArgumentException("角色信息不能为空");
        }
        setCreateFields(sysRole);
        sysRole.setId(null); // 确保ID自增
        sysRoleDao.insertSelective(sysRole);
        return sysRole.getId();
    }

    /**
     * 新增角色-权限关联
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer addRolePermission(SysRolePermissionDto dto) {
        SysRolePermission rolePermission = convertToRolePermission(dto);
        setCreateFields(rolePermission);
        return sysRolePermissionDao.insertSelective(rolePermission);
    }

    /**
     * 删除角色-权限关联
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteRolePermission(SysRolePermissionDto dto) {
        if (dto == null || dto.getRoleId() == null || dto.getPermissId() == null) {
            throw new IllegalArgumentException("角色权限信息不完整");
        }
        SysRolePermission rolePermission = convertToRolePermission(dto);
        setDeleteFields(rolePermission);
        return sysRolePermissionDao.updateByPrimaryKeySelective(rolePermission);
    }

    /**
     * 更新角色-权限关联
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer updateRolePermission(SysRolePermissionDto dto) {
        if (dto == null || dto.getId() == null) {
            throw new IllegalArgumentException("角色权限信息不完整");
        }
        SysRolePermission rolePermission = convertToRolePermission(dto);
        setUpdateFields(rolePermission);
        return sysRolePermissionDao.updateByPrimaryKeySelective(rolePermission);
    }

    /**
     * DTO -> 角色权限实体
     */
    private SysRolePermission convertToRolePermission(SysRolePermissionDto dto) {
        SysRolePermission rolePermission = new SysRolePermission();
        rolePermission.setId(dto.getId());
        rolePermission.setRoleId(dto.getRoleId());
        rolePermission.setPermissId(dto.getPermissId());
        return rolePermission;
    }

    /**
     * 设置通用字段 - 创建
     */
    private void setCreateFields(SysRole sysRole) {
        Date now = new Date();
        sysRole.setCreateTime(now);
        sysRole.setUpdateTime(now);
        sysRole.setIsDelete(0);
    }

    private void setCreateFields(SysRolePermission rolePermission) {
        Date now = new Date();
        rolePermission.setCreateTime(now);
        rolePermission.setUpdateTime(now);
        rolePermission.setIsDelete(0);
    }

    /**
     * 设置通用字段 - 更新
     */
    private void setUpdateFields(SysRole sysRole) {
        sysRole.setUpdateTime(new Date());
    }

    private void setUpdateFields(SysRolePermission rolePermission) {
        rolePermission.setUpdateTime(new Date());
        rolePermission.setIsDelete(0);
    }

    /**
     * 设置通用字段 - 删除
     */
    private void setDeleteFields(SysRole sysRole) {
        sysRole.setUpdateTime(new Date());
        sysRole.setIsDelete(1);
    }

    private void setDeleteFields(SysRolePermission rolePermission) {
        rolePermission.setUpdateTime(new Date());
        rolePermission.setIsDelete(1);
    }

    /**
     * 角色分页查询
     */
    public PageResult<SysRole> getPage(PageQuery<SysRole> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        SysRole query = pageQuery.getQueryData();
        List<SysRole> list = sysRoleDao.getPage(query);
        return new PageResult<>(list);
    }

    /**
     * 查询角色详情
     */
    public SysRole getRoleInfo(Long roleId) {
        if (roleId == null) {
            throw new IllegalArgumentException("角色ID不能为空");
        }
        SysRole role = sysRoleDao.selectByPrimaryKey(roleId);
        if (role == null || role.getIsDelete() == 1) {
            throw new RuntimeException("角色不存在或已被删除");
        }
        return role;
    }

    /**
     * 启用/禁用角色
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer enableRole(Long roleId, Integer status) {
        if (roleId == null) {
            throw new IllegalArgumentException("角色ID不能为空");
        }
        if (status == null || (status != 0 && status != 1)) {
            throw new IllegalArgumentException("状态值必须为0（禁用）或1（启用）");
        }
        SysRole role = new SysRole();
        role.setId(roleId);
        role.setIsDelete(status == 1 ? 0 : 1);
        role.setUpdateTime(new Date());
        return sysRoleDao.updateByPrimaryKeySelective(role);
    }

    /**
     * 批量逻辑删除角色
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer batchDeleteRoles(List<Long> roleIds) {
        if (roleIds == null || roleIds.isEmpty()) {
            throw new IllegalArgumentException("角色ID列表不能为空");
        }
        Date now = new Date();
        int count = 0;
        for (Long roleId : roleIds) {
            SysRole role = new SysRole();
            role.setId(roleId);
            role.setIsDelete(1);
            role.setUpdateTime(now);
            count += sysRoleDao.updateByPrimaryKeySelective(role);
        }
        return count;
    }

    /**
     * 分配权限（幂等处理）
     */
    @Transactional(rollbackFor = Exception.class)
    public void assignPermissions(Long roleId, List<Long> permissionIds) {
        if (roleId == null) {
            throw new IllegalArgumentException("角色ID不能为空");
        }
        // 清除旧关联
        sysRolePermissionDao.updateIsDeleteByRoleId(roleId, 1);

        if (permissionIds != null && !permissionIds.isEmpty()) {
            List<SysRolePermission> rolePermissions = permissionIds.stream()
                    .distinct()
                    .map(permissId -> {
                        SysRolePermission rp = new SysRolePermission();
                        rp.setRoleId(roleId);
                        rp.setPermissId(permissId);
                        setCreateFields(rp);
                        return rp;
                    })
                    .collect(Collectors.toList());
            sysRolePermissionDao.batchInsert(rolePermissions);
        }
    }
}
