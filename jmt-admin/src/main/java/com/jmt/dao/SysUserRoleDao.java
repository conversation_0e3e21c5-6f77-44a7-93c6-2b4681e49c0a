package com.jmt.dao;

import com.jmt.model.admin.user.SysUserRole;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【sys_user_role(系统用户角色关系表)】的数据库操作Mapper
* @createDate 2025-07-07 14:23:49
* @Entity com.jmt.model.user.SysUserRole
*/
public interface SysUserRoleDao {

    int deleteByPrimaryKey(Long id);

    int insert(SysUserRole record);

    int insertSelective(SysUserRole record);
    List<SysUserRole> selectAllByUserId(Long userId);
    SysUserRole selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SysUserRole record);


    List<Long> findAllByIdLongs(Long id);

    Integer deleteUserRoleByUserId(Long userId);

    Integer deleteUserRoleByUserIdAndRoleId(SysUserRole sysUserRole);

    // 新增：按用户ID批量逻辑删除角色关联
    int batchUpdateIsDeleteByUserId(SysUserRole sysUserRole);

    // 新增：批量插入用户角色关联
    int batchInsert(@Param("list") List<SysUserRole> list);
}
