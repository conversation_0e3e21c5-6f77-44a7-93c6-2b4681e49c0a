package com.jmt.dao;

import com.jmt.model.admin.role.SysRole;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【sys_role(系统角色表)】的数据库操作Mapper
* @createDate 2025-07-07 14:23:49
* @Entity com.jmt.model.role.SysRole
*/
public interface SysRoleDao {

    int deleteByPrimaryKey(Long id);

    int insert(SysRole record);

    int insertSelective(SysRole record);

    SysRole selectByPrimaryKey(Long id);

    Integer updateByPrimaryKeySelective(SysRole record);

    int updateByPrimaryKey(SysRole record);

    List<SysRole> findByRoleIds(@Param("roleIds") List<Long> roleIds);

    List<SysRole> getPage(SysRole sysUser);

    List<SysRole> selectAllAvailable();
}
