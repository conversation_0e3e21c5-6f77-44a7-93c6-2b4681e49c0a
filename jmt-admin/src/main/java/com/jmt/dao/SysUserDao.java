package com.jmt.dao;

import com.jmt.model.admin.user.SysUser;
import com.jmt.model.admin.user.SysUserRoleName;
import com.jmt.model.admin.user.SysUserVO;
import com.jmt.model.auth.UserInfo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface SysUserDao {

    List<SysUserRoleName> getPage(SysUserRoleName sysUser);

    SysUserVO getInfo(Long userId);

    SysUserVO getInfoByUaaId(Long uaaId);

    Long insert(SysUser record);

    Integer insertSelective(SysUser record);

    Integer updateByPrimaryKeySelective(SysUser record);

    List<SysUser> selectComboList(@Param("keyword") String keyword);

    Integer batchUpdateIsDelete(@Param("userIds") List<Long> userIds, @Param("isDelete") Integer isDelete, @Param("updateTime") Date updateTime);

    Integer changeStatus(@Param("id") Long id, @Param("userStatus") Integer userStatus);

    Integer delete(Long id);
}
