package com.jmt.dao;

import com.jmt.model.admin.resource.SysBusinessCategory;

/**
* <AUTHOR>
* @description 针对表【sys_business_category(经营类别)】的数据库操作Mapper
* @createDate 2025-07-07 14:23:49
* @Entity com.jmt.model.Resource.SysBusinessCategory
*/
public interface SysBusinessCategoryDao {

    int deleteByPrimaryKey(Long id);

    int insert(SysBusinessCategory record);

    int insertSelective(SysBusinessCategory record);

    SysBusinessCategory selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SysBusinessCategory record);

    int updateByPrimaryKey(SysBusinessCategory record);

}
