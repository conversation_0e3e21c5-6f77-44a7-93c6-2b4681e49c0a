package com.jmt.dao;

import com.jmt.model.admin.resource.SysResource;
import com.jmt.model.admin.resource.dto.SysResourceDto;
import com.jmt.model.admin.resource.dto.SysResourcePageQueryDto;
import com.jmt.model.admin.resource.vo.SysResourceVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【sys_resource(系统资源表)】的数据库操作Mapper
* @createDate 2025-07-07 14:23:49
* @Entity com.jmt.model.Resource.SysResource
*/
public interface SysResourceDao {

    Integer delete(Long id);

    SysResourceVo getInfo(Long id);

    Integer update(SysResourceDto sysResourceDto);

    List<SysResource> findByResourceIds(@Param("resourceIds") List<Long> resourceIds);

    List<SysResourceVo> getPage(SysResourcePageQueryDto sysResourcePageQueryDto);
    /**
     * 查询所有可用资源（未删除的）
     * @return 资源列表
     */
    List<SysResourceVo> selectAllAvailable();

    List<SysResourceVo> getResourcesByPermissionId(Long id);
}
