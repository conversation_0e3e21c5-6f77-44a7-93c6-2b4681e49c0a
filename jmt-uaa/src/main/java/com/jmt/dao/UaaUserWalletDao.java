package com.jmt.dao;

import com.jmt.model.uaa.UaaUserWallet;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface UaaUserWalletDao {
    Integer insert(UaaUserWallet uaaUserWallet);

    Integer updateById(UaaUserWallet uaaUserWallet);

    Integer deleteById(@Param("id") Long id);

    UaaUserWallet selectByUaaId(@Param("id") Long id);

    Integer updateByUaaId(UaaUserWallet uaaUserWallet);
}
