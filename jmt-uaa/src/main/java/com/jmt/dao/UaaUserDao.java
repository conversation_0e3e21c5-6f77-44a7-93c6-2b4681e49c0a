package com.jmt.dao;

import com.jmt.model.uaa.UaaUser;
import com.jmt.model.uaa.dto.UaaUserDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UaaUserDao {

    Integer insert(UaaUser user);

    Boolean existsByLoginName(String loginName);

    UaaUser existsByTelPhone(String telPhone);

    Integer updateById(UaaUser uaaUser);

    UaaUser selectByUaaId(Long uaaId);

    List<UaaUser> selectByUaaIds(List<Long> uaaIds);

    Integer updatePassword(UaaUser uaaUser);

    UaaUser existsUaa(UaaUserDTO userDTO);

    Integer lock(Long uaaId);

    Integer unlock(Long uaaId);

    Integer deleteById(@Param("id") Long id);
}
