package com.jmt.controller;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.jmt.base.BaseController;
import com.jmt.constant.RedisKeyConstant;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.uaa.dto.ChangePasswordDTO;
import com.jmt.model.uaa.dto.ForgotPasswordSmsDTO;
import com.jmt.model.uaa.dto.UaaUserDTO;
import com.jmt.model.uaa.UaaUser;
import com.jmt.service.UaaUserService;
import com.jmt.util.LoginUserUtil;
import com.jmt.util.RedisUtil;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("/uaa/*")
public class UaaController extends BaseController {

    @Resource
    private UaaUserService uaaUserService;

    @Resource
    private RedisUtil redisUtil;

    /**
     * 新增账户
     * @param userDTO
     * @return
     */
    @PostMapping("/v1/addUaaUser")
    @ResponseBody
    public String addUaaUser(@RequestBody UaaUserDTO userDTO) {
        try {
            UaaUser user = uaaUserService.registerUser(userDTO);
            return super.responseSuccess(user,"用户新增成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 修改账户信息
     * @param uaaUser
     * @return
     */
    @PostMapping("/v1/update")
    @ResponseBody
    public String update(@RequestBody UaaUser uaaUser, HttpServletRequest req) {
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        uaaUser.setUaaId(loginUser.getUaaId());
        uaaUserService.update(uaaUser);
        return super.responseSuccess("修改成功");
    }

    /**
     * 修改账户密码
     * @param dto
     * @return
     */
    @PostMapping("/v1/reset")
    public String changePassword(@RequestBody ChangePasswordDTO dto, HttpServletRequest req) {
        try {
            LoginUaaUser loginUser = LoginUserUtil.get(req);
            dto.setUaaId(loginUser.getUaaId());
            uaaUserService.changePassword(dto);
            return super.responseSuccess("密码修改成功");
        } catch (RuntimeException e) {
            return super.responseFail(e.getMessage());
        }
    }

    @GetMapping("/v1/getByUaaId/{id}")
    public String getUaaByUaaId(@PathVariable("id") Long id) {
        UaaUser user = uaaUserService.getByUaaId(id);
        return super.responseSuccess(user,"查询成功");
    }


    @GetMapping("/v1/getUaaByUaaIds")
    public String getUaaByUaaIds(@RequestParam("ids") List<Long> ids) {
        List<UaaUser> user = uaaUserService.selectByUaaIds(ids);
        return super.responseSuccess(user,"查询成功");
    }



    @GetMapping("/v1/getByPhone/{telPhone}")
    public String existsByTelPhone(@PathVariable("telPhone") String telPhone) {
        UaaUser user = uaaUserService.existsByTelPhone(telPhone);
        return super.responseSuccess(user,"查询成功");
    }

    @PostMapping(value ="/v1/lock")
    @ResponseBody
    String lock(@RequestParam("uaaId") Long uaaId) {
        Integer r = uaaUserService.lock(uaaId);
        if (r == 1) {
            return super.responseSuccess("账号锁定成功");
        } else {
            return super.responseFail("账号锁定失败");
        }
    }

    @PostMapping(value ="/v1/unlock")
    @ResponseBody
    String unlock(@RequestParam("uaaId") Long uaaId) {
        Integer r = uaaUserService.unlock(uaaId);
        if (r == 1) {
            return super.responseSuccess("账号解锁成功");
        } else {
            return super.responseFail("账号解锁失败");
        }
    }

    @PostMapping("/v1/delete/{id}")
    public String deleteUaa(@PathVariable Long id) {
        uaaUserService.deleteUaa(id);
        return super.responseSuccess("删除成功");
    }


    @ResponseBody
    @PostMapping("/sms/resetPassword")
    public String smsResetPassword(@RequestBody @Validated ForgotPasswordSmsDTO forgotPasswordSmsDTO) {
        String telPhone = forgotPasswordSmsDTO.getTelPhone();
        if (StrUtil.isEmpty(telPhone)) {
            return super.responseFailAuth("手机号不能为空");
        }
        String smsCode = forgotPasswordSmsDTO.getSmsCode();
        if (StrUtil.isEmpty(smsCode)) {
            return super.responseFail("短信验证码不能为空");
        }
        Object redisSmsCode = redisUtil.get(RedisKeyConstant.SMS_CACHE_KEY + telPhone);
        redisUtil.delete(RedisKeyConstant.SMS_CACHE_KEY + telPhone);
        if (ObjectUtil.isEmpty(redisSmsCode) || !smsCode.equals(Convert.toStr(redisSmsCode))) {
            return super.responseFailAuth("短信验证码错误");
        }
        uaaUserService.forgetPassword(forgotPasswordSmsDTO);
        return super.responseSuccess("重置成功");

    }




}
