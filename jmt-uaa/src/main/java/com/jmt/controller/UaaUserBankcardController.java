package com.jmt.controller;

import com.jmt.base.BaseController;
import com.jmt.model.auth.LoginUaaUser;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;

import com.jmt.model.uaa.UaaUserBankcard;
import com.jmt.service.UaaUserBankcardService;
import com.jmt.util.LoginUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/uaa/bankcard")
public class UaaUserBankcardController extends BaseController {

    @Resource
    private UaaUserBankcardService uaaUserBankcardService;

    /**
     * 分页查询
     * @param pageQuery
     * @return
     */
    @PostMapping("/v1/getPageList")
    public String getPageList(@RequestBody(required = false) PageQuery<UaaUserBankcard> pageQuery, HttpServletRequest req){
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
            // 设置默认页码和页大小，根据你的业务需求调整
            pageQuery.setPageNo(1);
            pageQuery.setPageSize(10);
        }
        UaaUserBankcard uaaUserBankcard=new UaaUserBankcard();
        uaaUserBankcard.setUaaId(LoginUserUtil.get(req).getUaaId());
        pageQuery.setQueryData(uaaUserBankcard);
        PageResult<UaaUserBankcard> pageResult = uaaUserBankcardService.getPage(pageQuery);
        return super.responseSuccess(pageResult,"查询成功");
    }


    /**
     * 新增银行卡
     * @param uaaUserBankcard
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/v1/add")
    public String add(@RequestBody UaaUserBankcard uaaUserBankcard, HttpServletRequest req) {
        try {
            LoginUaaUser loginUser = LoginUserUtil.get(req);
            uaaUserBankcard.setUaaId(loginUser.getUaaId());
            uaaUserBankcardService.createBankcard(uaaUserBankcard);
            return super.responseSuccess("银行卡添加成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }

    /**
     * 修改银行卡信息
     * @param uaaUserBankcard
     * @return
     */
    @PostMapping(value ="/v1/update")
    public String update(@RequestBody UaaUserBankcard uaaUserBankcard) {
        uaaUserBankcardService.updateBankcard(uaaUserBankcard);
        return super.responseSuccess("修改成功");
    }

    /**
     * 删除银行卡
     * @param id
     * @return
     */
    @PostMapping("/v1/delete/{id}")
    public String delete(@PathVariable Long id) {
        uaaUserBankcardService.deleteBankcard(id);
        return super.responseSuccess("删除成功");
    }

    /**
     * 根据id查询银行卡信息
     * @param id
     * @return
     */
    @ResponseBody
    @GetMapping("/v1/{id}")
    public String getById(@PathVariable Long id) {
        UaaUserBankcard uaaUserBankcard = uaaUserBankcardService.getBankcardById(id);
        if(uaaUserBankcard == null){
            return super.responseFail("不存在该银行卡");
        }
        return super.responseSuccess(uaaUserBankcard,"查询成功");
    }

    /**
     * 根据uaaid查询银行卡信息
     * @return
     */
    @ResponseBody
    @GetMapping("/v1/list")
    public String getByUaaId(HttpServletRequest  req) {
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        List<UaaUserBankcard> uaaUserBankcard = uaaUserBankcardService.getBankcardsByUaaId(loginUser.getUaaId());
        return super.responseSuccess(uaaUserBankcard,"查询成功");
    }

    @GetMapping("/v1/MyDefaultCard")
    public String getMyDefaultCard(HttpServletRequest  req) {
        LoginUaaUser loginUser = LoginUserUtil.get(req);
        UaaUserBankcard uaaUserBankcard = uaaUserBankcardService.getMyDefaultCard(loginUser.getUaaId());
        return super.responseSuccess(uaaUserBankcard,"查询成功");
    }

    /**
     * 设置默认银行卡
     * @param bankcardId
     * @param req
     * @return
     */
    @PostMapping("/v1/setDefault")
    public String setDefaultBankcard(@RequestParam Long bankcardId, HttpServletRequest req) {
        try {
            LoginUaaUser loginUser = LoginUserUtil.get(req);
            uaaUserBankcardService.setDefaultBankcard(loginUser.getUaaId(), bankcardId);
            return super.responseSuccess("默认银行卡设置成功");
        } catch (Exception e) {
            return super.responseFail(e.getMessage());
        }
    }
    /**
     * 根据uaaId查询默认地址
     * @param id
     * @return
     */
    @GetMapping("/v1/default/{id}")
    public String getDefaultBankcard(@PathVariable("id") Long id) {
        UaaUserBankcard bankcard = uaaUserBankcardService.getDefaultBankcardByUaaId(id);
        if(bankcard == null){
            return super.responseFail("不存在默认银行卡");
        }
        return super.responseSuccess(bankcard,"查询成功");
    }

}
