package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.dao.UaaUserBankcardDao;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.uaa.UaaUserBankcard;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class UaaUserBankcardService extends BaseService {
    @Resource
    private UaaUserBankcardDao uaaUserBankcardDao;

    @Transactional
    public Integer createBankcard(UaaUserBankcard bankcard) {
        if (bankcard.getIsDefault() != null && bankcard.getIsDefault() == 1) {
            cancelOtherDefaultCards(bankcard.getUaaId());
        } else {
            // 如果这是用户的第一张卡，自动设为默认
            int cardCount = uaaUserBankcardDao.countByUaaId(bankcard.getUaaId());
            if (cardCount == 0) {
                bankcard.setIsDefault(1);
            } else {
                bankcard.setIsDefault(0);
            }
        }
        Date now = new Date();
        bankcard.setCreateTime(now);
        bankcard.setUpdateTime(now);
        bankcard.setIsDelete(0);
        return uaaUserBankcardDao.insert(bankcard);
    }

    @Transactional(rollbackFor = Exception.class)
    public void setDefaultBankcard(Long uaaId, Long bankcardId) {
        UaaUserBankcard bankcard = uaaUserBankcardDao.selectById(bankcardId);
        if (bankcard == null || !bankcard.getUaaId().equals(uaaId)) {
            throw new RuntimeException("银行卡不存在或不属于该用户");
        }

        if (bankcard.getIsDefault() == 1) {
            return;
        }
        cancelOtherDefaultCards(uaaId);
        bankcard.setIsDefault(1);
        bankcard.setUpdateTime(new Date());
        uaaUserBankcardDao.updateById(bankcard);
    }

    /**
     * 取消用户的其他默认银行卡
     */
    private void cancelOtherDefaultCards(Long uaaId) {
        UaaUserBankcard currentDefault = uaaUserBankcardDao.findDefaultByUaaId(uaaId);
        if (currentDefault != null) {
            currentDefault.setIsDefault(0);
            currentDefault.setUpdateTime(new Date());
            uaaUserBankcardDao.updateById(currentDefault);
        }
    }

    @Transactional
    public Integer updateBankcard(UaaUserBankcard uaaUserBankcard) {
        uaaUserBankcard.setUpdateTime(new Date());
        return uaaUserBankcardDao.updateById(uaaUserBankcard);
    }

    @Transactional
    public void deleteBankcard(Long id) {
        uaaUserBankcardDao.deleteById(id);
    }

    public UaaUserBankcard getBankcardById(Long id) {
        return uaaUserBankcardDao.selectById(id);
    }

    public List<UaaUserBankcard> getBankcardsByUaaId(Long uaaId) {
        return uaaUserBankcardDao.selectByUaaId(uaaId);
    }

    public PageResult<UaaUserBankcard> getPage(PageQuery<UaaUserBankcard> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<UaaUserBankcard> list = uaaUserBankcardDao.selectByUaaId(pageQuery.getQueryData().getUaaId());
        return new PageResult<>(list);

    }

    public UaaUserBankcard getMyDefaultCard(Long uaaId) {
        return uaaUserBankcardDao.selectDefaultCardByUaaId(uaaId);
    }

    public UaaUserBankcard getDefaultBankcardByUaaId(Long id) {
        return uaaUserBankcardDao.findDefaultByUaaId(id);
    }
}
