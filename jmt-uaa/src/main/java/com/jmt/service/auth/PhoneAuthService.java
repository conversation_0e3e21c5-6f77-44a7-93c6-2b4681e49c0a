package com.jmt.service.auth;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.jmt.base.BaseService;
import com.jmt.constant.JmtConstant;
import com.jmt.constant.RedisKeyConstant;
import com.jmt.model.auth.*;
import com.jmt.model.cm.vo.CmUserNoVo;
import com.jmt.model.jhh.vo.JhhUserNoVo;
import com.jmt.model.uaa.dto.UaaUserDTO;
import com.jmt.util.*;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Service
public class PhoneAuthService extends BaseService {

    private static final String GET_PHONE_URL = "http://uni.getphonenumber.jmingt.com/loginByPhoneNumber";
    private static final String SIGN = "cGtHbVjRHmFMQszYhINhGPzJulVvuJUc";//暂时将密钥做签名

    @Resource
    private UaaAuthService uaaAuthService;

    @Resource
    private RedisUtil redisUtil;



    public String phoneAuthLogin(PhoneAuthLoginDto loginDto) {
        Map<String, Object> telPhoneMap;
        String telPhone = null;
        try {
            telPhoneMap = this.getLocalTelPhone(loginDto);
            logger.info("本机号码登录获取手机号结果:"+GsonUtil.toJson(telPhoneMap));
            Integer errCode= Convert.toInt(telPhoneMap.get("errCode"));
            String errMsg = Convert.toStr(telPhoneMap.get("errMsg"));
            if (errCode > 0 && StrUtil.isNotEmpty(errMsg)){
                logger.error("本机号码登录失败:"+errMsg);
                return super.responseFailAuth(errMsg);
            }
            telPhone = Convert.toStr(telPhoneMap.get("phoneNumber"));
        } catch (Exception e) {
            logger.error("本机号码登录异常:",e);
        }
        String token = null;
        if (StrUtil.isNotEmpty(telPhone)) {
            LoginUaaUser userDetails = uaaAuthService.loadUserByUsername(telPhone);
            if (ObjectUtil.isEmpty(userDetails.getUaaId())) {
                //如果账号不存在就注册一个账号
                try {
                    UaaUserDTO uaaUserDTO = new UaaUserDTO();
                    uaaUserDTO.setLoginName(telPhone);
                    uaaUserDTO.setTelPhone(telPhone);
                    uaaUserDTO.setNickname(JmtConstant.DEFAULT_NICKNAME+telPhone);
                    uaaUserDTO.setPassword(JmtConstant.DEFAULT_PASSWORD);
                    userDetails = uaaAuthService.registerUaaUser(uaaUserDTO);
                } catch (Exception e) {
                    logger.error("本机号码登录失败",e);
                    return super.responseFailAuth("本机号码登录失败");
                }
            }
            if(!userDetails.isEnabled()){
                return super.responseFailAuth("账号已禁用,请联系管理员");
            }
            Long uaaId = userDetails.getUaaId();

            JhhUserNoVo jhhUserNoVo = uaaAuthService.getJhhUserNo(uaaId);
            if (jhhUserNoVo != null) {
                userDetails.setOperatorNo(jhhUserNoVo.getOperatorNo());
                userDetails.setInvestorNo(jhhUserNoVo.getInvestorNo());
                userDetails.setSalesmanNo(jhhUserNoVo.getSalesmanNo());
            }

            CmUserNoVo cmUserNoVo = uaaAuthService.getCmUserNo(uaaId);
            if (cmUserNoVo != null) {
                userDetails.setBusNo(cmUserNoVo.getBusNo());
                userDetails.setShopNo(cmUserNoVo.getShopNo());
            }

            userDetails.setUserType(loginDto.getUserType());//添加登录者的身份
            userDetails.setClientId(loginDto.getClientId());//添加登录客户端ID
            UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
            SecurityContextHolder.getContext().setAuthentication(authentication);
            token = JwtTokenUtil.createToken(BeanUtil.beanToMap(userDetails),userDetails.getLoginName());
            redisUtil.set(RedisKeyConstant.TOKEN_CACHE_KEY + userDetails.getUaaId(),token,RedisKeyConstant.TOKEN_CACHE_EXPIRE);
        }
        return responseSuccessAuth(token);
    }

    private Map<String, Object> getLocalTelPhone(PhoneAuthLoginDto loginDto) throws Exception {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("openid", loginDto.getOpenId());
        paramMap.put("access_token", loginDto.getAccessToken());
        paramMap.put("sign", SIGN);
        return HttpsUtil.httpsMap(GET_PHONE_URL,"POST",GsonUtil.toJson(paramMap),6000);
    }
}
