package com.jmt.service.auth;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.jmt.base.BaseService;
import com.jmt.client.*;
import com.jmt.constant.JmtConstant;
import com.jmt.constant.RedisKeyConstant;
import com.jmt.model.auth.*;
import com.jmt.model.cm.vo.CmUserNoVo;
import com.jmt.model.jhh.vo.JhhUserNoVo;
import com.jmt.model.uaa.dto.UaaUserDTO;
import com.jmt.util.JwtTokenUtil;
import com.jmt.util.RedisUtil;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class SmsCodeAuthService extends BaseService {

    @Resource
    private UaaAuthService uaaAuthService;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private CmUserFeignClient cmUserFeignClient;

    public String smsCodeAuthLogin(SmsCodeAuthLoginDto loginDto) {
        String token = null;
        //密码需要客户端加密后传递
        try {
            LoginUaaUser userDetails = uaaAuthService.loadUserByUsername(loginDto.getTelPhone());
            if (ObjectUtil.isEmpty(userDetails.getUaaId())) {
                if(loginDto.getClientId().equals(JmtConstant.CLIENT_ID_JMT_CM)){
                    UaaUserDTO uaaUserDTO = new UaaUserDTO();
                    uaaUserDTO.setLoginName(loginDto.getTelPhone());
                    uaaUserDTO.setTelPhone(loginDto.getTelPhone());
                    uaaUserDTO.setPassword(JmtConstant.DEFAULT_PASSWORD);
                    cmUserFeignClient.add(uaaUserDTO);
                    userDetails = uaaAuthService.loadUserByUsername(loginDto.getTelPhone());
                } else {
                    logger.error("手机验证码登录失败：账号不存在");
                    return super.responseFailAuth("账号或密码错误");
                }
            }
            if(!userDetails.isEnabled()){
                logger.error("手机验证码登录失败：账号已禁用,请联系管理员");
                return super.responseFailAuth("账号已禁用,请联系管理员");
            }
            Long uaaId = userDetails.getUaaId();

            JhhUserNoVo jhhUserNoVo = uaaAuthService.getJhhUserNo(uaaId);
            if (jhhUserNoVo != null) {
                userDetails.setOperatorNo(jhhUserNoVo.getOperatorNo());
                userDetails.setInvestorNo(jhhUserNoVo.getInvestorNo());
                userDetails.setSalesmanNo(jhhUserNoVo.getSalesmanNo());
            }

            CmUserNoVo cmUserNoVo = uaaAuthService.getCmUserNo(uaaId);
            if (cmUserNoVo != null) {
                userDetails.setBusNo(cmUserNoVo.getBusNo());
                userDetails.setShopNo(cmUserNoVo.getShopNo());
            }
            userDetails.setUserType(loginDto.getUserType());//添加登录者的身份
            userDetails.setClientId(loginDto.getClientId());//添加登录客户端ID
            UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
            SecurityContextHolder.getContext().setAuthentication(authentication);
            token = JwtTokenUtil.createToken(BeanUtil.beanToMap(userDetails),userDetails.getLoginName());
            redisUtil.set(RedisKeyConstant.TOKEN_CACHE_KEY + userDetails.getUaaId(),token,RedisKeyConstant.TOKEN_CACHE_EXPIRE);
        } catch (AuthenticationException e) {
            logger.error("手机验证码登录异常:", e);
        }
        return responseSuccessAuth(token);
    }
}
