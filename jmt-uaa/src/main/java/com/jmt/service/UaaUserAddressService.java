package com.jmt.service;

import com.github.pagehelper.PageHelper;
import com.jmt.base.BaseService;
import com.jmt.dao.UaaUserAddressDao;
import com.jmt.model.page.PageQuery;
import com.jmt.model.page.PageResult;
import com.jmt.model.uaa.UaaUserAddress;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class UaaUserAddressService extends BaseService {
    @Resource
    private UaaUserAddressDao uaaUserAddressDao;

    @Transactional
    public Integer createAddress(UaaUserAddress uaaUserAddress) {
        if (uaaUserAddress.getIsDefault() != null && uaaUserAddress.getIsDefault() == 1) {
            cancelOtherDefaultCards(uaaUserAddress.getUaaId());
        } else {
            // 如果这是用户的第一张卡，自动设为默认
            int cardCount = uaaUserAddressDao.countByUaaId(uaaUserAddress.getUaaId());
            if (cardCount == 0) {
                uaaUserAddress.setIsDefault(1);
            } else {
                uaaUserAddress.setIsDefault(0);
            }
        }
        Date now = new Date();
        uaaUserAddress.setCreateTime(now);
        uaaUserAddress.setUpdateTime(now);
        uaaUserAddress.setIsDelete(0);
        return uaaUserAddressDao.insert(uaaUserAddress);
    }

    @Transactional(rollbackFor = Exception.class)
    public void setDefaultAddress(Long uaaId, Long addressId) {
        UaaUserAddress uaaUserAddress = uaaUserAddressDao.selectById(addressId);
        if (uaaUserAddress == null || !uaaUserAddress.getUaaId().equals(uaaId)) {
            throw new RuntimeException("地址不存在或不属于该用户");
        }

        if (uaaUserAddress.getIsDefault() == 1) {
            return;
        }
        cancelOtherDefaultCards(uaaId);
        uaaUserAddress.setIsDefault(1);
        uaaUserAddress.setUpdateTime(new Date());
        uaaUserAddressDao.updateById(uaaUserAddress);
    }

    /**
     * 取消用户的其他默认地址
     */
    private void cancelOtherDefaultCards(Long uaaId) {
        UaaUserAddress uaaUserAddress = uaaUserAddressDao.findDefaultByUaaId(uaaId);
        if (uaaUserAddress != null) {
            uaaUserAddress.setIsDefault(0);
            uaaUserAddress.setUpdateTime(new Date());
            uaaUserAddressDao.updateById(uaaUserAddress);
        }
    }

    @Transactional
    public Integer updateAddress(UaaUserAddress uaaUserAddress) {
        uaaUserAddress.setUpdateTime(new Date());
        return uaaUserAddressDao.updateById(uaaUserAddress);
    }

    @Transactional
    public void deleteAddress(Long id) {
        uaaUserAddressDao.deleteById(id);
    }


    public UaaUserAddress getAddressById(Long id) {
        return uaaUserAddressDao.selectById(id);
    }

    @Transactional
    public List<UaaUserAddress> getAddressByIds(List<Long> ids) {
        return uaaUserAddressDao.selectByIds(ids);
    }



    public List<UaaUserAddress> getAddressesByUaaId(Long uaaId) {
        return uaaUserAddressDao.selectByUaaId(uaaId);
    }

    public PageResult<UaaUserAddress> getPage(PageQuery<UaaUserAddress> pageQuery) {
        PageHelper.startPage(pageQuery.getPageNo(), pageQuery.getPageSize());
        List<UaaUserAddress> list = uaaUserAddressDao.selectByUaaId(pageQuery.getQueryData().getUaaId());
        return new PageResult<>(list);

    }

    public UaaUserAddress getDefaultAddressByUaaId(Long id) {
        return uaaUserAddressDao.findDefaultByUaaId(id);
    }
}
