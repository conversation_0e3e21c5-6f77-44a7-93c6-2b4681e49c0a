package com.jmt.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.druid.util.StringUtils;
import com.jmt.base.BaseService;
import com.jmt.constant.JmtConstant;
import com.jmt.convertor.UaaUserConvertor;
import com.jmt.dao.UaaUserDao;
import com.jmt.model.uaa.dto.ChangePasswordDTO;
import com.jmt.model.uaa.dto.ForgotPasswordSmsDTO;
import com.jmt.model.uaa.dto.UaaUserDTO;
import com.jmt.model.uaa.UaaUser;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class UaaUserService extends BaseService {

    @Resource
    private UaaUserDao uaaUserDao;

    @Resource
    private UaaUserConvertor uaaUserConvertor;

    @Resource
    private UaaUserWalletService uaaUserWalletService;

    @Resource
    private PasswordEncoder passwordEncoder;

    @Transactional
    public UaaUser registerUser(UaaUserDTO userDTO) {

        // 检查是否已存在账号
        UaaUser existingUser = uaaUserDao.existsUaa(userDTO);
        if (existingUser != null) {
            return existingUser;
        }

        com.jmt.model.uaa.UaaUser user = uaaUserConvertor.toEntity(userDTO);
        if(userDTO.getPassword() != null){
            user.setPassword(passwordEncoder.encode(userDTO.getLoginName() + userDTO.getPassword()));
        }
        String nickName = userDTO.getLoginName();
        if (StrUtil.isEmpty(nickName)) {
            nickName = JmtConstant.DEFAULT_NICKNAME + userDTO.getTelPhone();
        }
        user.setNickname(nickName);
        user.setActivated(0);
        user.setIsDelete(0);
        user.setCreateTime(new Date());
        user.setUpdateTime(new Date());
        Integer r = uaaUserDao.insert(user);
        if (r==1) {
            uaaUserWalletService.createWallet(user.getUaaId());
        }
        return user;
    }


    public UaaUser existsByTelPhone(String telPhone) {
        return uaaUserDao.existsByTelPhone(telPhone);
    }

    @Transactional
    public Integer update(UaaUser uaaUser) {
        uaaUser.setUpdateTime(new Date());
        return uaaUserDao.updateById(uaaUser);
    }

    /**
     * 修改密码
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void changePassword(ChangePasswordDTO dto) {
        UaaUser user = uaaUserDao.selectByUaaId(dto.getUaaId());
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 原密码是否正确
        if (StringUtils.isEmpty(user.getPassword())
                || !user.getPassword().equals(passwordEncoder.encode(user.getLoginName() + dto.getOldPassword()))) {
            throw new RuntimeException("原密码不正确");
        }

        // 新密码不能与旧密码相同
        if (user.getPassword().equals(passwordEncoder.encode(user.getLoginName() + dto.getNewPassword()))) {
            throw new RuntimeException("新密码不能与旧密码相同");
        }

        String encodedPassword = passwordEncoder.encode(user.getLoginName() + dto.getNewPassword());
        user.setPassword(encodedPassword);
        user.setUpdateTime(new Date());

        int updated = uaaUserDao.updatePassword(user);
        if (updated <= 0) {
            throw new RuntimeException("密码更新失败");
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void forgetPassword(ForgotPasswordSmsDTO forgotPasswordSmsDTO) {
        String newPassword = forgotPasswordSmsDTO.getNewPassword();
        String confirmPassword = forgotPasswordSmsDTO.getConfirmPassword();

        // 验证两次输入的密码是否一致
        if (!newPassword.equals(confirmPassword)) {
            throw new RuntimeException("两次输入的密码不一致");
        }

        //根据手机号判断用户是否存在
        UaaUser uaaUser = uaaUserDao.existsByTelPhone(forgotPasswordSmsDTO.getTelPhone());
        if (uaaUser == null) {
            throw new RuntimeException("手机号未注册");
        }

        try {
            // 更新密码
            String encodedPassword = passwordEncoder.encode(uaaUser.getLoginName() + forgotPasswordSmsDTO.getNewPassword());
            uaaUser.setPassword(encodedPassword);
            uaaUser.setUpdateTime(new Date());

            int updated = uaaUserDao.updatePassword(uaaUser);
            if (updated <= 0) {
                throw new RuntimeException("密码重置失败");
            }
        } catch (Exception e) {
            throw new RuntimeException("密码重置失败", e);
        }

    }

    public UaaUser getByUaaId(Long uaaId) {
        return uaaUserDao.selectByUaaId(uaaId);
    }

    public List<UaaUser> selectByUaaIds(List<Long> uaaIds) {
        return uaaUserDao.selectByUaaIds(uaaIds);
    }



    public Integer lock(Long uaaId) {
        return uaaUserDao.lock(uaaId);
    }

    public Integer unlock(Long uaaId) {
        return uaaUserDao.unlock(uaaId);
    }

    @Transactional
    public void deleteUaa(Long id) {
        uaaUserDao.deleteById(id);
    }
}
