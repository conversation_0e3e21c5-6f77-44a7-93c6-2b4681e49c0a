package com.jmt.service;

import com.jmt.base.BaseService;
import com.jmt.dao.UaaUserWalletDao;
import com.jmt.model.uaa.dto.ChangePayPasswordDTO;
import com.jmt.model.uaa.UaaUserWallet;
import org.apache.commons.lang.StringUtils;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

@Service
public class UaaUserWalletService extends BaseService {

    @Resource
    private UaaUserWalletDao uaaUserWalletDao;

    @Resource
    private PasswordEncoder passwordEncoder;

    @Transactional
    public Integer createWallet(Long uaaId) {
        UaaUserWallet uaaUserWallet = new UaaUserWallet();
        Date now = new Date();
        uaaUserWallet.setUaaId(uaaId);
        uaaUserWallet.setCreateTime(now);
        uaaUserWallet.setUpdateTime(now);
        uaaUserWallet.setIsDelete(0);
        uaaUserWallet.setBalance(BigDecimal.ZERO);
        uaaUserWallet.setPoints(0);
        return uaaUserWalletDao.insert(uaaUserWallet);
    }
    @Transactional
    public Integer updateWallet(UaaUserWallet uaaUserWallet) {
        uaaUserWallet.setUpdateTime(new Date());
        return uaaUserWalletDao.updateById(uaaUserWallet);
    }


    @Transactional
    public void deleteWallet(Long id) {
        uaaUserWalletDao.deleteById(id);
    }

    public UaaUserWallet getWalletById(Long uaaId) {
        return uaaUserWalletDao.selectByUaaId(uaaId);
    }

    /**
     * 更新余额接口
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBalance(Long uaaId, BigDecimal amount, boolean isIncrease) {
        if (uaaId == null || amount == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        UaaUserWallet wallet = uaaUserWalletDao.selectByUaaId(uaaId);
        if (wallet == null) {
            throw new RuntimeException("钱包记录不存在");
        }
        BigDecimal newBalance = isIncrease
                ? wallet.getBalance().add(amount)
                : wallet.getBalance().subtract(amount);

        // 校验余额是否足够（如果是扣减操作）
        if (!isIncrease && newBalance.compareTo(BigDecimal.ZERO) < 0) {
            throw new RuntimeException("余额不足");
        }
        wallet.setBalance(newBalance);
        wallet.setUpdateTime(new Date());
        int updated = uaaUserWalletDao.updateByUaaId(wallet);
        if (updated <= 0) {
            throw new RuntimeException("更新余额失败");
        }
    }

    /**
     * 更新积分接口
     */
    @Transactional(rollbackFor = Exception.class)
    public void updatePoints(Long uaaId, Integer points, boolean isIncrease) {
        if (uaaId == null || points == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        UaaUserWallet wallet = uaaUserWalletDao.selectByUaaId(uaaId);
        if (wallet == null) {
            throw new RuntimeException("钱包记录不存在");
        }
        Integer newPoints = isIncrease
                ? wallet.getPoints() + points
                : wallet.getPoints() - points;
        // 校验积分是否足够（如果是扣减操作）
        if (!isIncrease && newPoints < 0) {
            throw new RuntimeException("积分不足");
        }
        wallet.setPoints(newPoints);
        wallet.setUpdateTime(new Date());
        int updated = uaaUserWalletDao.updateByUaaId(wallet);
        if (updated <= 0) {
            throw new RuntimeException("更新积分失败");
        }
    }

    /**
     * 修改支付密码
     */
    @Transactional(rollbackFor = Exception.class)
    public void changePayPassword(Long uaaId, ChangePayPasswordDTO dto) {

        // 查询钱包信息
        UaaUserWallet wallet = uaaUserWalletDao.selectByUaaId(uaaId);
        if (wallet == null) {
            throw new RuntimeException("钱包不存在");
        }

        //  验证原支付密码是否正确
        if (StringUtils.isEmpty(wallet.getPayPassword())) {
            throw new RuntimeException("请先设置支付密码");
        }

        if (!wallet.getPayPassword().equals(passwordEncoder.encode(uaaId + dto.getOldPayPassword()))) {
            throw new RuntimeException("原支付密码不正确");
        }

        if (wallet.getPayPassword().equals(passwordEncoder.encode(uaaId + dto.getNewPayPassword()))) {
            throw new RuntimeException("新支付密码不能与旧密码相同");
        }

        String encodedPassword =passwordEncoder.encode(uaaId + dto.getNewPayPassword());
        wallet.setPayPassword(encodedPassword);
        wallet.setUpdateTime(new Date());

        int updated = uaaUserWalletDao.updateById(wallet);
        if (updated <= 0) {
            throw new RuntimeException("支付密码更新失败");
        }
    }

    /**
     * 校验支付密码
     * @param uaaId
     * @param payPassword
     * @return
     */
    public Boolean validatePayPassword(Long uaaId, String payPassword) {
        UaaUserWallet uaaUserWallet = uaaUserWalletDao.selectByUaaId(uaaId);
        String password=passwordEncoder.encode(uaaId + payPassword);
        if (!password.equals( uaaUserWallet.getPayPassword())) {
            return false;
        }
        return true;
    }

    /**
     * 检查是否已设置支付密码
     */
    public boolean hasSetPayPassword(Long uaaId) {
        UaaUserWallet wallet = uaaUserWalletDao.selectByUaaId(uaaId);
        if (wallet == null) {
            return false;
        }
        return StringUtils.isNotBlank(wallet.getPayPassword());
    }

    /**
     * 初始化支付密码（初次设置）
     */
    @Transactional(rollbackFor = Exception.class)
    public void initPayPassword(Long uaaId, String newPayPassword) {
        UaaUserWallet wallet = uaaUserWalletDao.selectByUaaId(uaaId);
        if (wallet == null) {
            throw new RuntimeException("钱包不存在");
        }

        // 检查是否已经设置过支付密码
        if (!StringUtils.isEmpty(wallet.getPayPassword())) {
            throw new RuntimeException("支付密码已设置，请使用修改功能");
        }

        // 加密新密码
        String encodedPassword = passwordEncoder.encode(uaaId + newPayPassword);
        wallet.setPayPassword(encodedPassword);
        wallet.setUpdateTime(new Date());

        int updated = uaaUserWalletDao.updateById(wallet);
        if (updated <= 0) {
            throw new RuntimeException("支付密码设置失败");
        }
    }


}
