package com.jmt.convertor;

import com.jmt.model.uaa.dto.UaaUserDTO;
import com.jmt.model.uaa.UaaUser;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface UaaUserConvertor {
    // 实体类 -> DTO
    UaaUserDTO toDto(UaaUser uaaUser);

    // DTO -> 实体类
    UaaUser toEntity(UaaUserDTO dto);

    // 列表转换
    List<UaaUserDTO> toDtoList(List<UaaUser> list);

    List<UaaUser> toEntityList(List<UaaUserDTO> dtoList);

}
