package com.jmt.convertor;

import com.jmt.model.uaa.dto.UaaUserWalletDTO;
import com.jmt.model.uaa.UaaUserWallet;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface UaaUserWalletConvertor {
    // 实体类 -> DTO
    UaaUserWalletDTO toDto(UaaUserWallet uaaUserWallet);

    // DTO -> 实体类
    UaaUserWallet toEntity(UaaUserWalletDTO dto);

    // 列表转换
    List<UaaUserWalletDTO> toDtoList(List<UaaUserWallet> list);

    List<UaaUserWallet> toEntityList(List<UaaUserWalletDTO> dtoList);

}
