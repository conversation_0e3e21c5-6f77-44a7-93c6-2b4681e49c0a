<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace名字必须与Mapper接口名一致 -->

<mapper namespace="com.jmt.dao.UaaAuthDao">
	<select id="loadUserByUsername" parameterType="java.lang.String" resultType="com.jmt.model.uaa.UaaUser">
		SELECT
			`uaaId`,
			`nickname`,
			`loginName`,
			`password`,
			`activated`,
			`email`,
			`sex`,
			`photo`,
			`telPhone`,
			`address`,
			`country`,
			`province`,
			`city`,
			`area`,
			`isRealAuth`,
			`unionId`
		FROM
			`uaa_user`
		WHERE
			`isDelete` =0 AND (`loginName`=#{loginName} OR `telPhone`=#{loginName})
		LIMIT 1
	</select>
</mapper>
