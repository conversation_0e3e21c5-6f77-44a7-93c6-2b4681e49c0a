<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace名字必须与Mapper接口名一致 -->

<mapper namespace="com.jmt.dao.UaaUserWalletDao">

	<insert id="insert" parameterType="com.jmt.model.uaa.UaaUserWallet" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO uaa_user_wallet
			(uaaId, balance, points, createTime, updateTime, isDelete)
		VALUES
			(#{uaaId}, #{balance}, #{points}, #{createTime}, #{updateTime}, #{isDelete})
	</insert>

	<update id="updateById" parameterType="com.jmt.model.uaa.UaaUserWallet">
		UPDATE uaa_user_wallet
		<set>
			<if test="uaaId != null">uaaId = #{uaaId},</if>
			<if test="balance != null">balance = #{balance},</if>
			<if test="points != null">points = #{points},</if>
			<if test="updateTime != null">updateTime = #{updateTime},</if>
			<if test="isDelete != null">isDelete = #{isDelete},</if>
			<if test="payPassword != null">payPassword = #{payPassword},</if>
		</set>
		WHERE id = #{id}
	</update>

	<update id="deleteById">
		UPDATE uaa_user_wallet
		SET isDelete = 1
		WHERE id = #{id}
	</update>

	<select id="selectByUaaId" resultType="com.jmt.model.uaa.UaaUserWallet">
		SELECT id, uaaId, balance, points, createTime, updateTime, isDelete,payPassword
		FROM uaa_user_wallet
		WHERE uaaId = #{id} AND isDelete = 0
		ORDER BY id DESC
		LIMIT 1
	</select>

	<update id="updateByUaaId" parameterType="com.jmt.model.uaa.UaaUserWallet">
		UPDATE uaa_user_wallet
		<set>
			<if test="uaaId != null">uaaId = #{uaaId},</if>
			<if test="balance != null">balance = #{balance},</if>
			<if test="points != null">points = #{points},</if>
			<if test="updateTime != null">updateTime = #{updateTime},</if>
			<if test="isDelete != null">isDelete = #{isDelete},</if>
			<if test="payPassword != null">payPassword = #{payPassword},</if>
		</set>
		WHERE uaaId = #{uaaId}
	</update>


</mapper>
