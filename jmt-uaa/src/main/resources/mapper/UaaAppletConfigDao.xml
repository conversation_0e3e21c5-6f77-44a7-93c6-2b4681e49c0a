<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace名字必须与Mapper接口名一致 -->

<mapper namespace="com.jmt.dao.UaaAppletConfigDao">

	<select id="loadConfigByClientId" parameterType="java.lang.String" resultType="com.jmt.model.uaa.UaaUser">
		SELECT
			`id`,
			`appletName`,
			`clientId`,
			`appId`,
			`appSecret`,
		FROM
			`uaa_applet_config`
		WHERE
			`clientId`=#{clientId}
		LIMIT 1
	</select>
</mapper>