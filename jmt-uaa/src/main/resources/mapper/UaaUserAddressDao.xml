<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace名字必须与Mapper接口名一致 -->

<mapper namespace="com.jmt.dao.UaaUserAddressDao">


	<insert id="insert" parameterType="com.jmt.model.uaa.UaaUserAddress" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO uaa_user_address
		(uaaId, linkName, telPhone, country, province, city, detailAddress, createTime, updateTime, isDelete, isDefault)
		VALUES
			(#{uaaId}, #{linkName}, #{telPhone}, #{country}, #{province}, #{city}, #{detailAddress},
			 #{createTime}, #{updateTime}, #{isDelete},#{isDefault})
	</insert>

	<update id="updateById" parameterType="com.jmt.model.uaa.UaaUserAddress">
		UPDATE uaa_user_address
		<set>
			<if test="uaaId != null">uaaId = #{uaaId},</if>
			<if test="linkName != null">linkName = #{linkName},</if>
			<if test="telPhone != null">telPhone = #{telPhone},</if>
			<if test="country != null">country = #{country},</if>
			<if test="province != null">province = #{province},</if>
			<if test="city != null">city = #{city},</if>
			<if test="detailAddress != null">detailAddress = #{detailAddress},</if>
			<if test="updateTime != null">updateTime = #{updateTime},</if>
			<if test="isDelete != null">isDelete = #{isDelete},</if>
			<if test="isDefault != null">isDefault = #{isDefault},</if>
		</set>
		WHERE id = #{id}
	</update>

	<update id="deleteById" parameterType="java.lang.Long">
		UPDATE uaa_user_address
		SET
		isDelete = 1,
		updateTime = NOW()
		WHERE
		id = #{id}
		AND isDelete = 0
	</update>

	<select id="selectById" resultType="com.jmt.model.uaa.UaaUserAddress">
		SELECT id, uaaId, linkName, telPhone, country, province, city, detailAddress, createTime, updateTime, isDelete,isDefault
		FROM uaa_user_address
		WHERE id = #{id} AND isDelete = 0
	</select>

	<select id="selectByIds" resultType="com.jmt.model.uaa.UaaUserAddress">
		SELECT
		id, uaaId, linkName, telPhone, country, province, city,
		detailAddress, createTime, updateTime, isDelete,isDefault
		FROM uaa_user_address
		WHERE id IN
		<foreach collection="list" item="id" open="(" separator="," close=")">
			#{id,jdbcType=BIGINT}
		</foreach>
		AND isDelete = 0
	</select>

	<select id="selectByUaaId" resultType="com.jmt.model.uaa.UaaUserAddress">
		SELECT id, uaaId, linkName, telPhone, country, province, city, detailAddress, createTime, updateTime, isDelete,isDefault
		FROM uaa_user_address
		WHERE uaaId = #{uaaId} AND isdelete = 0
		ORDER BY createTime DESC
	</select>

	<!-- 查找用户的默认地址 -->
	<select id="findDefaultByUaaId" resultType="com.jmt.model.uaa.UaaUserAddress">
		SELECT id, uaaId, linkName, telPhone, country, province, city, detailAddress, createTime, updateTime, isDefault FROM uaa_user_address
		WHERE uaaId = #{uaaId} AND isDefault = 1 AND isDelete = 0
		LIMIT 1
	</select>

	<!-- 统计用户地址数量 -->
	<select id="countByUaaId" resultType="java.lang.Integer">
		SELECT COUNT(1) FROM uaa_user_address
		WHERE uaaId = #{uaaId} AND isDelete = 0
	</select>

</mapper>